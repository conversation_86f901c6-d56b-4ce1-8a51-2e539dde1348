# Chyrpe Cloud Functions
This is the main repository for Chyrpe's backend services, running on GCP.

## Developer Procedures
### Updating Shared Files
Shared files contain reusable functions that are common to many different modules. To update these functions, edit `shared_master.py` in the root of the directory and then run `copy_shared.py` to copy the changes into each subdirectory.

### Feature Branches
Updates to the codebase should observe the following procedure:
1. Create a new branch from main with a descriptive title for the feature or work being developed
2. Once completed, create a Pull Request to dev with a description of changes to files, new functions, and modified functions
3. If changes pass a code review the branch will be merged to dev & deployed, where the changes will be tested
    1. If bugs or undefined behaviour are found in testing, debug the code on the feature branch and resubmit the Pull Request to dev
4. When we are satisfied that the feature is stable, it will be merged to main & deployed

### Deployment - Firebase Functions
Before first deploying from the repo:
- Set up aliases for project environment using `firebase use --add` - one for production environment and one for development environment
    - Check that your `.firebaserc` file contains the aliases after running
- **IMPORTANT**: when switching to `dev` branch run `firebase use dev` and when switching to main run `firebase use prod` to deploy to the correct environment
- Run `firebase deploy --only functions` with additional flags to reduce compute time by only deploying new/modified functions: see [the CLI docs](https://firebase.google.com/docs/cli/#deploy_specific_functions)

### Deployment - API Gateway
Some endpoints are designed for use in secured third-party apps, which use API Gateway for secure invocation. If only modifying existing endpoints, these steps are not necessary. If adding new endpoints, the following procedure should be followed to deploy these endpoints:
1. Follow standard procedures as defined in Feature Branches to submit the code for review.
2. Add the new endpoint(s) to the paths section of `devgateway.yaml`, following the format used by other endpoints in the file.
3. Once deployed to the dev environment, follow the instructions to update the gateway:
    1. Activate the Cloud Shell
    2. Delete any previous versions of the config file using `rm devgateway.yaml`
    3. Click the three dots, select upload, & upload the new config file
    4. Use the following command to create a new API config: `gcloud api-gateway api-configs create <name of gateway> --api=<name of API> --openapi-spec=<config file> --project=<current project>`
    5. Use the following command to update the gateway to use the created API config: `gcloud api-gateway gateways update <name of gateway> --api=<name of API> --api-config=<config file> --project=<current project> --location=europe-west2`
4. Repeat these steps for `prodgateway.yaml` when deploying to the production environment.

### Deployment - emergency rollback
If a critical bug is found, especially in the production environment, and we suspect that it is caused by the most recent code commit, use the following commands to rollback the change:

```bash
git reset --hard HEAD~1
git push --force
```

And redeploy to main. We can then use the history of the dev branch to determine what went wrong.