# Imports in alphabetical order

from amplitude import Amplitude, BaseEvent, Identify, EventOptions
from datetime import datetime
from firebase_admin import initialize_app
from firebase_functions import options, scheduler_fn
from firebase_functions.firestore_fn import (
  DocumentSnapshot,
)
from google.cloud import firestore
from google.cloud.firestore_v1 import <PERSON><PERSON><PERSON>er
from firebase_admin import firestore
import google.cloud.logging
import logging
from common_translations import get_translated_text


# initializing log client
log_client = google.cloud.logging.Client()
log_client.setup_logging()


# defining common variables
amplitude_client = Amplitude("65dbac83dec4e06df231ca34a83e4757")

# defining common methods in global scope
def sendPushNotification(uid, title, text, push_notifications_collection):
    push_notifications_collection.add({"notification_title": title, "notification_text": text,
                                       "user_refs": f'/users/{uid}',
                                       "timestamp": firestore.SERVER_TIMESTAMP})


### Actual cloud functions

initialize_app()
from datetime import timedelta


# checks if given user has received same-region likes on that day
def hasReceivedSameRegionLikesToday(db, uid, region, end_time) -> bool:
    """Checks whether the user has received relevant likes on a given day
    :param db: Database Instance
    :param uid: User's uid
    :param region: User's region
    :param end_time: Cutoff time for finding likes
    :return: Boolean - whether or not the user has received relevant likes
    """

    try:

        user_reference = db.collection("users").document(uid)

        likes_collection = db.collection('likes')
        start_time = end_time - timedelta(1)

        likes_query = (likes_collection.where(filter=FieldFilter("likedUser", "==", user_reference))
                        .where(filter=FieldFilter("likingUserWlRegion", "==", region))
                        .where(filter=FieldFilter("mutual", "==", False))
                        .where(filter=FieldFilter("unmatched", "==", False))
                       .where(filter=FieldFilter("timestamp", ">", start_time))
                       .where(filter=FieldFilter("timestamp", "<", end_time))).limit(1).get()

        if len(likes_query) > 0:
            return True
        else:
            return False

    except Exception as e:

        logging.exception(e)


# SEND BATCHED EVENTS AMPLITUDE: Once a day, sends liked/disliked events to Amplitude
@scheduler_fn.on_schedule(
    schedule='55 23 * * *',
    timezone=scheduler_fn.Timezone("GMT"),
    region="europe-west1",
    timeout_sec=1800,
    memory=options.MemoryOption.GB_32,
    cpu=8
)
def sendBatchedEventsAmplitude(event: scheduler_fn.ScheduledEvent) -> None:
    """
    Once a day, sends liked/disliked events to Amplitude
    :param event: Due to scheduled trigger of cloud function, no practical relevance
    :return: None
    """
    db = firestore.client()
    user_collection = db.collection("users")

    # Get the current UTC date and time
    yesterday_date = datetime.now()

    # Format the date as "YYYYMMDD"
    formatted_date = yesterday_date.strftime("%Y%m%d")
    print(formatted_date)

    def paginate_query(query, batch_size=1000):
        docs = []
        last_doc = None

        while True:
            query_result = []
            # Apply pagination with limit and start_after if last_doc is set
            if last_doc:
                query_result = query.limit(batch_size).start_after(last_doc).get()
            else:
                query_result = query.limit(batch_size).get()

            # If no more documents are found, break the loop
            if len(query_result)<=0:
                break

            # Append to the docs list and set the last document for next page
            docs.extend(query_result)
            last_doc = query_result[-1]  # Set last_doc to the last document in the batch

        return docs

    # fetches all users who were active in the past day
    def query_or_composite_filter() -> list[DocumentSnapshot]:
        docs_1 = paginate_query(user_collection.where("latestDayDislikesGiven", "==", formatted_date))
        docs_2 = paginate_query(user_collection.where("latestDayDislikesReceived", "==", formatted_date))
        docs_3 = paginate_query(user_collection.where("latestDayLikesGiven", "==", formatted_date))
        docs_4 = paginate_query(user_collection.where("latestDayLikesReceived", "==", formatted_date))
        docs_5 = paginate_query(user_collection.where("latestDayMessaging", "==", formatted_date))

        # Combine all results and remove duplicates
        all_docs = {doc.id: doc for doc in (docs_1 + docs_2 + docs_3 + docs_4 + docs_5)}

        # Return the unique documents as a list
        return list(all_docs.values())

    yesterday_active_users = query_or_composite_filter()
    logging.info(len(yesterday_active_users))

    # sends events for each active user to Amplitude
    for u in yesterday_active_users:

        try:

            u_doc = u.to_dict()
            u_uid = u_doc.get('uid')

            latestDayDislikesGiven = u_doc.get("latestDayDislikesGiven")
            latestDayDislikesReceived = u_doc.get("latestDayDislikesReceived")
            latestDayLikesGiven = u_doc.get("latestDayLikesGiven")
            latestDayLikesReceived = u_doc.get("latestDayLikesReceived")
            latestDayMessaging = u_doc.get("latestDayMessaging")

            if latestDayDislikesGiven == formatted_date:

                dislikesGivenDay = u_doc.get('dislikesGivenDay').get(formatted_date)

                event = BaseEvent(event_type="Matching: Disliked", user_id=u_uid, event_properties={
                    "Disliked on Day Count": dislikesGivenDay
                }, time=int(yesterday_date.timestamp())*1000)
                amplitude_client.track(event)

                logging.info("latestDayDislikesGiven tracked")

            if latestDayDislikesReceived == formatted_date:
                dislikesReceivedDay = u_doc.get('dislikesReceivedDay').get(formatted_date)

                event = BaseEvent(event_type="Matching: Received Dislikes", user_id=u_uid, event_properties={
                    "Received Dislikes On Day Count": dislikesReceivedDay
                }, time=int(yesterday_date.timestamp())*1000)
                amplitude_client.track(event)

                logging.info("latestDayDislikesReceived tracked")

            if latestDayLikesGiven == formatted_date:
                likesGivenDay = u_doc.get('likesGivenDay').get(formatted_date)

                event = BaseEvent(event_type="Matching: Liked", user_id=u_uid, event_properties={
                    "Likes on Day Count": likesGivenDay
                }, time=int(yesterday_date.timestamp())*1000)
                amplitude_client.track(event)

            if latestDayLikesReceived == formatted_date:
                identify_obj = Identify()

                identify_obj.set("Has Been Liked", True)
                amplitude_client.identify(identify_obj, EventOptions(user_id=u_uid))

                likesReceivedDay = u_doc.get('likesReceivedDay').get(formatted_date)

                event = BaseEvent(event_type="Matching: Received Likes", user_id=u_uid, event_properties={
                    "Received Likes On Day Count": likesReceivedDay
                }, time=int(yesterday_date.timestamp())*1000)
                amplitude_client.track(event)

                user_notification_cohort = u_doc.get("notificationTestingCohort")
                user_gender = u_doc.get("gender")
                user_name = u_doc.get("name")

                if user_gender == "Female":
                    push_notifications_collection = db.collection("ff_user_push_notifications")

                    try:

                        # before a woman is sent a notification, we first have to determine if at least one like
                        # she received in fulfilled in-app visibility condition: same region as her

                        should_send = hasReceivedSameRegionLikesToday(db, u_uid, u_doc.get("wlRegion"), yesterday_date)

                        if should_send:

                            sendPushNotification(u_uid, "Chyrpe",
                                                     f"{get_translated_text("Hey", u_doc.get("language", "en"))} {user_name}, {get_translated_text("you’ve received new likes today!", u_doc.get("language", "en"))}", push_notifications_collection)

                            event = BaseEvent(event_type="Notifications: Sent Received Likes Daily W Notification NoEmoji",
                                                  user_id=u_uid, time=int(yesterday_date.timestamp())*1000)
                            amplitude_client.track(event)

                            event = BaseEvent(event_type="Notifications: Sent Received Likes Daily W Notification Notification", user_id=u_uid,
                                              event_properties={"Notification Testing Cohort": user_notification_cohort}, time=int(yesterday_date.timestamp())*1000)
                            amplitude_client.track(event)

                    except Exception as e:
                        logging.exception(e)

                logging.info("latestDayLikesGiven tracked")

            if latestDayMessaging == formatted_date:
                messageTodayPerMatchSent = u_doc.get('messageTodayPerMatchSent')

                for m in messageTodayPerMatchSent:
                    event = BaseEvent(event_type="Chat: Sent Messages", user_id=u_uid, event_properties={
                        "Match ID": m,
                        "Messages Count": messageTodayPerMatchSent[m]['count'],
                        "Other Gender Matched": messageTodayPerMatchSent[m]['otherGender'],
                        "Own Gender Matched": u_doc.get("gender")
                    }, time=int(yesterday_date.timestamp())*1000)
                    amplitude_client.track(event)

                u.reference.update({"messageTodayPerMatchSent": {}})

                logging.info("messageTodayPerMatchSent tracked")

        except Exception as e:
            logging.error(e)
            continue
