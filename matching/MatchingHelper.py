from dataclasses import dataclass, field, asdict
from typing import List, Optional, Dict, Any
import sqlalchemy
import random
import logging
import phonenumbers
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

def log_retry_attempt(retry_state):
    """Logs retry attempts before each retry."""
    logging.warning(f"Retrying... Attempt {retry_state.attempt_number}")

def with_retry(max_attempts=6, min_wait=2, max_wait=20, exception_types=(Exception)):
    """Decorator to add retry logic with exponential backoff."""
    return retry(
        retry=retry_if_exception_type(exception_types),
        wait=wait_exponential(multiplier=1, min=min_wait, max=max_wait),  # Initial wait 2s, doubles each time, max 10s
        stop=stop_after_attempt(max_attempts),  # Max 5 retry attempts
        before=log_retry_attempt,
        reraise=True
    )

def get_compatible_roles(user_role):
    if user_role == 'Dominant':
        return ['Submissive', 'Switch']
    elif user_role == 'submissive':
        return ['Dominant', 'Switch']
    elif user_role == 'Switch':
        return ['Dominant', 'Submissive', 'Switch']
    else:
        return ['Dominant', 'Submissive', 'Switch']  # or raise an exception if role is invalid


@dataclass
class UserPreferences:
    gender: Optional[str] = None
    age_range: Optional[Dict[str, int]] = field(default_factory=dict)
    relationship_type: Optional[List[str]] = field(default_factory=list)
    matching_mode: Optional[str] = "Global"
    distance: Optional[float] = 500.0
    role_matching: bool = False
    role_req: Optional[List[str]] = None
    hide_findoms: bool = False
    findom_preferred: Optional[bool] = False
    likes_bypass_filters: bool = False
    height_range: Optional[Dict[str, float]] = field(default_factory=dict)
    ethnicity: Optional[List[str]] = field(default_factory=list)
    religion: Optional[List[str]] = field(default_factory=list)
    education_level: Optional[List[str]] = field(default_factory=list)
    children: Optional[List[str]] = field(default_factory=list)
    family_plans: Optional[List[str]] = field(default_factory=list)
    politics: Optional[List[str]] = field(default_factory=list)
    drinking: Optional[List[str]] = field(default_factory=list)
    smoking: Optional[List[str]] = field(default_factory=list)
    dealbreakers: Dict[str, bool] = field(default_factory=dict)

    @staticmethod
    def from_user_doc(user_doc: Dict[str, Any]) -> "UserPreferences":
        def parse_range(lower_key: str, upper_key: str) -> Dict[str, int]:
            lower = user_doc.get(lower_key, None)
            upper = user_doc.get(upper_key, None)
            return {"lower": lower, "upper": upper}

        def parse_list(field: str) -> List[str]:
            # Handle potential mixed naming conventions
            key_snake_case = f"{field}_Req"
            key_camel_case = f"{field}Req"
            return user_doc.get(key_snake_case) or user_doc.get(key_camel_case, [])

        def parse_dealbreaker(field: str) -> bool:
            key_snake_case = f"{field}_Dealbreaker"
            key_camel_case = f"{field}Dealbreaker"
            if (parse_list(field) == [] or parse_list(field) == ["Open to all"] or parse_list(field) == "Open to all") and not (field in ["age", "height"]):
                return False
            return user_doc.get(key_snake_case) or user_doc.get(key_camel_case, False)

        def parse_role_req() -> List[str]:
            user_role_req = user_doc.get("roleReq", None)
            user_role_matching = user_doc.get("roleMatching", False),
            if user_role_req is None:
                if user_role_matching:
                    return get_compatible_roles(user_doc.get("position", "Switch"))
                else:
                    return ['Dominant', 'Submissive', 'Switch']
            else:
                if user_role_req == ['Open to all']:
                    return ['Dominant', 'Submissive', 'Switch']

                return user_role_req


        return UserPreferences(
            gender=user_doc.get("genderReq"),
            age_range=parse_range("lowerAgeReq", "upperAgeReq"),
            relationship_type=parse_list("dating_intentions"),
            matching_mode="Local" if user_doc.get("localMatching") else "Global",
            distance=user_doc.get("distanceReq", 500.0),
            role_matching=user_doc.get("roleMatching", False),
            role_req=parse_role_req(),
            hide_findoms=user_doc.get("professionalsReq", False),
            findom_preferred=user_doc.get('findomPreferred', False),
            likes_bypass_filters=user_doc.get("matchBypassReq", False),
            height_range=parse_range("lowerHeightReq", "upperHeightReq"),
            ethnicity=parse_list("ethnicity"),
            religion=parse_list("religion"),
            education_level=parse_list("educationLevel"),
            children=parse_list("children"),
            family_plans=parse_list("familyPlans"),
            politics=parse_list("politics"),
            drinking=parse_list("drinking"),
            smoking=parse_list("smoking"),
            dealbreakers={
                "gender": parse_dealbreaker("gender"),
                "age": parse_dealbreaker("age"),
                "relationship_type": parse_dealbreaker("dating_intentions"),
                "distance": parse_dealbreaker("distance"),
                "role_matching": parse_dealbreaker("roleMatching"),
                "hide_findoms": parse_dealbreaker("professionals"),
                "likes_bypass_filters": parse_dealbreaker("matchBypass"),
                "height": parse_dealbreaker("height"),
                "ethnicity": parse_dealbreaker("ethnicity"),
                "religion": parse_dealbreaker("religion"),
                "education_level": parse_dealbreaker("educationLevel"),
                "children": parse_dealbreaker("children"),
                "family_plans": parse_dealbreaker("familyPlans"),
                "politics": parse_dealbreaker("politics"),
                "drinking": parse_dealbreaker("drinking"),
                "smoking": parse_dealbreaker("smoking")
            }
        )

ENUM_DEFINITIONS = {
    "relationship_type": [
        "Long-term relationship", "Short-term relationship", "One night stand",
        "Play sessions", "Platonic relationship", "Virtual relationship"
    ],
    "ethnicity": [
        "Black/African Descent", "East Asian", "Hispanic/Latino", "Middle Eastern",
        "Native American", "Pacific Islander", "South Asian", "Southeast Asian",
        "White/Caucasian", "Other", "Prefer not to say"
    ],
    "religion": [
        "Agnostic", "Atheist", "Buddhist", "Catholic", "Christian", "Hindu",
        "Jewish", "Muslim", "Sikh", "Spiritual", "Other", "Prefer not to say"
    ],
    "edu_level": ["High School", "Bachelor", "Master or Doctorate", "Prefer not to say"],
    "children": ["Don’t have children", "Have children", "Prefer not to say"],
    "family_plan": [
        "Don’t want children", "Want children", "Open to children",
        "Not sure", "Prefer not to say"
    ],
    "politics": ["Left", "Center", "Right", "Not Political", "Other", "Prefer not to say"],
    "drinking": ["Yes", "Occasionally", "No", "Prefer not to say"],
    "smoking": ["Yes", "Occasionally", "No", "Prefer not to say"]
}

PARAMETER_MAPPING_REQUIREMENT = {
    "relationship_type": "relationship_type",
    "ethnicity": "ethnicity",
    "religion": "religion",
    "education_level": "edu_level",
    "children": "children",
    "family_plans": "family_plan",
    "politics": "politics",
    "drinking": "drinking",
    "smoking": "smoking",
}


def create_one_multi_hot_vector(data):
    """
    Creates a one/multi-hot encoding vector based on the input data.

    Parameters:
        data (dict): A dictionary containing multi-hot arrays and single-hot values.
                     Example:
                     {
                         "RelationshipType": ["Short-term relationship", "One night stand"],
                         "Ethnicity": ["Hispanic/Latino"],
                         "Religion": None,  # None value
                         "EduLevel": "Bachelor",
                         "Children": None,  # None value
                         "FamilyPlan": "Want children",
                         "Politics": "Left",
                         "Drinking": "Yes",
                         "Smoking": "No"
                     }

    Returns:
        np.ndarray: A one-dimensional array representing the chained one/multi-hot encoding.
    """
    encoding = []

    for key, values in data.items():
        enum_values = ENUM_DEFINITIONS.get(key)
        if enum_values == None:
            continue
        vector = [0] * (len(enum_values) + 1)  # +1 for "missing data" position

        if values is None:
            # Set the "missing data" position to 1
            vector[-1] = 1
        elif isinstance(values, list):  # Multi-hot encoding
            for value in values:
                if value in enum_values:
                    vector[enum_values.index(value)] = 1
        else:  # Single-hot encoding
            if values in enum_values:
                vector[enum_values.index(values)] = 1

        encoding.extend(vector)

    return f'[{",".join(map(str, encoding))}]'

def create_one_multi_hot_vector_requirements(data: UserPreferences):
    """
    Creates a one/multi-hot encoding vector based on the input data.

    Parameters:
        data (dict): A dictionary containing multi-hot arrays and single-hot values.
                     Example:
                     {
                         "RelationshipType": ["Short-term relationship", "One night stand"],
                         "Ethnicity": ["Hispanic/Latino"],
                         "Religion": None,  # None value
                         "EduLevel": "Bachelor",
                         "Children": None,  # None value
                         "FamilyPlan": "Want children",
                         "Politics": "Left",
                         "Drinking": "Yes",
                         "Smoking": "No"
                     }

    Returns:
        np.ndarray: A one-dimensional array representing the chained one/multi-hot encoding.
    """
    encoding = []
    output_enums = {}

    for key in PARAMETER_MAPPING_REQUIREMENT:
        value = asdict(data).get(key)
        second_attribute = PARAMETER_MAPPING_REQUIREMENT[key]

        output_enums[second_attribute] = value

    for key, values in output_enums.items():
        enum_values = ENUM_DEFINITIONS.get(key)
        if enum_values == None:
            continue
        vector = [0] * (len(enum_values) + 1)  # +1 for "missing data" position

        if values is None:
            # Set the "missing data" position to 1
            vector[-1] = 1
        elif isinstance(values, list):  # Multi-hot encoding
            for value in values:
                if value in enum_values:
                    vector[enum_values.index(value)] = 1
        else:  # Single-hot encoding
            if values in enum_values:
                vector[enum_values.index(values)] = 1

        encoding.extend(vector)

    if not(1 in encoding):
        for i, j in enumerate(encoding):
            encoding[i] = 1


    return f'[{",".join(map(str, encoding))}]'

@with_retry()
def call_suggest_users_function(
        pool,
        user_id: str,
        highest_elo_limit: int,
        lowest_elo_limit: int,
        similar_elo_limit: int,
        new_people_limit: int,
        most_active_limit: int,
        geographically_close_limit: int,
        liked_by_limit: int,
        highest_elo_weight: float,
        lowest_elo_weight: float,
        similar_elo_weight: float,
        new_people_weight: float,
        most_active_weight: float,
        geographically_close_weight: float,
        liked_by_weight: float,
        vector_req: str,
        vector_threshold: float,
        include_role: bool,
        exclude_professional: bool,
        role_req=None,
        relationship_type_req=None,
        min_age=None,
        max_age=None,
        max_distance=None,
        min_height=None,
        max_height=None,
        ethnicity_req=None,
        religions_req=None,
        edu_req=None,
        children_req=None,
        family_req=None,
        politics_req=None,
        drinking_req=None,
        smoking_req=None,
        findom_preferred=False
):
    logging.getLogger('sqlalchemy.engine').setLevel(logging.DEBUG)

    # Build the query
    stmt = sqlalchemy.text("""
        SELECT * FROM suggest_users_weighted_advanced_returnonly(
            p_user_id => :p_user_id,
            highest_elo_limit => :highest_elo_limit,
            similar_elo_limit => :similar_elo_limit,
            new_people_limit => :new_people_limit,
            most_active_limit => :most_active_limit,
            geographically_close_limit => :geographically_close_limit,
            liked_by_limit => :liked_by_limit,
            highest_elo_weight => :highest_elo_weight,
            similar_elo_weight => :similar_elo_weight,
            new_people_weight => :new_people_weight,
            most_active_weight => :most_active_weight,
            geographically_close_weight => :geographically_close_weight,
            liked_by_weight => :liked_by_weight,
            vector_req => :vector_req,
            vector_threshold => :vector_threshold,
            include_role => :include_role,
            role_req => :role_req,
            exclude_professional => :exclude_professional,
            relationship_type_req => :relationship_type_req,
            min_age => :min_age,
            max_age => :max_age,
            max_distance => :max_distance,
            min_height => :min_height,
            max_height => :max_height,
            ethnicity_req => :ethnicity_req,
            religions_req => :religions_req,
            edu_req => :edu_req,
            children_req => :children_req,
            family_req => :family_req,
            politics_req => :politics_req,
            drinking_req => :drinking_req,
            smoking_req => :smoking_req,
            findom_preferred => :findom_preferred
        );
    """)

    parameters = {
            "p_user_id": user_id,
            "highest_elo_limit": highest_elo_limit,
            "lowest_elo_limit": lowest_elo_limit,
            "similar_elo_limit": similar_elo_limit,
            "new_people_limit": new_people_limit,
            "most_active_limit": most_active_limit,
            "geographically_close_limit": geographically_close_limit,
            "liked_by_limit": liked_by_limit,
            "highest_elo_weight": highest_elo_weight,
            "lowest_elo_weight": lowest_elo_weight,
            "similar_elo_weight": similar_elo_weight,
            "new_people_weight": new_people_weight,
            "most_active_weight": most_active_weight,
            "geographically_close_weight": geographically_close_weight,
            "liked_by_weight": liked_by_weight,
            "vector_req": vector_req,
            "vector_threshold": vector_threshold,
            "include_role": include_role,
            "role_req": role_req,
            "exclude_professional": exclude_professional,
            "relationship_type_req": relationship_type_req,
            "min_age": min_age,
            "max_age": max_age,
            "max_distance": max_distance,
            "min_height": min_height,
            "max_height": max_height,
            "ethnicity_req": ethnicity_req,
            "religions_req": religions_req,
            "edu_req": edu_req,
            "children_req": children_req,
            "family_req": family_req,
            "politics_req": politics_req,
            "drinking_req": drinking_req,
            "smoking_req": smoking_req,
            "findom_preferred": findom_preferred
        }

    with pool.connect() as db_conn:
        result = db_conn.execute(stmt, parameters).fetchall()

        db_conn.commit()

        return result

@with_retry()
def call_suggest_old_users_function(
        pool,
        user_id: str,
        highest_elo_limit: int,
        lowest_elo_limit: int,
        similar_elo_limit: int,
        new_people_limit: int,
        most_active_limit: int,
        geographically_close_limit: int,
        liked_by_limit: int,
        highest_elo_weight: float,
        lowest_elo_weight: float,
        similar_elo_weight: float,
        new_people_weight: float,
        most_active_weight: float,
        geographically_close_weight: float,
        liked_by_weight: float,
        vector_req: str,
        vector_threshold: float,
        include_role: bool,
        exclude_professional: bool,
        role_req=None,
        relationship_type_req=None,
        min_age=None,
        max_age=None,
        max_distance=None,
        min_height=None,
        max_height=None,
        ethnicity_req=None,
        religions_req=None,
        edu_req=None,
        children_req=None,
        family_req=None,
        politics_req=None,
        drinking_req=None,
        smoking_req=None,
        findom_preferred=False,
):
    logging.getLogger('sqlalchemy.engine').setLevel(logging.DEBUG)

    # Build the query
    stmt = sqlalchemy.text("""
        SELECT * FROM suggest_old_users_weighted_advanced_returnonly(
            p_user_id => :p_user_id,
            highest_elo_limit => :highest_elo_limit,
            similar_elo_limit => :similar_elo_limit,
            new_people_limit => :new_people_limit,
            most_active_limit => :most_active_limit,
            geographically_close_limit => :geographically_close_limit,
            liked_by_limit => :liked_by_limit,
            highest_elo_weight => :highest_elo_weight,
            similar_elo_weight => :similar_elo_weight,
            new_people_weight => :new_people_weight,
            most_active_weight => :most_active_weight,
            geographically_close_weight => :geographically_close_weight,
            liked_by_weight => :liked_by_weight,
            vector_req => :vector_req,
            vector_threshold => :vector_threshold,
            include_role => :include_role,
            role_req => :role_req,
            exclude_professional => :exclude_professional,
            relationship_type_req => :relationship_type_req,
            min_age => :min_age,
            max_age => :max_age,
            max_distance => :max_distance,
            min_height => :min_height,
            max_height => :max_height,
            ethnicity_req => :ethnicity_req,
            religions_req => :religions_req,
            edu_req => :edu_req,
            children_req => :children_req,
            family_req => :family_req,
            politics_req => :politics_req,
            drinking_req => :drinking_req,
            smoking_req => :smoking_req,
            findom_preferred => :findom_preferred
        );
    """)

    parameters = {
            "p_user_id": user_id,
            "highest_elo_limit": highest_elo_limit,
            "lowest_elo_limit": lowest_elo_limit,
            "similar_elo_limit": similar_elo_limit,
            "new_people_limit": new_people_limit,
            "most_active_limit": most_active_limit,
            "geographically_close_limit": geographically_close_limit,
            "liked_by_limit": liked_by_limit,
            "highest_elo_weight": highest_elo_weight,
            "lowest_elo_weight": lowest_elo_weight,
            "similar_elo_weight": similar_elo_weight,
            "new_people_weight": new_people_weight,
            "most_active_weight": most_active_weight,
            "geographically_close_weight": geographically_close_weight,
            "liked_by_weight": liked_by_weight,
            "vector_req": vector_req,
            "vector_threshold": vector_threshold,
            "include_role": include_role,
            "role_req": role_req,
            "exclude_professional": exclude_professional,
            "relationship_type_req": relationship_type_req,
            "min_age": min_age,
            "max_age": max_age,
            "max_distance": max_distance,
            "min_height": min_height,
            "max_height": max_height,
            "ethnicity_req": ethnicity_req,
            "religions_req": religions_req,
            "edu_req": edu_req,
            "children_req": children_req,
            "family_req": family_req,
            "politics_req": politics_req,
            "drinking_req": drinking_req,
            "smoking_req": smoking_req,
            "findom_preferred": findom_preferred
        }

    with pool.connect() as db_conn:
        result = db_conn.execute(stmt, parameters).fetchall()

        db_conn.commit()

        return result


def call_suggest_liked_you_function(
        pool,
        user_id: str):
    # Build the query
    stmt = sqlalchemy.text("""
        SELECT * FROM suggest_user_liked_you(:user_id, :like_limit);
    """)

    parameters = {
            "user_id": user_id,
            "like_limit": 3
            }

    with pool.connect() as db_conn:
        result = db_conn.execute(stmt, parameters).fetchall()

        db_conn.commit()

        return result


def shuffle_uids(suggestion_uids: list[str]) -> list[str]:
    """
    Shuffles UIDs so that results are not deterministic/the same for two users
    """
    if len(suggestion_uids) < 4:
        # if there are less than 4 shuffle them all in the same batch
        return suggestion_uids
    one_quarter = round(len(suggestion_uids) / 4)
    first_quarter_profiles = suggestion_uids[:one_quarter]
    remainder_profiles = suggestion_uids[one_quarter:]
    random.shuffle(first_quarter_profiles)
    random.shuffle(remainder_profiles)
    return first_quarter_profiles + remainder_profiles


def is_phone_blocked(phone_list, phone_number):
    if phone_number != None:
        for phone in phone_list:
            match = phonenumbers.is_number_match(phone_number, phone)
            if match == 4 or match == 3:
                return True
    return False

def filter_blocked_profiles(uid: str, user_phone: str, profiles_to_filter, blocked_profiles_f, blocked_phone_numbers_f):
    """
    Filter profiles by blocked users, blocked numbers
    """
    for profile in profiles_to_filter[:]:
        p_uid = profile.get("uid")
        p_phone = profile.get("phone_number")
        if p_uid in blocked_profiles_f or is_phone_blocked(blocked_phone_numbers_f, p_phone):
            profiles_to_filter.remove(profile)
        else:
            # check if this profile has blocked the calling user
            p_blocked_profiles = profile.get("blockedProfiles") or []
            p_blocked_uids = [bp.get('uid') for bp in p_blocked_profiles]
            p_blocked_numbers = profile.get("blockedContactNumberOnly") or []
            if uid in p_blocked_uids or is_phone_blocked(p_blocked_numbers, user_phone):
                profiles_to_filter.remove(profile)
    return profiles_to_filter

def get_profiles_from_uids(db, suggestion_uids: list[str]) -> list[dict]:
    """
    Given a list of UIDs, look up the full user profile
    """
    user_profiles = []
    for ps_uid in set(suggestion_uids):
        ps_user_doc = db.collection('users').document(ps_uid).get()
        if ps_user_doc.exists:
            ps_user_data = ps_user_doc.to_dict()
            ps_user_publicProfile = ps_user_data.get("publicProfile")
            if ps_user_publicProfile is not None and not ps_user_data.get('paused') and ps_user_data.get('verified'):
                user_profiles.append(ps_user_data)
    return user_profiles

auto_expansion_factors = {
        0: {"age": 0, "distance": 1, "threshold": 0.5},
        1: {"age": 0, "distance": 1.2, "threshold": 1.5},
        2: {"age": 5, "distance": 5, "threshold": 0.8},
        3: {"age": 10, "distance": 50, "threshold": 2},
        4: {"age": 50, "distance": 100, "threshold": 10}
    }

params_dict = {
        'local': {
            'lt40': {
                'highest_elo_limit': 25, 'lowest_elo_limit': 0, 'similar_elo_limit': 0,
                'new_people_limit': 0,
                'most_active_limit': 0,
                'geographically_close_limit': 15, 'liked_by_limit': 5, 'highest_elo_weight': 0.8,
                'lowest_elo_weight': 0, 'similar_elo_weight': 0.0, 'new_people_weight': 0.0, 'most_active_weight': 0.0,
                'geographically_close_weight': 0.1, 'liked_by_weight': 0.1
            },
            'lt60': {
                'highest_elo_limit': 25, 'lowest_elo_limit': 0, 'similar_elo_limit': 25,
                'new_people_limit': 25,
                'most_active_limit': 20,
                'geographically_close_limit': 35, 'liked_by_limit': 5, 'highest_elo_weight': 0.1,
                'lowest_elo_weight': 0, 'similar_elo_weight': 0.1, 'new_people_weight': 0.1, 'most_active_weight': 0.1,
                'geographically_close_weight': 0.3, 'liked_by_weight': 0.2
            },
            'gte60': {
                'highest_elo_limit': 23, 'lowest_elo_limit': 0, 'similar_elo_limit': 10,
                'new_people_limit': 23,
                'most_active_limit': 20,
                'geographically_close_limit': 35, 'liked_by_limit': 5, 'highest_elo_weight': 0.1,
                'lowest_elo_weight': 0, 'similar_elo_weight': 0.2, 'new_people_weight': 0.1, 'most_active_weight': 0.1,
                'geographically_close_weight': 0.2, 'liked_by_weight': 0.2
            }
        },
        'global': {
            'lt40': {
                'highest_elo_limit': 25, 'lowest_elo_limit': 0, 'similar_elo_limit': 0,
                'new_people_limit': 0,
                'most_active_limit': 0,
                'geographically_close_limit': 15, 'liked_by_limit': 5, 'highest_elo_weight': 0.8,
                'lowest_elo_weight': 0, 'similar_elo_weight': 0, 'new_people_weight': 0.0, 'most_active_weight': 0,
                'geographically_close_weight': 0.2, 'liked_by_weight': 0.0
            },
            'lt60': {
                'highest_elo_limit': 20, 'lowest_elo_limit': 0, 'similar_elo_limit': 15,
                'new_people_limit': 25,
                'most_active_limit': 20,
                'geographically_close_limit': 10, 'liked_by_limit': 5, 'highest_elo_weight': 0.3,
                'lowest_elo_weight': 0, 'similar_elo_weight': 0.2, 'new_people_weight': 0.1, 'most_active_weight': 0,
                'geographically_close_weight': 0.2, 'liked_by_weight': 0.2
            },
            'gte60': {
                'highest_elo_limit': 30, 'lowest_elo_limit': 0, 'similar_elo_limit': 20,
                'new_people_limit': 7,
                'most_active_limit': 20,
                'geographically_close_limit': 15, 'liked_by_limit': 5, 'highest_elo_weight': 0.05,
                'lowest_elo_weight': 0, 'similar_elo_weight': 0.4, 'new_people_weight': 0.1,
                'most_active_weight': 0.1,
                'geographically_close_weight': 0.15, 'liked_by_weight': 0.2
            }
        }}
