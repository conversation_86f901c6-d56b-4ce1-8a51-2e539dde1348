# Imports in alphabetical order
import copy
import json
import logging
import phonenumbers
import random
import re
import requests
import sqlalchemy
import time
from typing import Any, Optional

from amplitude import Amplitude, BaseEvent, Identify, EventOptions
from common_like import get_like_filter_data
from common_translations import get_translated_text
from common_utils import is_dev_environment
from datetime import datetime, timedelta, timezone
from firebase_admin import initialize_app
from firebase_admin import firestore
from firebase_functions import https_fn, identity_fn, options, scheduler_fn
from firebase_functions.firestore_fn import (
  on_document_created,
  on_document_deleted,
  on_document_updated,
  on_document_written,
  Event,
  Change,
  DocumentSnapshot,
)
from google.cloud import secretmanager
from google.cloud.firestore_v1 import FieldFilter, Or
import google.cloud.logging
from google.cloud.sql.connector import Connector, IPTypes
import MatchingHelper as mh
import pg8000
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type


# initializing log client
log_client = google.cloud.logging.Client()
log_client.setup_logging()

# defining common methods in global scope
def access_secret_version(secret_name):
    '''Access a secret from the project's secret manager'''
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/greenrocks-9abc3/secrets/{secret_name}/versions/latest"
    response = client.access_secret_version(name=name)
    return response.payload.data.decode('UTF-8')

def connect_with_connector(connector:Connector) -> sqlalchemy.engine.base.Engine:

    """
    Initializes a connection pool for a Cloud SQL instance of Postgres.

    Uses the Cloud SQL Python Connector package.
    """
    def getconn() -> pg8000.dbapi.Connection:
        conn: pg8000.dbapi.Connection = connector.connect(
            "greenrocks-9abc3:europe-west2:chyrpe-prod1",
            "pg8000",
            user="<EMAIL>",
            db="chyrpe_main_db",
            enable_iam_auth=True,
        )
        return conn

    # The Cloud SQL Python Connector can be used with SQLAlchemy
    # using the 'creator' argument to 'create_engine'
    pool = sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        pool_size=2,
        max_overflow=2,
        # ...
    )
    return pool


def connect_with_connector_replica(connector:Connector) -> sqlalchemy.engine.base.Engine:

    """
    Initializes a connection pool for a Cloud SQL instance of Postgres.

    Uses the Cloud SQL Python Connector package.
    """

    instance_name = random.choice(["greenrocks-9abc3:europe-west1:chyrpe-prod1-replica", "greenrocks-9abc3:europe-west4:chyrpe-prod1-replica-2"])

    def getconn() -> pg8000.dbapi.Connection:
        conn: pg8000.dbapi.Connection = connector.connect(
            instance_name,
            "pg8000",
            user="<EMAIL>",
            db="chyrpe_main_db",
            enable_iam_auth=True,
        )
        return conn

    # The Cloud SQL Python Connector can be used with SQLAlchemy
    # using the 'creator' argument to 'create_engine'
    pool = sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        pool_size=2,
        max_overflow=2,
        # ...
    )
    return pool

db_conn_new = None

db_conn_new_replica = None


def connect_with_connector_persist():
    # initialize Cloud SQL Python Connector object
    connector = Connector(refresh_strategy="LAZY")

    def getconn() -> pg8000.dbapi.Connection:
        conn: pg8000.dbapi.Connection = connector.connect(
            "greenrocks-9abc3:europe-west2:chyrpe-prod1",
            "pg8000",
            user="<EMAIL>",
            db="chyrpe_main_db",
            enable_iam_auth=True,
            ip_type=IPTypes.PUBLIC
        )
        return conn

    # The Cloud SQL Python Connector can be used with SQLAlchemy
    # using the 'creator' argument to 'create_engine'
    pool = sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        # [START_EXCLUDE]
        # Pool size is the maximum number of permanent connections to keep.
        pool_size=10,
        # Temporarily exceeds the set pool_size if no connections are available.
        max_overflow=2,
        # The total number of concurrent connections for your application will be
        # a total of pool_size and max_overflow.
        # 'pool_timeout' is the maximum number of seconds to wait when retrieving a
        # new connection from the pool. After the specified amount of time, an
        # exception will be thrown.
        pool_timeout=30,  # 30 seconds
        # 'pool_recycle' is the maximum number of seconds a connection can persist.
        # Connections that live longer than the specified amount of time will be
        # re-established
        pool_recycle=1800,  # 30 minutes
        # [END_EXCLUDE]
    )
    return pool

def connect_with_connector_replica_persist(instance_name):

    """
    Initializes a connection pool for a Cloud SQL instance of Postgres.

    Uses the Cloud SQL Python Connector package.
    """

    connector = Connector(refresh_strategy="LAZY")

    def getconn() -> pg8000.dbapi.Connection:
        conn: pg8000.dbapi.Connection = connector.connect(
            instance_name,
            "pg8000",
            user="<EMAIL>",
            db="chyrpe_main_db",
            enable_iam_auth=True,
            ip_type=IPTypes.PUBLIC
        )
        return conn

    # The Cloud SQL Python Connector can be used with SQLAlchemy
    # using the 'creator' argument to 'create_engine'
    pool = sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        # [START_EXCLUDE]
        # Pool size is the maximum number of permanent connections to keep.
        pool_size=5,
        # Temporarily exceeds the set pool_size if no connections are available.
        max_overflow=2,
        # The total number of concurrent connections for your application will be
        # a total of pool_size and max_overflow.
        # 'pool_timeout' is the maximum number of seconds to wait when retrieving a
        # new connection from the pool. After the specified amount of time, an
        # exception will be thrown.
        pool_timeout=30,  # 30 seconds
        # 'pool_recycle' is the maximum number of seconds a connection can persist.
        # Connections that live longer than the specified amount of time will be
        # re-established
        pool_recycle=1800,  # 30 minutes
        # [END_EXCLUDE]
    )
    return pool



def find_all_likes_of_two_users(user1_id: str, user2_id: str, likes_collection) -> list[DocumentSnapshot]:
    '''Returns all likes documents that include 2 given users by id'''
    filter_1 = FieldFilter("involedUIDs", "==", [user1_id, user2_id])
    filter_2 = FieldFilter("involedUIDs", "==", [user2_id, user1_id])

    # Create the union filter of the two filters (queries)
    or_filter = Or(filters=[filter_1, filter_2])

    # Execute the query
    docs = likes_collection.where(filter=or_filter).get()

    return docs


def sendPushNotification(uid, title, text, push_notifications_collection):
    push_notifications_collection.add({"notification_title": title, "notification_text": text,
                                       "user_refs": f'/users/{uid}',
                                       "timestamp": firestore.SERVER_TIMESTAMP})

def sendPushNotificationWithPayload(uid, title, text, push_notifications_collection, payload, initial_page_name):
    import json

    push_notifications_collection.add({"notification_title": title, "notification_text": text,
                                       "user_refs": f'/users/{uid}',
                                       "timestamp": firestore.SERVER_TIMESTAMP,
                                       "parameter_data": json.dumps(payload),
                                       "initial_page_name": initial_page_name})

def addInAppNotification(sender, receivers, match, type, message, notifications_collection):
    notifications_collection.add({
        "message": message,
        "time": firestore.SERVER_TIMESTAMP,
        "type": type,
        "profileLink": match,
        "sender": sender,
        "senderUID": sender.id,
        "receivers": receivers,
        "receiverUID": receivers[0].id
    })

# defining common variables
amplitude_client = Amplitude("65dbac83dec4e06df231ca34a83e4757")

def log_retry_attempt(retry_state):
    """Logs retry attempts before each retry."""
    logging.warning(f"Retrying... Attempt {retry_state.attempt_number}")

def with_retry(max_attempts=50, min_wait=2, max_wait=15, exception_types=(Exception)):
    """Decorator to add retry logic with exponential backoff."""
    return retry(
        retry=retry_if_exception_type(exception_types),
        wait=wait_exponential(multiplier=1, min=min_wait, max=max_wait),  # Initial wait 2s, doubles each time, max 10s
        stop=stop_after_attempt(max_attempts),  # Max 5 retry attempts
        before=log_retry_attempt,
        reraise=True
    )


### Actual cloud functions

initialize_app()


# BENCHMARK DB: Runs a regular benchmark function on Postgres for monitoring purposes
@scheduler_fn.on_schedule(
    schedule='*/30 * * * *',
    timezone=scheduler_fn.Timezone("GMT"),
    region="europe-west1",
)
def benchmarkDb(event: scheduler_fn.ScheduledEvent) -> None:

    '''
    Runs a regular benchmark function on Postgres for monitoring purposes

    Based on an extended matching query for Anna
    '''


    db = firestore.client()

    # This is Anna's uid
    uid = "GpuBXwlF9QN6lC48ZCfKKfyjF9p1"

    @with_retry()
    def benchmark_sql_with_retry():
        connector = Connector(refresh_strategy="lazy")
        pool = connect_with_connector(connector)
        with pool.connect() as db_conn:
            # query database for suggest users with explain analyze
            stmt = sqlalchemy.text(
                """explain analyze SELECT suggest_users_weighted_returnonly(p_user_id => :p_user_id, highest_elo_limit => :highest_elo_limit, lowest_elo_limit => :lowest_elo_limit, 
                        similar_elo_limit => :similar_elo_limit, new_people_limit => :new_people_limit, most_active_limit => :most_active_limit, 
                        geographically_close_limit => :geographically_close_limit, liked_by_limit => :liked_by_limit, highest_elo_weight => :highest_elo_weight, 
                        lowest_elo_weight => :lowest_elo_weight, similar_elo_weight => :similar_elo_weight, new_people_weight => :new_people_weight, most_active_weight => :most_active_weight, 
                        geographically_close_weight => :geographically_close_weight, liked_by_weight => :liked_by_weight, include_role => :include_role)""")

            result = db_conn.execute(stmt, {
                'p_user_id': uid,
                'highest_elo_limit': 100, 'lowest_elo_limit': 0, 'similar_elo_limit': 10,
                'new_people_limit': 30,
                'most_active_limit': 30,
                'geographically_close_limit': 50, 'liked_by_limit': 50, 'highest_elo_weight': 1,
                'lowest_elo_weight': 0, 'similar_elo_weight': 0.1, 'new_people_weight': 0.3, 'most_active_weight': 0.3,
                'geographically_close_weight': 0.5, 'liked_by_weight': 0.5, 'include_role': False}).fetchall()

            db_conn.commit()

            # Extracts actual timing out of result
            for row in result:
                if 'Execution Time' in str(row._data):
                    time = re.findall(r'\d+\.\d+', str(row._data))[0]

        # closes connection pool and connector
        pool.dispose()
        connector.close()
    
    benchmark_sql_with_retry()

    # writes result to special collection
    db.collection("SQL_Benchmark_Matching").add({"timestamp": firestore.firestore.SERVER_TIMESTAMP, "execution_duration": time})

@with_retry()
def execute_sql_block_with_retry(uid, uid_to_block):
    with Connector(refresh_strategy="lazy") as connector:
        pool = connect_with_connector(connector)

        try:
            with pool.connect() as db_conn:
                stmt = sqlalchemy.text(
                    """SELECT add_block(:p_blocking_user, :p_blocked_user)""")

                # Execute with the parameters
                result = db_conn.execute(stmt, {
                    'p_blocking_user': uid,
                    'p_blocked_user': uid_to_block}).fetchall()

                db_conn.commit()
                logging.info(f"Committed dislike in SQL: calling user: {uid}")
        finally:
            # dispose pool
            pool.dispose()
# BLOCK USER: Blocking a user in the app
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.GB_1, concurrency=20, timeout_sec=90, enforce_app_check=True)
def blockUser(req: https_fn.CallableRequest) -> Any:
    """
    Blocking a user in the app
    :param req: Data includes "uidToBlock" (string)
    :return: None
    """


    if req.auth.uid == None:
        return "User not authenticated"

    # gets calling user uid and uid of person to block as a parameter
    uid = req.auth.uid
    uid_to_block = req.data["uidToBlock"]

    db = firestore.client()
    likes_collection = db.collection("likes")

    # fetches all applicable likes documents
    likes_to_unlike = find_all_likes_of_two_users(uid, uid_to_block, likes_collection)

    # unmatches all found documents + resets timestamp so that it definitely does not show up on boost likes screen
    for like in likes_to_unlike:
        update_data = {"unmatched": True, "timestamp": (datetime.now()-timedelta(2))}
        like.reference.update(update_data)
        filter = db.collection('likeFilters').document(like.id).get()
        if filter.exists:
            filter.reference.update(update_data)

    if not is_dev_environment():
        # adds block to postgresql db
        try:
            try:
                execute_sql_block_with_retry(uid, uid_to_block)
            except Exception as e:
                logging.exception(e)
                db.collection('SQLExceptions').add(
                    {'function': 'blockUser', 'user': uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})
        except:
            logging.exception(f"Fail Block User: calling user: {uid}, user to be blocked: {uid_to_block}")
    else:
        logging.info("Dev environment, skipping SQL")

# CREATE INITIAL MATCH: Matches two users for secure matching
@https_fn.on_call(region="europe-west2", concurrency=20, memory=options.MemoryOption.GB_1, timeout_sec=180, enforce_app_check=True)
def createInitialMatch(req: https_fn.CallableRequest) -> Any:
    """
    Matches two users for secure matching
    :param req: Data includes "otherUserUID" (string)
    :return: None
    """

    if req.auth == None:
        return "User not authenticated"

    # gets calling user uid and uid of person to match with as a parameter
    caller_uid = req.auth.uid
    other_uid = req.data["otherUserUID"]
    inactiveChats = req.data.get("inactiveChats", 10)

    time.sleep(3)

    db = firestore.client()

    own_ref = db.collection('users').document(caller_uid)
    own_doc = own_ref.get().to_dict()

    # getting own public name from document, instead of using unreliable passed in parameter
    own_name = own_doc.get("publicName", "****")

    # checking if other user actually liked this user
    other_user_ref = db.collection('users').document(other_uid)
    other_user_doc = other_user_ref.get()
    other_user_liked_users = other_user_doc.get('likedProfiles')
    other_user_liked_uids_og = [x['uid'] for x in other_user_liked_users]

    other_user_liked_uids = copy.deepcopy(other_user_liked_uids_og)

    print(f"other_user_liked_uids: {other_user_liked_uids}")

    other_user_liked_uids = fetch_liked_profiles(other_user_ref, other_user_liked_users, other_uid)

    # getting other pnma from document, instead of using unreliable passed in parameter

    other_doc = other_user_doc.to_dict()
    other_lang = other_doc.get("language", "en")
    other_pnMa = other_doc.get("pnMa", False)

    try:

        user_blocked_profiles = [x['uid'] for x in other_user_doc.to_dict().get("blockedProfiles", [])]

    except Exception as e:

        logging.exception(e)
        user_blocked_profiles = []

    # if liked user has blocked calling user, unmatch the like immediately
    if caller_uid in user_blocked_profiles:
        print("caller is blocked")
        return

    print(f"caller_uid not in other_user_liked_uids: {caller_uid not in other_user_liked_uids}")

    # if caller_uid not in other_user_liked_uids:
    #     print("return")
    #     return

    print("not return")

    likes_collection = db.collection("likes")

    # gets the likes document to update
    likes_query = likes_collection \
        .where(filter=FieldFilter("likedUser", "==", own_ref)) \
        .where(filter=FieldFilter("likingUser", "==", other_user_ref))

    likes_documents = likes_query.get()


    # updates this like document with mutual = true etc.

    if len(likes_documents) > 0:
        update_data = {'unmatched': False, 'mutual': True, 'speakAllow': True, 'matchDate': firestore.SERVER_TIMESTAMP, 'recentMessageSender': own_ref, 'recentMessageDate': firestore.SERVER_TIMESTAMP, 'shortMessagesAllow': True, 'profileVisitsAllow': True, 'nameAddressAllow': True, 'nsfwAllow': False}
        likes_document = likes_documents[0]
        likes_document_ref = likes_document.reference
        likes_document_ref.update(update_data)
        filter = db.collection('likeFilters').document(likes_document.id).get()
        if filter.exists:
            filter.reference.update(update_data)

    # updates Amplitude accordingly

    try:
        amplitude_client = Amplitude("65dbac83dec4e06df231ca34a83e4757")

        other_gender = other_user_doc.get('gender')
        own_gender = own_doc.get("gender")

        identify_obj = Identify()
        identify_obj.set("Has Been Matched", True)
        amplitude_client.identify(identify_obj, EventOptions(user_id=caller_uid))

        identify_obj2 = Identify()
        identify_obj2.set("Has Been Matched", True)
        amplitude_client.identify(identify_obj2, EventOptions(user_id=other_uid))

        event1 = BaseEvent(event_type="Matching: Matched", user_id=caller_uid, event_properties={
            "Other Gender Matched": other_gender,
            "Own Gender Matched": own_gender,
            "Match ID": likes_document_ref.id,
            "Findom Involved": (own_doc.get("findom", False) or other_doc.get("findom", False))
        })
        amplitude_client.track(event1)

        event2 = BaseEvent(event_type="Matching: Matched", user_id=other_uid, event_properties={
            "Other Gender Matched": own_gender,
            "Own Gender Matched": other_gender,
            "Match ID": likes_document_ref.id,
            "Findom Involved": (own_doc.get("findom", False) or other_doc.get("findom", False))
        })
        amplitude_client.track(event2)
    except:
        print("Amplitude failed")

    if not is_dev_environment():
        # update Postgres to reflect match
        try:
            try:
                execute_sql_like_with_retry(caller_uid, other_uid)
            except Exception as e:
                logging.exception(e)
                db.collection('SQLExceptions').add(
                    {'function': 'createInitialMatch', 'user': caller_uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})
        except:
            logging.exception("Fail Create Initial Match User")
    else:
        logging.info("Dev environment, skipping SQL")

    # Determining if cloud function is "v2", i.e., the notification for the message should be sent from here & other user wants it
    v2 = req.data.get("v2", False)

    if v2:
        try:
            logging.info(f"Own name parameter passed in: {own_name}")

            own_public_profile_id = req.data.get("ownPublicProfileId", "")

            payload = {"chat":f"likes/{likes_document_ref.id}","partnerName": own_name}
            initial_page_name = "Chat"

            message = f"{own_name} {get_translated_text("matched with you. Write a message now and get the conversation started!", other_lang)}"

            notifications_collection = db.collection("notifications")
            # send in-app notification
            addInAppNotification(own_doc.get("publicProfile"), [other_user_ref], likes_document_ref, "match", message, notifications_collection)

            # send a push notification
            if other_pnMa:
                push_notifications_collection = db.collection("ff_user_push_notifications")
                title = get_translated_text("New match", other_lang)

                sendPushNotificationWithPayload(other_uid, title, message, push_notifications_collection, payload, initial_page_name)

        except Exception as e:
            logging.error(e)

    checkChatLimit(caller_uid)
    checkChatLimit(other_uid)

    return

@with_retry()
def execute_sql_dislike_with_retry(uid, uid_to_dislike):

    with Connector(refresh_strategy="lazy") as connector:
        pool = connect_with_connector(connector)

        try:
            with pool.connect() as db_conn:
                logging.info(f"Attempting to dislike in SQL: calling user: {uid}")
                stmt = sqlalchemy.text(
                    """SELECT insert_suggestion_wr_now(:user_id, :suggested_user_id, :suggestion_outcome)""")

                result = db_conn.execute(stmt, {
                    'user_id': uid,
                    'suggested_user_id': uid_to_dislike,
                    'suggestion_outcome': "Rejected"}).fetchall()

                db_conn.commit()
                logging.info(f"Committed dislike in SQL: calling user: {uid}")
        finally:
            # dispose pool
            pool.dispose()


@with_retry()
def execute_sql_dislike_with_retry_incl_pool(pool, uid, uid_to_dislike):

    with pool.connect() as db_conn:
        logging.info(f"Attempting to dislike in SQL: calling user: {uid}")
        stmt = sqlalchemy.text(
            """SELECT insert_suggestion_wr_now(:user_id, :suggested_user_id, :suggestion_outcome)""")

        result = db_conn.execute(stmt, {
            'user_id': uid,
            'suggested_user_id': uid_to_dislike,
            'suggestion_outcome': "Rejected"}).fetchall()

        db_conn.commit()
        logging.info(f"Committed dislike in SQL: calling user: {uid}")


# DISLIKE USER: Updates Postgres + further backend data on disliking a user
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.GB_4, concurrency=80, enforce_app_check=True)
def dislikeUser(req: https_fn.CallableRequest) -> Any:
    """
        Updates Postgres + further backend data on disliking a user
        :param req: Data includes "uidToDislike" (string)
        :return: None
    """

    if req.auth.uid == None:
        return "User not authenticated"

    global db_conn_new

    if not db_conn_new:
        db_conn_new = connect_with_connector_persist()

    print(req.raw_request.user_agent)

    uid = req.auth.uid
    uid_to_dislike = req.data["uidToDislike"]

    db = firestore.client()

    # Get the current UTC date and time
    current_date = datetime.now()

    # Format the date as "YYYYMMDD"
    formatted_date = current_date.strftime("%Y%m%d")

    likes_collection = db.collection("likes")

    # Retrieve relevant likes to unlike
    likes_to_unlike = find_all_likes_of_two_users(uid, uid_to_dislike, likes_collection)

    # Unlike each of the found like
    for like in likes_to_unlike:
        update_data = {"unmatched": True}
        like.reference.update(update_data)
        filter = db.collection('likeFilters').document(like.id).get()
        if filter.exists:
            filter.reference.update(update_data)

    # Update involved user documents
    db.collection("users").document(uid).update({"dislikesGiven": firestore.Increment(1), f"dislikesGivenDay.{formatted_date}": firestore.Increment(1), "latestDayDislikesGiven": formatted_date})
    db.collection("users").document(uid_to_dislike).update({"dlr": firestore.Increment(1), f"dislikesReceivedDay.{formatted_date}": firestore.Increment(1), "latestDayDislikesReceived": formatted_date})

    own_data = db.collection("users").document(uid).get().to_dict()
    other_data = db.collection("users").document(uid_to_dislike).get().to_dict()

    if not is_dev_environment():
        # Update Postgres
        try:
            try:
                execute_sql_dislike_with_retry_incl_pool(db_conn_new, uid, uid_to_dislike)
            except Exception as e:
                logging.exception(e)
                db.collection('SQLExceptions').add(
                    {'function': 'dislikeUser', 'user': uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

            event2 = BaseEvent(event_type="Matching: Gave Dislike", user_id=uid,
                            event_properties={"Disliked User": uid_to_dislike, "Disliked Gender": other_data.get("gender"), "Total Dislikes Given": own_data.get("dislikesGiven", 0), "Findom": own_data.get("findom", False)})

            event3 = BaseEvent(event_type="Matching: Received Dislike", user_id=uid_to_dislike,
                            event_properties={"Disliking User": uid, "Disliking Gender": own_data.get("gender"), "From Findom": own_data.get("findom", False)})

            amplitude_client.track(event2)
            amplitude_client.track(event3)
        except:
            logging.exception(f"Fail Dislike User: Disliking User: {uid}, Disliked User: {uid_to_dislike}")
    else:
        logging.info("Dev environment, skipping SQL")

    return None

# GET MIDNIGHT STRING: Returns current day timestamp for secure like limit counting
@https_fn.on_call(region="europe-west2", enforce_app_check=True)
def getMidnightString(req: https_fn.CallableRequest) -> Any:
    """
    Returns current day timestamp for secure like limit counting
    :param req: Callable request incl. user auth information - no extra params
    :return: String (current server-day in YYYYMMDD format)
    """

    if req.auth.uid == None:
        return "User not authenticated"

    # Get the current UTC date and time
    current_date = datetime.now()

    # Format the date as "YYYYMMDD"
    formatted_date = current_date.strftime("%Y%m%d")

    return formatted_date

# GET MIDNIGHT STRING MONDAY: Returns timestamp of last Monday for secure evolved like limit counting
@https_fn.on_call(region="europe-west2",enforce_app_check=True)
def getMidnightStringMonday(req: https_fn.CallableRequest) -> Any:
    """
    Returns timestamp of last Monday for secure evolved like limit counting
    :param req: Callable request incl. user auth information - no extra params
    :return: String (last Monday's server-day in YYYYMMDD format)
    """

    if req.auth.uid == None:
        return "User not authenticated"

    # Get the current UTC date and time
    today = datetime.now()

    # Format the date as "YYYYMMDD"
    last_monday = today - timedelta(days=today.weekday())

    # Format the date as "YYYYMMDD"
    formatted_date = last_monday.strftime("%Y%m%d")

    return formatted_date

# INCOGNITO LIKE: Makes sure incognito likes are processed correctly
@https_fn.on_call(region="europe-west2", concurrency=20, memory=options.MemoryOption.GB_1, timeout_sec=180, enforce_app_check=True)
def incognitoLike(req: https_fn.CallableRequest) -> Any:
    """
        Makes sure incognito likes are processed correctly
        :param req: Callable request incl. user auth information - extra param: userToLikeUID
        :return: None
    """

    if req.auth.uid == None:
        return "User not authenticated"

    uid = req.auth.uid

    uid_to_like = req.data["userToLikeUID"]

    db = firestore.client()

    #increment the like counter of the liked person & likes given counter of liking counter
    try:
        db.collection("users").document(uid_to_like).update({"likesCount": firestore.Increment(1)})
    except Exception as e:
        logging.exception(e)


    db.collection("users").document(uid).update({"likesGiven": firestore.Increment(1)})

    liked_user_ref = db.collection("users").document(uid_to_like)
    liked_user = liked_user_ref.get().to_dict()
    user_blocked_profiles = [x['uid'] for x in liked_user.get("blockedProfiles", [])]
    # user_disliked_profiles = [x['uid'] for x in liked_user.get("passedProfiles", [])]

    liked_user_ref.update({"likesReceived": firestore.Increment(1), "incognitoLikedBy": firestore.ArrayUnion([uid])})

    # updating in Postgres
    try:

        try:
            @with_retry()
            def insert_suggestion_with_retry():
                connector = Connector(refresh_strategy="lazy")
                pool = connect_with_connector(connector)

                with pool.connect() as db_conn:

                    stmt = sqlalchemy.text(
                        """SELECT insert_suggestion_wr_now(:user_id, :suggested_user_id, :suggestion_outcome)""")

                    # Execute with the parameters
                    result = db_conn.execute(stmt, {
                        'user_id': uid,
                        'suggested_user_id': uid_to_like,
                        'suggestion_outcome': "Accepted"}).fetchall()

                    db_conn.commit()

                pool.dispose()
                connector.close()

            insert_suggestion_with_retry()


        except Exception as e:
            logging.exception(e)
            db.collection('SQLExceptions').add(
                {'function': 'Incongito Like', 'user': uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

    except:
        print("Fail Incognito Like User")

# LIKE USER: Updates Postgres + further backend data on liking a user

@with_retry()
def execute_sql_like_with_retry_incl_pool(pool, uid, uid_to_like):

        with pool.connect() as db_conn:
            logging.info(f"Attempting like SQL: calling user: {uid}")
            stmt = sqlalchemy.text(
                """SELECT insert_suggestion_wr_now(:user_id, :suggested_user_id, :suggestion_outcome)""")

            # Execute with the parameters
            result = db_conn.execute(stmt, {
                'user_id': uid,
                'suggested_user_id': uid_to_like,
                'suggestion_outcome': "Accepted"}).fetchall()

            db_conn.commit()
            logging.info(f"Committed like SQL: calling user: {uid}")

@with_retry()
def execute_sql_like_with_retry(uid, uid_to_like):
    with Connector(refresh_strategy="lazy") as connector:
        pool = connect_with_connector(connector)

        try:
            with pool.connect() as db_conn:
                logging.info(f"Attempting like SQL: calling user: {uid}")
                stmt = sqlalchemy.text(
                    """SELECT insert_suggestion_wr_now(:user_id, :suggested_user_id, :suggestion_outcome)""")

                # Execute with the parameters
                result = db_conn.execute(stmt, {
                    'user_id': uid,
                    'suggested_user_id': uid_to_like,
                    'suggestion_outcome': "Accepted"}).fetchall()

                db_conn.commit()
                logging.info(f"Committed like SQL: calling user: {uid}")
        finally:
            # dispose pool
            pool.dispose()

@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.GB_1, concurrency=20, timeout_sec=180, enforce_app_check=True)
def likeUser(req: https_fn.CallableRequest) -> Any:
    """
       Updates Postgres + further backend data on liking a user
       :param req: Data includes "uidToLike" (string), "refLikeDoc" (string)
       :return: None
    """
    if req.auth.uid == None:
        return "User not authenticated"

    uid = req.auth.uid

    global db_conn_new

    if db_conn_new == None:
        db_conn_new = connect_with_connector_persist()

    # get parameters from request
    uid_to_like = req.data["uidToLike"]
    ref_like_doc = req.data["refLikeDoc"]

    db = firestore.client()

    # Get the current UTC date and time
    current_date = datetime.now()

    # Format the date as "YYYYMMDD"
    formatted_date = current_date.strftime("%Y%m%d")

    user_document = db.collection("users").document(uid).get()

    user_data = user_document.to_dict()
    findom = user_data.get('findom', False)

    likes_given_today = user_data.get("likesGivenDay", {}).get(formatted_date, 0)

    likes_given_new = likes_given_today + 1

    # update liking statistics in calling user document
    db.collection("users").document(uid).update({"likesGiven": firestore.Increment(1), f"likesGivenDay.{formatted_date}": firestore.Increment(1), "latestDayLikesGiven": formatted_date})

    # get the liked users' user document
    liked_user_ref = db.collection("users").document(uid_to_like)
    liked_user = liked_user_ref.get().to_dict()
    user_blocked_profiles = [x['uid'] for x in liked_user.get("blockedProfiles", [])]
    # user_disliked_profiles = [x['uid'] for x in liked_user.get("passedProfiles", [])]

    # if liked user has blocked calling user, unmatch the like immediately
    if uid in user_blocked_profiles:
        update_data = {"unmatched": True}
        db.collection("likes").document(ref_like_doc).update(update_data)
        filter = db.collection('likeFilters').document(ref_like_doc).get()
        if filter.exists:
            filter.reference.update(update_data)
    else:
        update_dict = {"likesReceived": firestore.Increment(1), f"likesReceivedDay.{formatted_date}": firestore.Increment(1), "latestDayLikesReceived": formatted_date}
        if findom:
            update_dict['findomLikesReceived'] = firestore.Increment(1)
        liked_user_ref.update(update_dict)

    # update Postgres accordingly with like
    try:
        if not is_dev_environment():
            try:
                execute_sql_like_with_retry_incl_pool(db_conn_new, uid, uid_to_like)
            except Exception as e:
                logging.exception(e)
                db.collection('SQLExceptions').add(
                    {'function': 'likeUser', 'user': uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})
        else:
            logging.info("Dev environment, skipping SQL")

        # send a push notification to the liked user if it is a male
        try:
            push_notifications_collection = db.collection("ff_user_push_notifications")


            user_notification_cohort = liked_user.get("notificationTestingCohort")
            uid_liked = liked_user.get("uid")
            uid_liked_gender = liked_user.get("gender")
            uid_liked_name = liked_user.get("name")
            uid_liked_language = liked_user.get("language", "en")

            event2 = BaseEvent(event_type="Matching: Gave Like", user_id=uid,
                               event_properties={"Liked User": uid_liked, "Liked Gender": uid_liked_gender, "Like ID": ref_like_doc, "Total Likes Given": user_data.get("likesGiven", 0), "Findom": user_data.get("findom", False)})

            event3 = BaseEvent(event_type="Matching: Received Like", user_id=uid_liked,
                               event_properties={"Liking User": uid, "Liking Gender": user_data.get("gender"), "Like ID": ref_like_doc, "From Findom": user_data.get("findom", False)})

            amplitude_client.track(event2)
            amplitude_client.track(event3)

            professionals_req = liked_user.get('professionalsReq', False)
            should_not_receive_findom_like = findom and professionals_req

            if uid_liked_gender == "Male" and not should_not_receive_findom_like:
                if user_notification_cohort == "Emoji":

                    sendPushNotification(uid_liked, "Chyrpe",
                                         f"{get_translated_text("Hey", uid_liked_language)} {uid_liked_name}, {get_translated_text("you’ve just received a like!", uid_liked_language)} 🩷", push_notifications_collection)

                    event = BaseEvent(event_type="Notifications: Sent Received Like Notification Emoji",
                                      user_id=uid_liked)
                    amplitude_client.track(event)

                else:
                    sendPushNotification(uid_liked, "Chyrpe",
                                         f"{get_translated_text("Hey", uid_liked_language)} {uid_liked_name}, {get_translated_text("you’ve just received a like!", uid_liked_language)}", push_notifications_collection)

                    event = BaseEvent(event_type="Notifications: Sent Received Like Notification NoEmoji",
                                      user_id=uid_liked)
                    amplitude_client.track(event)

                event = BaseEvent(event_type="Notifications: Sent Received Like Notification", user_id=uid_liked,
                                  event_properties={"Notification Testing Cohort": user_notification_cohort})
                amplitude_client.track(event)



        except Exception as e:
            logging.exception(e)

    except:
        logging.exception("Fail Like User")


@with_retry()
def reset_own_dislikes_sql(uid):
    with Connector(refresh_strategy="lazy") as connector:
        pool = connect_with_connector(connector)
        try:
            with pool.connect() as db_conn:
                logging.info(f"Attempting to dislike in SQL: calling user: {uid}")
                stmt = sqlalchemy.text(
                    """SELECT delete_own_rejections(:user_id)""")

                result = db_conn.execute(stmt, {
                    'user_id': uid,
                }).fetchall()

                db_conn.commit()
                logging.info(f"Committed deletion of own rejections in SQL: calling user: {uid}")
        finally:
            # dispose pool
            pool.dispose()

# RESET OWN DISLIKES: Resets dislikes for users who have seen everybody
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.MB_512, enforce_app_check=True)
def resetOwnDislikes(req: https_fn.CallableRequest) -> Any:
    """
       Resets dislikes for users who have seen everybody
       :param req: Callable request incl. user auth information - no extra params
       :return: None
    """
    if req.auth.uid == None:
        return

    uid = req.auth.uid

    db = firestore.client()

    users_collection = db.collection("users")
    user_ref = users_collection.document(uid)

    # call SQL function to delete all but matches there
    try:
        try:

            reset_own_dislikes_sql(uid)

        except Exception as e:
            logging.exception(e)
            db.collection('SQLExceptions').add(
                {'function': 'resetOwnDislikes', 'user': uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

    except:
        logging.exception(f"Fail Reset Own Dislikes: User: {uid}")

    # delete all passed profiles in Firestore
    user_ref.update({"passedProfiles": firestore.DELETE_FIELD, "matchingSuggestions": firestore.DELETE_FIELD, "sawEverybody": firestore.DELETE_FIELD})

    time.sleep(1)

# SAVE USER: Updates Postgres + further backend data on saving a user
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.GB_1, timeout_sec=180, enforce_app_check=True)
def saveUser(req: https_fn.CallableRequest) -> Any:
    """
         Updates Postgres + further backend data on saving a user

         Will replace saving in generateMatches in the future (phase in from 29/10/24)
         :param req: Data includes "uidToSave" (string)
         :return: None
      """

    if req.auth.uid == None:
        return "User not authenticated"

    global db_conn_new

    if db_conn_new == None:
        db_conn_new = connect_with_connector_persist()

    uid = req.auth.uid

    uid_to_save = req.data["uidToSave"]

    db = firestore.client()

    push_notifications_collection = db.collection("ff_user_push_notifications")

    saved_user = db.collection("users").document(uid_to_save).get().to_dict()
    user_notification_cohort = saved_user.get("notificationTestingCohort")
    uid_saved = saved_user.get("uid")
    uid_saved_language = saved_user.get("language", "en")

    own_data = db.collection("users").document(uid).get().to_dict()

    # update saved users in Postgres
    @with_retry()
    def save_user_with_retry(pool):

        with pool.connect() as db_conn:

            # update saved profiles in Postgres

                stmt = sqlalchemy.text(
                    """SELECT insert_suggestion_wr_now(:user_id, :suggested_user_id, :suggestion_outcome)""")

                # Execute with the parameters
                result = db_conn.execute(stmt, {
                    'user_id': uid,
                    'suggested_user_id': saved_user.get('uid'),
                    'suggestion_outcome': "Saved"}).fetchall()

                db_conn.commit()

                logging.info(f"Committed saved user in SQL: {str(saved_user.get('uid'))}, calling user: {uid}")

    try:
        save_user_with_retry(db_conn_new)
    except Exception as e:
        logging.exception(e)
        db.collection('SQLExceptions').add(
            {'function': 'saveUser -> saving', 'user': uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

    # send push notification to saved user
    try:

        event2 = BaseEvent(event_type="Matching: Gave Save", user_id=uid,
                           event_properties={"Saved User": saved_user.get('uid'),
                                             "Saved Gender": saved_user.get("gender"),
                                             "Findom": own_data.get("findom", False)})

        event3 = BaseEvent(event_type="Matching: Received Save", user_id=saved_user.get("uid"),
                           event_properties={"Saving User": uid, "Saving Gender": own_data.get("gender"), "From Findom": own_data.get("findom", False)})

        amplitude_client.track(event2)
        amplitude_client.track(event3)


        if user_notification_cohort == "Emoji":

            sendPushNotification(uid_saved, "Chyrpe",
                                 f"{get_translated_text("Someone just saved your profile. Maybe some profile changes can make it a like?", uid_saved_language)} ⭐️", push_notifications_collection)

            event = BaseEvent(event_type="Notifications: Sent Received Save Notification Emoji",
                              user_id=uid_saved)
            amplitude_client.track(event)

        else:
            sendPushNotification(uid_saved, "Chyrpe",
                                 f"{get_translated_text("Someone just saved your profile. Maybe some profile changes can make it a like?", uid_saved_language)}", push_notifications_collection)

            event = BaseEvent(event_type="Notifications: Sent Received Save Notification NoEmoji",
                              user_id=uid_saved)
            amplitude_client.track(event)

        event = BaseEvent(event_type="Notifications: Sent Received Save Notification", user_id=uid_saved,
                          event_properties={"Notification Testing Cohort": user_notification_cohort})
        amplitude_client.track(event)
    except Exception as e:
        logging.exception(e)

#SEND MESSAGE: Takes over all message sending functionality from sendMessage, incl. more advanced GPT filter
@https_fn.on_call(region="europe-west1", min_instances=1,  memory=options.MemoryOption.GB_2, cpu=2, concurrency=80, enforce_app_check=True)
def sendMessageAdvanced(req: https_fn.CallableRequest) -> Any:
    """
    Checks messages against ChatGPT filtering & sends message from one user to the other or
    returns ChatGPT rejection

    :param req: Data includes "matchId" (string), "message" (string)
    """

    if req.auth.uid == None:
        return "User not authenticated"

    # get caller parameters
    uid = req.auth.uid
    match_id = req.data.get("matchId")
    message = req.data.get("message")
    inactiveChats = req.data.get("inactiveChats", 10)

    db = firestore.client()

    # get user document
    user_ref = db.collection("users").document(uid)
    user_doc = user_ref.get()
    user_data = user_doc.to_dict()

    user_gender = user_data.get("gender")

    # retrieves match and finds if message needs to get scanned
    match_ref = db.collection("likes").document(match_id)
    match_doc = match_ref.get()
    match_data = match_doc.to_dict()

    # determine if message needs to get scanned
    nsfw_allowed = match_data.get("nsfwAllow")
    scan_need = (user_gender == "Male") and (not nsfw_allowed)
    mutual = match_data.get("mutual", False)

    # if message needs to get scanned:
    if scan_need:
        #   scan
        import ModerationHelper as mh

        scan_result = mh.moderate_content_leaner(message)
        #   if scan comes back negative: reject
        if scan_result == False:
            return json.dumps({"id": "rejected", "title": f"{'Your match' if mutual else 'The other person'} has explicit and kinky language disabled", "content": "Your message may contain such content. Women decide when that is okay."})

    # else: continue

    # set the batch document

    batch = db.batch()

    # create message doc

    match_ref = db.collection("likes").document(match_id)
    message_ref = match_ref.collection("messages").document()
    batch.create(message_ref, {"body": message, "time": firestore.firestore.SERVER_TIMESTAMP, "isSysMessage": False,
                               "sender": user_ref})

    # update match doc

    batch.update(match_ref, {"recentMessageText": message,
                             "recentMessageDate": firestore.firestore.SERVER_TIMESTAMP, "chatting": True,
                             "recentMessageSender": user_ref, "recentMessageSeenBy": [user_ref],
                             "recentMessageNotSeenBy": [u for u in match_data.get("involvedUsers") if u.id != uid]})

    # TODO: update user docs & send notifications

    # one batched write for all changes

    batch.commit()

    # exit
    uids = list(set([u.id for u in match_data.get("involvedUsers", [])]))
    for uid in uids:
        checkChatLimit(uid)
    return ''

#SEND MESSAGE: Updates batched statistics for Amplitude based on every sent message
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.MB_512, enforce_app_check=True)
def sendMessage(req: https_fn.CallableRequest) -> Any:
    """
         Updates Postgres + further backend data on saving a user

         Will replace saving in generateMatches in the future (phase in from 29/10/24)
         :param req: Data includes "otherGender" (string), "matchId" (string)
         :return: None
     """
    if req.auth.uid == None:
        return "User not authenticated"


    uid = req.auth.uid
    other_gender = req.data["otherGender"]
    match_id = req.data["matchId"]

    db = firestore.client()

    # 3 May 2025: New messaging system messages should be excluded from this
    recent_message = db.collection("likes").document(match_id).collection('messages').order_by('time', direction=firestore.Query.DESCENDING).limit(1).get()
    if recent_message[0].to_dict().get('processing') is not None:
        return

    current_date = datetime.now()

    # Format the date as "YYYYMMDD"
    formatted_date = current_date.strftime("%Y%m%d")

    users_collection = db.collection("users")
    likes_collection = db.collection("likes")

    # Update user as needed
    users_collection.document(uid).update({f"messageTodayPerMatchSent.{match_id}.otherGender": other_gender, f"messageTodayPerMatchSent.{match_id}.count": firestore.Increment(1), "latestDayMessaging": formatted_date})
    likes_collection.document(match_id).update({"recentMessageSender": users_collection.document(uid)})

    event2 = BaseEvent(event_type="Matching: Sent Message", user_id=uid,
                       event_properties={"Match ID": match_id, "Receiving Gender": other_gender})

    amplitude_client.track(event2)

    # Determining if cloud function is "v2", i.e., the notification for the message should be sent from here & other user wants it
    v2 = req.data.get("v2", False)

    if v2:
        
        me = req.data.get("me", False)
        message_input = req.data.get("message", "")
        other_uid = req.data.get("otherUID", "")
        own_name = req.data.get("ownName", "")
        other_users_reference = db.collection("users").document(other_uid)
        other_language = other_users_reference.get().to_dict().get("language", "en")
        own_public_profile_id = req.data.get("ownPublicProfileId", "")

        payload = {"chat": f"likes/{match_id}", "partnerName": own_name}
        initial_page_name = "Chat"

        message = f"{own_name} {get_translated_text("sent you a new message:", other_language)} {message_input[0:80]}"

        notifications_collection = db.collection("notifications")
        # send in-app notification
        addInAppNotification(users_collection.document(uid).collection("publicProfile").document(own_public_profile_id), [users_collection.document(other_uid)],
                             likes_collection.document(match_id), "message", message, notifications_collection)

        if v2 and me:
            push_notifications_collection = db.collection("ff_user_push_notifications")
            title = get_translated_text("New message", other_language)
            sendPushNotificationWithPayload(other_uid, title, message, push_notifications_collection, payload, initial_page_name)

#UNMATCH: Unmatches two users entirely on the backend, including all notifications, messages etc.
@https_fn.on_call(region="europe-west2", concurrency=20, memory=options.MemoryOption.GB_1, enforce_app_check=True)
def unmatch(req: https_fn.CallableRequest) -> Any:
    """
         Unmatches two users entirely on the backend, including all notifications, messages etc.

         :param req: Data includes "otherUserUID" (string)
         :return: None
      """

    if req.auth.uid == None:
        return "User not authenticated"

    caller_uid = req.auth.uid
    other_uid = req.data["otherUserUID"]

    # 2. Checks other user’s doc whether they actually liked calling user

    db = firestore.client()

    other_user_ref = db.collection('users').document(other_uid)
    other_user_doc = other_user_ref.get()
    other_user_public_profile = other_user_doc.get('publicProfile')

    own_user_ref = db.collection('users').document(caller_uid)
    own_user_doc = own_user_ref.get()
    own_public_profile = own_user_doc.get('publicProfile')


    likes_collection = db.collection("likes")

    likes_documents = find_all_likes_of_two_users(caller_uid, other_uid, likes_collection)

    # 3. Updates this like document with mutual = false

    if len(likes_documents) > 0:
        for likes_document in likes_documents:
            update_data = {'mutual': False, 'unmatched': True, "timestamp": (datetime.now()-timedelta(2))}
            likes_document_ref = likes_document.reference
            likes_document_ref.update(update_data)
            filter = db.collection('likeFilters').document(likes_document.id).get()
            if filter.exists:
                filter.reference.update(update_data)


    # 4. Delete associated notifications

    try:
        notifications_collection = db.collection('notifications')

        def query_or_composite_filter_notifications() -> list[DocumentSnapshot]:
            query1 = notifications_collection.where(filter=FieldFilter("sender", "==", own_public_profile)).where(filter=FieldFilter("receivers",
                                                                                              "==",
                                                                                              [other_user_ref]))
            query2 = notifications_collection.where(filter=FieldFilter("sender", "==", other_user_public_profile)).where(filter=FieldFilter("receivers",
                                                                                                     "==",
                                                                                                     [own_user_ref]))

            results1 = query1.get()
            results2 = query2.get()

            return list(results1) + list(results2)

        notifications_documents = query_or_composite_filter_notifications()

        # 5. Updates this like document with mutual = false

        if len(notifications_documents) > 0:
            for n_document in notifications_documents:
                n_document_ref = n_document.reference
                n_document_ref.delete()


    except Exception as e:
        logging.exception(e)

    # 6. delete this match? / put it into matches for deletion?

    try:

        amplitude_client = Amplitude("65dbac83dec4e06df231ca34a83e4757")

        other_gender = other_user_doc.get('gender')
        own_gender = own_user_doc.get('gender')

        identify_obj2 = Identify()
        identify_obj2.set("Has Been Unmatched", True)
        amplitude_client.identify(identify_obj2, EventOptions(user_id=other_uid))

        event1 = BaseEvent(event_type="Matching: Unmatched", user_id=caller_uid, event_properties={
            "Other Gender Matched": other_gender,
            "Own Gender Matched": own_gender,
            "Match ID": likes_document_ref.id
        })
        amplitude_client.track(event1)
    except:
        print("Amplitude failed")

    if not is_dev_environment():
        try:
            @with_retry()
            def unmatch_with_retry():
                connector = Connector(refresh_strategy="lazy")
                pool = connect_with_connector(connector)
                try:
                    with pool.connect() as db_conn:
                        # query database
                        # add_block(p_blocking_user character varying, p_blocked_user character varying
                        # result = db_conn.execute(sqlalchemy.text("""SELECT * from \"User\"""")).fetchall()
                        stmt = sqlalchemy.text(
                            """SELECT add_block(:p_blocking_user, :p_blocked_user)""")

                        # Execute with the parameters
                        result = db_conn.execute(stmt, {
                            'p_blocking_user': caller_uid,
                            'p_blocked_user': other_uid}).fetchall()
                        # result = db_conn.execute(sqlalchemy.func.add_user('user_example', 23, datetime.datetime(2001, 8, 1), Gender.Female, Role.Dominant, GenderPreference.Male, 0.1291, -43.1923)).fetchall()

                        db_conn.commit()

                        # Do something with the results
                        for row in result:
                            print(row._mapping)
                finally:
                    pool.dispose()
                    connector.close()

            unmatch_with_retry()
        except Exception as e:
            logging.exception(e)
            db.collection('SQLExceptions').add(
                {'function': 'unmatch', 'user': caller_uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})
    else:
        logging.info("Dev environment, skipping SQL")

    return

# UPDATE ELO SCORE: Scheduled to call the elo score updating function in Postgres once a day
@scheduler_fn.on_schedule(
    schedule="*/30 0 * * *", region="europe-west1")
def updateEloScores(event: scheduler_fn.ScheduledEvent) -> None:
    """
         Scheduled to call the elo score updating function in Postgres once a day

         :param event: Due to scheduled trigger of cloud function, no practical relevance
         :return: None
    """

    db = firestore.client()

    global db_conn_new

    if not db_conn_new:
        db_conn_new = connect_with_connector_persist()

    try:
        @with_retry()
        def update_elo_scores_with_retry(pool):
            # 1. updates raw elo scores
            with pool.connect() as db_conn:
                stmt = sqlalchemy.text(
                    """CALL update_elo_scores()""")

                db_conn.execute(stmt)
                db_conn.commit()

            with pool.connect() as db_conn:
                stmt = sqlalchemy.text(
                    """CALL update_elo_scores_uniform()""")

                db_conn.execute(stmt)
                db_conn.commit()

            time.sleep(5)

        # 2. normalises updated elo scores
            with pool.connect() as db_conn:

                stmt = sqlalchemy.text(
                    """CALL normalise_elo()""")

                db_conn.execute(stmt)
                db_conn.commit()
        
        update_elo_scores_with_retry(db_conn_new)

    except Exception as e:
        logging.exception(e)
        db.collection('SQLExceptions').add(
            {'function': 'Elo Update', 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

    except:
        logging.exception("Fail: Raw Elo Update")

    
    print("Successfully updated")

# UPDATE MATCHING LIMITS: Updates all server timestamps in likeLimitsConfig documents
@scheduler_fn.on_schedule(
    schedule='01 00 * * *',
    timezone=scheduler_fn.Timezone("GMT"),
    region="europe-west1",
)
def updateMatchingLimits(event: scheduler_fn.ScheduledEvent) -> None:
    '''
    Runs daily at midnight GMT, updates all like limits documents with new date
    '''

    db = firestore.client()

    # query all likeLimitsConfig collection documents, get current day
    query = db.collection('likeLimitsConfig').get()

    now_utc = datetime.now(timezone.utc)
    today = datetime.now()

    # Create a datetime object for midnight today in UTC
    last_monday = today - timedelta(days=today.weekday())

    today_string = today.strftime('%Y%m%d')
    monday_string = last_monday.strftime('%Y%m%d')

    # loop over documents, for each prepare update

    batch = db.batch()

    for d in query:
        batch.update(d.reference, {"server_timestamp_string": today_string,
                                 "monday_timestamp_string": monday_string})

    # update in batch
    batch.commit()

# UPGRADE TO EVOLVED LIKE: Upgrades an existing like to evolved status
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.MB_512, enforce_app_check=True)
def upgradeToEvolvedLike(req: https_fn.CallableRequest) -> Any:
    """
         Upgrades an existing like to evolved status

         :param req: Data includes "likeId" (string)
         :return: None
    """
    if req.auth.uid == None:
        return

    uid = req.auth.uid
    match_ref = req.data["likeId"]

    db = firestore.client()
    user_ref = db.collection("users").document(uid)
    user_doc = user_ref.get()
    user_data = user_doc.to_dict()
    remaining_aEvolvedLikesLeft = user_data.get("aEvolvedLikesLeft", 0)

    # Updates like to evolved
    db.collection("likes").document(match_ref).update({"evolvedLike": True})

    # If user is using aEvolvedLikes, decrement their count
    if remaining_aEvolvedLikesLeft > 0:
        db.collection("users").document(uid).update({"aEvolvedLikesLeft": firestore.Increment(-1)})

    return

# UPON LIKE CREATION: Updates every likes document as it is created to have all necessary fields for all possible queries
@on_document_created(document="likes/{docId}", region="europe-west1", memory=options.MemoryOption.GB_2, timeout_sec=180)
def uponLikeCreation(event: Event[DocumentSnapshot]) -> None:
    """
             Updates every likes document as it is created to have all necessary fields for all possible queries

             :param event: Triggered cloud function that brings an event
             :return: None
    """
    db = firestore.client()
    like_id = event.data.id

    liking_user = event.data.to_dict().get("likingUser")
    liking_user_data = liking_user.get().to_dict()

    # gets the liking user's region + adds the right timestamp to the likes document
    liking_user_location = liking_user_data.get("wlRegion")
    findom = liking_user_data.get("findom", False)

    db.collection("likes").document(like_id).update({"timestamp": firestore.SERVER_TIMESTAMP, "likingUserWlRegion": liking_user_location, "findom": findom})

    like_doc = db.collection("likes").document(like_id).get()
    like_data = like_doc.to_dict()
    liked_user = like_data.get("likedUser").get()
    liked_user_data = liked_user.to_dict()
    filter_data = get_like_filter_data(liked_user_data, liking_user_data)
    filter_data.update(
        {
            "like": like_doc.reference,
            "likedUser": liked_user.reference,
            "mutual": like_data.get("mutual"),
            "unmatched": like_data.get("unmatched"),
            "timestamp": like_data.get("timestamp"),
        }
    )
    db.collection('likeFilters').document(like_id).set(filter_data)


@https_fn.on_call(region="europe-west1", timeout_sec=360, memory=options.MemoryOption.GB_2, concurrency=10, enforce_app_check=True)
def generateMatchesAdvanced(req: https_fn.CallableRequest) -> Any:
    """
            Suggests new other users to calling user, uses Postgres suggestion algorithm
            :param req: Callable request incl. user auth information - no extra params
            :return: None
    """
    def older_than_two_weeks(date):
        # Convert date to UTC if it is not already timezone-aware
        if date.tzinfo is None:
            date = date.replace(tzinfo=timezone.utc)
        else:
            date = date.astimezone(timezone.utc)

        now = datetime.now(timezone.utc)

        # determine date 14 days ago
        delta = now - timedelta(days=2)

        return date < delta

    def filter_profiles_locally_basis(profiles_to_filter, passed_profiles_f, blocked_profiles_f, liked_profiles_f,
                                      blocked_phone_numbers_f, own_uid):
        for profile in profiles_to_filter[:]:
            p_uid = profile.get("uid")
            p_phone = profile.get("phone_number")
            if len(passed_profiles_f) > 0:
                if p_uid in passed_profiles_f:
                    profiles_to_filter.remove(profile)
                    continue
            if len(blocked_profiles_f) > 0:
                if p_uid in blocked_profiles_f:
                    profiles_to_filter.remove(profile)
                    continue
            if len(liked_profiles_f) > 0:
                if p_uid in liked_profiles_f:
                    profiles_to_filter.remove(profile)
                    continue
            if len(blocked_phone_numbers_f) > 0:
                if mh.is_phone_blocked(blocked_phone_numbers_f, p_phone):
                    profiles_to_filter.remove(profile)
                    continue
            if p_uid == own_uid:
                profiles_to_filter.remove(profile)
                continue
        return profiles_to_filter

    uid = req.auth.uid

    global db_conn_new
    global db_conn_new_replica

    if db_conn_new == None:
        db_conn_new = connect_with_connector_persist()

    if db_conn_new_replica == None:
        db_conn_new_replica = connect_with_connector_replica_persist("greenrocks-9abc3:europe-west1:chyrpe-prod1-replica")

    # get user document of calling user
    db = firestore.client()
    users_collection = db.collection("users")
    user_ref = users_collection.document(uid)
    user_ref.update({"sawEverybody": False})

    user_doc = user_ref.get()
    user_data = user_doc.to_dict()

    user_sign_up_finished = user_data.get("signUpFinished", False)
    user_paused = user_data.get("paused", False)

    smooth_swiping = req.data.get('smoothSwiping', False)

    if (not user_sign_up_finished) or (user_paused):
        return

    # get passed profiles to determine their status & remove unmatching status if necessary

    user_passed_profiles = user_data.get("passedProfiles", [])

    for profile in user_passed_profiles[:]:
        pass_date = profile.get("date", datetime.now())
        if older_than_two_weeks(pass_date):
            user_passed_profiles.remove(profile)

    # determine vital user characteristics for matching algo

    user_phone = user_data.get("phone_number", "")
    user_role = user_data.get("position", "")
    user_gender = user_data.get("gender", "")

    user_blocked_profiles = user_data.get("blockedProfiles", [])
    user_liked_profiles = user_data.get("likedProfiles", [])
    user_blocked_phones = user_data.get("blockedContactNumberOnly", [])

    user_liked_uids_og = [lp.get('uid') for lp in user_liked_profiles if len(user_liked_profiles) > 0]
    user_liked_uids = copy.deepcopy(user_liked_uids_og)


    user_liked_uids = update_liked_profiles(user_ref, user_liked_profiles, uid)

    user_passed_uids = [pp.get('uid') for pp in user_passed_profiles if len(user_passed_profiles) > 0]
    user_role_matching = user_data.get("roleMatching", False)

    feedback_agent_ref = users_collection.document("feedbackAgent").collection("publicProfile").document(
        "feedbackAgent")

    user_blocked_uids = [bp.get('uid') for bp in user_blocked_profiles if len(user_blocked_profiles) > 0]
    user_incognito_likes = user_data.get("incognitoLikedBy", [])

    user = mh.UserPreferences.from_user_doc(user_data)
    encoding = mh.create_one_multi_hot_vector_requirements(user)

    user_local = user_data.get("localMatching", False)
    user_likes_given = user_data.get("likesGiven", 0)
    user_dislikes_given = user_data.get("dislikesGiven", 0)
    user_total_interactions = user_likes_given + user_dislikes_given

    age_lower = user.age_range.get("lower")
    age_upper = user.age_range.get("upper")
    distance = user.distance * 1000

    print(user)

    if not user_local:
        distance = 10000000

    try:

        logging.info(f"Attempting to update user in SQL: calling user: {uid}")

        user_long = user_data.get('location').longitude
        user_lat = user_data.get('location').latitude

        @with_retry()
        def user_update_1(pool):
            with pool.connect() as db_conn:
                stmt = sqlalchemy.text(
                    """SELECT update_user(p_user_id => :user_id, p_gender => :gender, p_role => :role, p_gender_preference => :gender_preference,
                    p_longitude => :longitude, p_latitude => :latitude, p_upper_age_band => :upper_age_band, p_lower_age_band => :lower_age_band,
                    p_proximity_preference => :proximity_preference, p_match_status => :match_status, p_incognito => :incognito,
                    p_latest_suggestions => :latest_suggestions, p_findom_interest => :findom_interest)""")

                # Execute with the parameters
                result = db_conn.execute(stmt, {
                    'user_id': uid,
                    'gender': user_data.get('gender'),
                    'role': user_data.get('position').capitalize(),
                    'latest_suggestions': datetime.now().strftime("%Y-%m-%d"),
                    'longitude': round(user_long, 2),
                    'latitude': round(user_lat, 2),
                    'upper_age_band': user_data.get('upperAgeReq'),
                    'lower_age_band': user_data.get('lowerAgeReq'),
                    'proximity_preference': 10000000 if (
                            not user_local or distance > 500000) else distance,  # in m, not in km
                    'gender_preference': 'Female' if user_data.get('genderReq') == 'Women' else (
                        'Male' if user_data.get('genderReq') == 'Men' else 'Everyone'),
                    'match_status': 'Neither' if user_paused else (
                        'RegionalMatch' if user_local else 'GlobalMatch'),
                    'incognito': user_data.get('incognito'),
                    'findom_interest': get_findom_interest_status(user_data.get('kinks', []), user_data.get('professionalsReq', False))
                }).fetchall()

                db_conn.commit()
                logging.info(f"Committed updated user in SQL: calling user: {uid}")

        user_update_1(db_conn_new)

    except Exception as e:
        logging.exception(e)
        db.collection('SQLExceptions').add(
            {'function': 'generateMatchesAdvanced -> user update', 'user': user_data.get('uid'), 'error': str(e),
             'time': firestore.SERVER_TIMESTAMP})


    selected_params_dict = {}

    local_global_dict_component = "local" if user_local else "global"
    swipe_dict_component = "lt40" if user_total_interactions < 40 else "lt60" if user_total_interactions < 60 else "gte60"

    selected_params_dict = mh.params_dict[local_global_dict_component][swipe_dict_component]

    # special parameters for new women
    suggestion_uids = []

    print(user)

    result = []

    special_womens_dict = {
        'highest_elo_limit': 5, 'lowest_elo_limit': 0, 'similar_elo_limit': 0,
        'new_people_limit': 0,
        'most_active_limit': 0,
        'geographically_close_limit': 55, 'liked_by_limit': 5, 'highest_elo_weight': 1,
        'lowest_elo_weight': 0, 'similar_elo_weight': 0.0, 'new_people_weight': 0.0, 'most_active_weight': 0.0,
        'geographically_close_weight': 0.0, 'liked_by_weight': 0.0
    }

    eligible_woman = (user_gender == "Female") and (user_total_interactions < 40)

    eligible_dicts = [selected_params_dict]

    if eligible_woman:
        logging.info("eligible woman swiping")
        eligible_dicts.insert(0, special_womens_dict)

    print(eligible_dicts)

    for curr_dict in eligible_dicts:

        print(curr_dict)

        for i in range(0, 5):

            try:
                if not (user.dealbreakers.get("distance") == True) and i == 3:
                    distance = 100000000

                if eligible_woman:
                    distance = 100000

            except Exception as e:
                logging.exception(e)

            try:
                result = mh.call_suggest_users_function(
                    pool=random.choice([db_conn_new,db_conn_new_replica]),
                    user_id=uid,
                    highest_elo_limit=curr_dict.get("highest_elo_limit"),
                    lowest_elo_limit=curr_dict.get("lowest_elo_limit"),
                    similar_elo_limit=curr_dict.get("similar_elo_limit"),
                    new_people_limit=curr_dict.get("new_people_limit"),
                    most_active_limit=curr_dict.get("most_active_limit"),
                    geographically_close_limit=curr_dict.get("geographically_close_limit"),
                    liked_by_limit=curr_dict.get("liked_by_limit"),
                    highest_elo_weight=curr_dict.get("highest_elo_weight"),
                    lowest_elo_weight=curr_dict.get("lowest_elo_weight"),
                    similar_elo_weight=curr_dict.get("similar_elo_weight"),
                    new_people_weight=curr_dict.get("new_people_weight"),
                    most_active_weight=curr_dict.get("most_active_weight"),
                    geographically_close_weight=curr_dict.get("geographically_close_weight"),
                    liked_by_weight=curr_dict.get("liked_by_weight"),
                    vector_req=encoding,
                    vector_threshold=mh.auto_expansion_factors[i]["threshold"],
                    include_role=user.role_matching,
                    role_req=user.role_req,
                    exclude_professional=user.hide_findoms,
                    relationship_type_req=user.relationship_type if (user.dealbreakers["relationship_type"] is True) else None,
                    # Example list
                    min_age=age_lower if (user.dealbreakers["age"] is True) else max(18, int(user.age_range.get("lower") -
                                                                                             mh.auto_expansion_factors[i]["age"])),
                    max_age=age_upper if (user.dealbreakers["age"] is True) else min(99, int(user.age_range.get("upper") +
                                                                                             mh.auto_expansion_factors[i]["age"])),
                    max_distance=distance if (user.dealbreakers["distance"] is True) else (
                                distance * mh.auto_expansion_factors[i]["distance"]),
                    min_height=user.height_range.get("lower") if (user.dealbreakers["height"] is True) else None,
                    # NULL in the query
                    max_height=user.height_range.get("upper") if (user.dealbreakers["height"] is True) else None,
                    # NULL in the query
                    ethnicity_req=user.ethnicity if (user.dealbreakers["ethnicity"] is True) else None,  # NULL in the query
                    religions_req=user.religion if (user.dealbreakers["religion"] is True) else None,
                    edu_req=user.education_level if (user.dealbreakers["education_level"] is True) else None,
                    children_req=user.children if (user.dealbreakers["children"] is True) else None,
                    family_req=user.family_plans if (user.dealbreakers["family_plans"] is True) else None,
                    politics_req=user.politics if (user.dealbreakers["politics"] is True) else None,
                    drinking_req=user.drinking if (user.dealbreakers["drinking"] is True) else None,
                    smoking_req=user.smoking if (user.dealbreakers["smoking"] is True) else None,
                    findom_preferred=user.findom_preferred
                )

                logging.info(f"try {i}, {len(result)} length")

            except Exception as e:
                logging.exception(e)
                db.collection('SQLExceptions').add(
                    {'function': 'generateMatchesAdvanced -> matching function 1', 'user': user_data.get('uid'), 'error': str(e),
                     'time': firestore.SERVER_TIMESTAMP})


            if len(result) > 2:
                break

        if len(result) > 2:
            break


    for i, row in enumerate(result):
        suggestion_uids.append(row._mapping.get('suggested_user_uid'))

        if i > 40:
            break

    if user_data.get("findom"):

        findom_suggested_uids = generateFindomMatches(eligible_dicts, user, uid, db, eligible_woman, encoding, age_lower, age_upper, distance)

        suggestion_uids_to_cover = copy.deepcopy(suggestion_uids)[:int(len(suggestion_uids)/2)]
        findom_uids_to_cover = copy.deepcopy(findom_suggested_uids)[:int(len(suggestion_uids)/2)]

        findom_uids_to_cover.extend(suggestion_uids_to_cover)

        suggestion_uids = findom_uids_to_cover


    logging.info(f"suggestion uids: {len(suggestion_uids)}")

    if user.likes_bypass_filters and len(suggestion_uids) > 10:

        logging.info(f"Start match bypass: {len(suggestion_uids)}")

        try:
            results_2 = mh.call_suggest_liked_you_function(random.choice([db_conn_new,db_conn_new_replica]), uid)
            for row in results_2:
                suggestion_uids.append(row._mapping.get('suggested_user_uid'))

        except Exception as e:
            logging.exception(e)
            db.collection('SQLExceptions').add(
                {'function': 'generateMatchesAdvanced -> matching function 2', 'user': user_data.get('uid'), 'error': str(e),
                 'time': firestore.SERVER_TIMESTAMP})

        logging.info(f"End match bypass: {len(suggestion_uids)}")

    if len(suggestion_uids) == 0:
        user_ref.update({"matchingSuggestions": [], "sawEverybody": True,
                         "passedProfiles": user_passed_profiles,
                         "latestMatchesGenerated": firestore.SERVER_TIMESTAMP,
                         "incognitoLikedBy": user_incognito_likes})

        logging.info("no suggestions")

        return

    shuffled_uids = mh.shuffle_uids(suggestion_uids)

    try:
        logging.info(f"pre removal of duplicate suggestions: {len(shuffled_uids)}")
        # remove previously liked or passed uids - extra backstop due to recent sync challenges
        for u in shuffled_uids[:]:
            if u in user_liked_uids or u in user_passed_uids:
                shuffled_uids.remove(u)
        logging.info(f"post removal of duplicate suggestions: {len(shuffled_uids)}")
    except Exception as e:
        logging.exception(e)

    # get full profiles from UIDs
    user_profiles = mh.get_profiles_from_uids(db, shuffled_uids)

    # give people with activated kink filter an advantage

    if user_gender == "Female" and user_role.lower() == "dominant":

        try:

            move_profiles = []

            for i, u in enumerate(user_profiles[:]):  # Iterate over a copy to avoid modification issues
                if i > 5 and u.get("kinksSimilarityReq", False):
                    move_profiles.append((i, user_profiles.pop(i)))

            for i, profile in move_profiles:
                new_index = max(i - 2, 0)  # Ensure index doesn't go negative
                user_profiles.insert(new_index, profile)

        except Exception as e:
            logging.exception(e)

    logging.info(f"user profiles gotten: {len(user_profiles)}, uid: {uid}")

    necessary_filtered_profiles = filter_profiles_locally_basis(user_profiles, user_passed_uids,
                                                                user_blocked_uids, user_liked_uids,
                                                                user_blocked_phones, uid)

    # filter profiles
    filtered_profiles = mh.filter_blocked_profiles(
        uid,
        user_phone,
        necessary_filtered_profiles,
        user_blocked_uids,
        user_blocked_phones
    )

    logging.info(f"Blocked profiles filter returned uids: {len(filtered_profiles)}")

    return_filtered_profiles = [{"publicProfile": p.get('publicProfile'), "uid": p.get("uid")} for p in
                                filtered_profiles]

    # if enough profiles were found, write to user doc and return
    if len(filtered_profiles) >= 1:
        if not smooth_swiping:
            return_filtered_profiles.insert(0, {"publicProfile": feedback_agent_ref, "uid": "feedbackAgent"})
        user_ref.update({"matchingSuggestions": return_filtered_profiles,
                         "passedProfiles": user_passed_profiles,
                         "latestMatchesGenerated": firestore.SERVER_TIMESTAMP,
                         "incognitoLikedBy": user_incognito_likes, "likedProfiles": []})

    else:
        user_ref.update({"matchingSuggestions": [], "sawEverybody": True,
                         "passedProfiles": user_passed_profiles,
                         "latestMatchesGenerated": firestore.SERVER_TIMESTAMP,
                         "incognitoLikedBy": user_incognito_likes, "likedProfiles": []})





def checkChatLimit(uid: str):
    """
    Checks if the user has reached their chat limit.
    :param uid: The user's UID.
    :return: None
    """
    db = firestore.client()
    user_ref = db.collection("users").document(uid)
    likes_collection = db.collection("likes")

    # All active chats where the user was the last one to send a message
    query = likes_collection.where(filter=FieldFilter("recentMessageSender", "==", user_ref)) \
        .where(filter=FieldFilter("mutual", "==", True)) \
        .where(filter=FieldFilter("unmatched", "==", False)) \
        .where(filter=FieldFilter("diamondLike", "==", False)) \
        .where(filter=FieldFilter("involvedUsersProfileRefOnly", "array_contains", uid))


    count = query.count()

    # if count > inactiveChats:
    user_ref.update({"unansweredChats": count})
    # else:
    #     user_ref.update({"hasReachedChatLimit": False})

# GENERATE MATCHES: Suggests new other users to calling user, uses Postgres suggestion algorithm
@https_fn.on_call(region="europe-west2", timeout_sec=300, memory=options.MemoryOption.GB_2, cpu=2, concurrency=1, enforce_app_check=True)
def generateMatches(req: https_fn.CallableRequest) -> Any:
    """
            Suggests new other users to calling user, uses Postgres suggestion algorithm
            :param req: Callable request incl. user auth information - no extra params
            :return: None
    """
    def older_than_two_weeks(date):
        # Convert date to UTC if it is not already timezone-aware
        if date.tzinfo is None:
            date = date.replace(tzinfo=timezone.utc)
        else:
            date = date.astimezone(timezone.utc)

        now = datetime.now(timezone.utc)

        # determine date 14 days ago
        delta = now - timedelta(days=5)

        return date < delta

    def is_phone_blocked(phone_list, phone_number):
        if phone_number != None:
            for phone in phone_list:
                match = phonenumbers.is_number_match(phone_number, phone)
                if match == 4 or match == 3:
                    return True

        return False

    def filter_profiles_locally_basis(profiles_to_filter, passed_profiles_f, blocked_profiles_f, liked_profiles_f,
                                      blocked_phone_numbers_f, own_uid):
        for profile in profiles_to_filter[:]:
            p_uid = profile.get("uid")
            p_phone = profile.get("phone_number")
            if len(passed_profiles_f) > 0:
                if p_uid in passed_profiles_f:
                    profiles_to_filter.remove(profile)
                    continue
            if len(blocked_profiles_f) > 0:
                if p_uid in blocked_profiles_f:
                    profiles_to_filter.remove(profile)
                    continue
            if len(liked_profiles_f) > 0:
                if p_uid in liked_profiles_f:
                    profiles_to_filter.remove(profile)
                    continue
            if len(blocked_phone_numbers_f) > 0:
                if is_phone_blocked(blocked_phone_numbers_f, p_phone):
                    profiles_to_filter.remove(profile)
                    continue
            if p_uid == own_uid:
                profiles_to_filter.remove(profile)
                continue
        return profiles_to_filter

    def filter_profiles_locally_characs(profiles_to_filter, min_age, max_age):
        for profile in profiles_to_filter[:]:
            profile_age = profile.get("age")
            if profile_age >= min_age and profile_age <= max_age:
                continue
            else:
                profiles_to_filter.remove(profile)

        return profiles_to_filter

    def filter_profiles_premium(profiles_to_filter, interests_req_f, lower_height_req_f, upper_height_req_f):

        for profile in profiles_to_filter[:]:
            public_profile = profile.get("publicProfile").get().to_dict()
            profile_hobbies = public_profile.get("hobbies", [])
            profile_height = public_profile.get("height", False)
            if len(interests_req_f) > 0:
                if len(profile_hobbies) > 0:
                    if not [any(x in element for x in profile_hobbies) for element in interests_req_f]:
                        profiles_to_filter.remove(profile)

                        continue
            if profile_height != False:
                if profile_height >= lower_height_req_f and profile_height <= upper_height_req_f:
                    continue
                else:
                    profiles_to_filter.remove(profile)
                    continue

        return profiles_to_filter

    uid = req.auth.uid

    # get user document of calling user
    db = firestore.client()
    users_collection = db.collection("users")
    user_ref = users_collection.document(uid)
    user_ref.update({"sawEverybody": False})

    user_doc = user_ref.get()
    user_data = user_doc.to_dict()

    user_sign_up_finished = user_data.get("signUpFinished", False)

    if not user_sign_up_finished:
        return

    # get passed profiles to determine their status & remove unmatching status if necessary

    user_passed_profiles = user_data.get("passedProfiles", [])

    for profile in user_passed_profiles[:]:
        pass_date = profile.get("date", datetime.now())
        if older_than_two_weeks(pass_date):
            user_passed_profiles.remove(profile)


    # determine vital user characteristics for matching algo

    user_gender = user_data.get("gender", "Male")
    user_gender_req = user_data.get("genderReq", "Men")
    user_local = user_data.get("localMatching", False)
    user_region = user_data.get("wlRegion", "Other")
    user_global = user_data.get("globalMatching", False)
    user_likes_given = user_data.get("likesGiven", 0)
    user_dislikes_given = user_data.get("dislikesGiven", 0)
    user_total_interactions = user_likes_given + user_dislikes_given
    user_distance_req = user_data.get("distanceReq", 100000) * 1000
    user_professionals_req = user_data.get("professionalsReq", False)
    user_findom = user_data.get("findom", False)

    user_min_age = user_data.get("lowerAgeReq", 18)
    user_max_age = user_data.get("upperAgeReq", 99)

    user_pnMa = user_data.get("pnMa", None)
    user_pnMe = user_data.get("pnMe", None)

    publicProfile = user_data.get("publicProfile")

    future_pnMa = False if (user_pnMa == False) else True
    future_pnMe = False if (user_pnMe == False) else True

    user_blocked_profiles = user_data.get("blockedProfiles", [])
    user_liked_profiles = user_data.get("likedProfiles", [])
    user_blocked_phones = user_data.get("blockedContactNumberOnly", [])

    user_blocked_uids = [bp.get('uid') for bp in user_blocked_profiles if len(user_blocked_profiles) > 0]
    user_liked_uids = [lp.get('uid') for lp in user_liked_profiles if len(user_liked_profiles) > 0]
    user_passed_uids = [pp.get('uid') for pp in user_passed_profiles if len(user_passed_profiles) > 0]
    user_incognito_likes = user_data.get("incognitoLikedBy", [])
    user_role_matching = user_data.get("roleMatching", False)

    user_saved_profiles = user_data.get('savedSuggestions', [])
    user_lastSaved_profiles = user_data.get('lastSavedSuggestions', [])

    @with_retry()
    def get_connector_and_pool_with_retry():
        connector = Connector(refresh_strategy="lazy")
        pool = connect_with_connector(connector)
        return connector, pool

    connector, pool = get_connector_and_pool_with_retry()

    @with_retry()
    def save_user_profiles_with_retry():
        try:
            with pool.connect() as db_conn:
                if user_saved_profiles != user_lastSaved_profiles:
                    for saved_user in user_saved_profiles:
                        logging.info(f"Attempting to save user in SQL: {str(saved_user.get('uid'))}, calling user: {uid}")

                        if saved_user in user_lastSaved_profiles:
                            continue
                    # update saved profiles in Postgres
                    try:
                        stmt = sqlalchemy.text(
                            """SELECT insert_suggestion_wr_now(:user_id, :suggested_user_id, :suggestion_outcome)""")

                        # Execute with the parameters
                        result = db_conn.execute(stmt, {
                            'user_id': uid,
                            'suggested_user_id': saved_user.get('uid'),
                            'suggestion_outcome': "Saved"}).fetchall()

                        db_conn.commit()

                        logging.info(f"Committed saved user in SQL: {str(saved_user.get('uid'))}, calling user: {uid}")

                    except Exception as e:
                        logging.exception(e)
                        db.collection('SQLExceptions').add(
                            {'function': 'generateMatches -> saving', 'user': uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

        except:
            print("Fail Generate Matches")
    
    save_user_profiles_with_retry()

    # update user in Postgres
    if user_data.get('paused', False) == True:
        paused_status = True
    else:
        paused_status = False

    if user_data.get('globalMatching', False) == True:
        global_status = True
    else:
        global_status = False

    if user_data.get('localMatching', False) == True:
        regional_status = True
    else:
        regional_status = False

    # updating current user document
    try:
        @with_retry()
        def update_user_with_retry():
            try:

                logging.info(f"Attempting to update user in SQL: calling user: {uid}")

                user_long = user_data.get('location').longitude
                user_lat = user_data.get('location').latitude

                with pool.connect() as db_conn:

                    stmt = sqlalchemy.text(
                        """SELECT update_user(p_user_id => :user_id, p_gender => :gender, p_role => :role, p_gender_preference => :gender_preference, 
                        p_longitude => :longitude, p_latitude => :latitude, p_upper_age_band => :upper_age_band, p_lower_age_band => :lower_age_band,
                        p_proximity_preference => :proximity_preference, p_match_status => :match_status, p_incognito => :incognito,
                        p_latest_suggestions => :latest_suggestions)""")

                    # Execute with the parameters
                    result = db_conn.execute(stmt, {
                        'user_id': uid,
                        'gender': user_data.get('gender'),
                        'role': user_data.get('position').capitalize(),
                        'latest_suggestions': datetime.now().strftime("%Y-%m-%d"),
                        'longitude': round(user_long, 2),
                        'latitude': round(user_lat, 2),
                        'upper_age_band': user_data.get('upperAgeReq'),
                        'lower_age_band': user_data.get('lowerAgeReq'),
                        'proximity_preference': 10000000 if (global_status or user_distance_req > 500000) else user_distance_req,  # in m, not in km
                        'gender_preference': 'Female' if user_data.get('genderReq') == 'Women' else (
                            'Male' if user_data.get('genderReq') == 'Men' else 'Everyone'),
                        'match_status': 'Neither' if paused_status else ('Both' if regional_status and global_status else (
                            'RegionalMatch' if regional_status else 'GlobalMatch')),
                        'incognito': user_data.get('incognito')}).fetchall()

                    db_conn.commit()
                    logging.info(f"Committed updated user in SQL: calling user: {uid}")

            except Exception as e:
                logging.exception(e)
                db.collection('SQLExceptions').add(
                    {'function': 'generateMatches -> user update', 'user': user_data.get('uid'), 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

        update_user_with_retry()
    except:
        print("Fail Generate Matches")


    if user_findom == True:
        try:
            @with_retry()
            def update_user_findom_with_retry():
                try:

                    logging.info(f"Attempting to update user findom in SQL: calling user: {uid}")

                    with pool.connect() as db_conn:

                        stmt = sqlalchemy.text(
                            """SELECT add_professional(p_user_id => :user_id)""")

                        # Execute with the parameters
                        result = db_conn.execute(stmt, {
                            'user_id': uid,
                        }).fetchall()

                        db_conn.commit()
                        logging.info(f"Committed updated user in SQL: calling user: {uid}")

                except Exception as e:
                    logging.exception(e)
                    db.collection('SQLExceptions').add(
                        {'function': 'generateMatches -> findom update', 'user': user_data.get('uid'), 'error': str(e),
                        'time': firestore.SERVER_TIMESTAMP})

            update_user_findom_with_retry()

        except:
            print("Fail Generate Matches")


    # fetch additional requirements for women

    user_min_height_req = user_data.get("lowerHeightReq", 0)
    user_max_height_req = user_data.get("upperHeightReq", 250)
    user_hobbies_req = user_data.get("hobbiesToSeeReq", [])
    user_evolved_subscriber = user_data.get("evolvedSubscriber", False)

    feedback_agent_ref = users_collection.document("feedbackAgent").collection("publicProfile").document(
        "feedbackAgent")

    # generate new matches in Postgres
    uid_set_for_filters = []

    if user_local:

        @with_retry()
        def get_local_matches_from_db_with_retry():
            """
            Gets new suggestions with local matching parameters
            :return: Set of local matching suggestions for user
            """

            uid_set_for_filters_temp = []

            with pool.connect() as db_conn:
                stmt = sqlalchemy.text(
                    """SELECT suggest_users_weighted_returnonly(p_user_id => :p_user_id, highest_elo_limit => :highest_elo_limit, lowest_elo_limit => :lowest_elo_limit, 
                    similar_elo_limit => :similar_elo_limit, new_people_limit => :new_people_limit, most_active_limit => :most_active_limit, 
                    geographically_close_limit => :geographically_close_limit, liked_by_limit => :liked_by_limit, highest_elo_weight => :highest_elo_weight, 
                    lowest_elo_weight => :lowest_elo_weight, similar_elo_weight => :similar_elo_weight, new_people_weight => :new_people_weight, most_active_weight => :most_active_weight, 
                    geographically_close_weight => :geographically_close_weight, liked_by_weight => :liked_by_weight, include_role => :include_role, exclude_professional => :exclude_professional)""")

                # Execute with the parameters

                # new users = attractive people
                # after 40 swipes = adjusts to your score with some variance

                if user_total_interactions < 40:

                    result = db_conn.execute(stmt, {
                        'p_user_id': uid,
                        'highest_elo_limit': 5, 'lowest_elo_limit': 0, 'similar_elo_limit': 0,
                        'new_people_limit': 5,
                        'most_active_limit': 5,
                        'geographically_close_limit': 35, 'liked_by_limit': 5, 'highest_elo_weight': 1,
                        'lowest_elo_weight': 0, 'similar_elo_weight': 0, 'new_people_weight': 0, 'most_active_weight': 0,
                        'geographically_close_weight': 1, 'liked_by_weight': 0, 'include_role': user_role_matching, 'exclude_professional': user_professionals_req}).fetchall()
                    # result = db_conn.execute(sqlalchemy.func.add_user('user_example', 23, datetime.datetime(2001, 8, 1), Gender.Female, Role.Dominant, GenderPreference.Male, 0.1291, -43.1923)).fetchall()

                elif user_total_interactions < 60:

                    result = db_conn.execute(stmt, {
                        'p_user_id': uid,
                        'highest_elo_limit': 5, 'lowest_elo_limit': 0, 'similar_elo_limit': 5,
                        'new_people_limit': 10,
                        'most_active_limit': 10,
                        'geographically_close_limit': 15, 'liked_by_limit': 5, 'highest_elo_weight': 1.15,
                        'lowest_elo_weight': 0, 'similar_elo_weight': 0.25, 'new_people_weight': 0.75,
                        'most_active_weight': 0,
                        'geographically_close_weight': 5, 'liked_by_weight': 0.125, 'include_role': user_role_matching, 'exclude_professional': user_professionals_req}).fetchall()

                else:

                    result = db_conn.execute(stmt, {
                        'p_user_id': uid,
                        'highest_elo_limit': 3, 'lowest_elo_limit': 0, 'similar_elo_limit': 10,
                        'new_people_limit': 10,
                        'most_active_limit': 10,
                        'geographically_close_limit': 15, 'liked_by_limit': 5, 'highest_elo_weight': 0.2,
                        'lowest_elo_weight': 0, 'similar_elo_weight': 0.1, 'new_people_weight': 1,
                        'most_active_weight': 1,
                        'geographically_close_weight': 2.3, 'liked_by_weight': 0.1, 'include_role': user_role_matching, 'exclude_professional': user_professionals_req}).fetchall()

                db_conn.commit()

                # Append returned user ids to the output for this function
                for row in result:
                    uid_set_for_filters_temp.append(row._mapping.get('suggest_users_weighted_returnonly'))

                return uid_set_for_filters_temp

        @with_retry()
        def update_user_proximity_with_retry(new_proximity):
            """
            (Invisibly to the user) expands search proximity in SQL if necessary
            :param new_proximity: New proximity in m
            :return: None
            """
            with pool.connect() as db_conn:
                stmt = sqlalchemy.text(
                    """SELECT update_user(p_user_id => :p_user_id, p_proximity_preference => :proximity_preference)""")

                # Execute with the parameters
                result = db_conn.execute(stmt, {
                    'p_user_id': uid,
                    'proximity_preference': new_proximity,  # in m, not in km
                    }).fetchall()

                db_conn.commit()
                logging.info(f"Committed updated user in SQL: calling user: {uid}")

        uid_set_for_filters = get_local_matches_from_db_with_retry()

        # exponentially increases user's proximity settings if noone found in immediate, original proximity, up to global
        if len(uid_set_for_filters) < 3:
            update_user_proximity_with_retry(user_distance_req*2)
            uid_set_for_filters = get_local_matches_from_db_with_retry()

        if len(uid_set_for_filters) < 3:
            update_user_proximity_with_retry(user_distance_req*5)
            uid_set_for_filters = get_local_matches_from_db_with_retry()

        if len(uid_set_for_filters) < 3:
            update_user_proximity_with_retry(user_distance_req*10)
            uid_set_for_filters = get_local_matches_from_db_with_retry()

        if len(uid_set_for_filters) < 3:
            update_user_proximity_with_retry(user_distance_req*20)
            uid_set_for_filters = get_local_matches_from_db_with_retry()

        if len(uid_set_for_filters) < 3:
            update_user_proximity_with_retry(user_distance_req*1000)
            uid_set_for_filters = get_local_matches_from_db_with_retry()

    else:

        # generating suggestions for globally matching users (and paused)
        @with_retry()
        def generate_suggestions_with_retry():
            try:

                with pool.connect() as db_conn:
                    stmt = sqlalchemy.text(
                        """SELECT suggest_users_weighted_returnonly(p_user_id => :p_user_id, highest_elo_limit => :highest_elo_limit, lowest_elo_limit => :lowest_elo_limit, 
                        similar_elo_limit => :similar_elo_limit, new_people_limit => :new_people_limit, most_active_limit => :most_active_limit, 
                        geographically_close_limit => :geographically_close_limit, liked_by_limit => :liked_by_limit, highest_elo_weight => :highest_elo_weight, 
                        lowest_elo_weight => :lowest_elo_weight, similar_elo_weight => :similar_elo_weight, new_people_weight => :new_people_weight, most_active_weight => :most_active_weight, 
                        geographically_close_weight => :geographically_close_weight, liked_by_weight => :liked_by_weight, include_role => :include_role, exclude_professional => :exclude_professional)""")

                    # Execute with the parameters

                    # new users = attractive people
                    # after 40 swipes = adjusts to your score with some variance

                    if user_total_interactions < 40:

                        logging.info("Global matching for starters invoked")

                        result = db_conn.execute(stmt, {
                            'p_user_id': uid,
                            'highest_elo_limit': 25, 'lowest_elo_limit': 0, 'similar_elo_limit': 0,
                            'new_people_limit': 5,
                            'most_active_limit': 5,
                            'geographically_close_limit': 10, 'liked_by_limit': 5, 'highest_elo_weight': 1.5,
                            'lowest_elo_weight': 0, 'similar_elo_weight': 0, 'new_people_weight': 1, 'most_active_weight': 1,
                            'geographically_close_weight': 0.5, 'liked_by_weight': 0, 'include_role': user_role_matching, 'exclude_professional': user_professionals_req}).fetchall()

                    elif user_total_interactions < 60:

                        result = db_conn.execute(stmt, {
                            'p_user_id': uid,
                            'highest_elo_limit': 10, 'lowest_elo_limit': 0, 'similar_elo_limit': 15,
                            'new_people_limit': 5,
                            'most_active_limit': 10,
                            'geographically_close_limit': 10, 'liked_by_limit': 5, 'highest_elo_weight': 0.5,
                            'lowest_elo_weight': 0, 'similar_elo_weight': 0.25, 'new_people_weight': 0.25,
                            'most_active_weight': 0.25,
                            'geographically_close_weight': 0.5, 'liked_by_weight': 0.25, 'include_role': user_role_matching, 'exclude_professional': user_professionals_req}).fetchall()

                    else:

                        result = db_conn.execute(stmt, {
                            'p_user_id': uid,
                            'highest_elo_limit': 3, 'lowest_elo_limit': 0, 'similar_elo_limit': 20,
                            'new_people_limit': 7,
                            'most_active_limit': 10,
                            'geographically_close_limit': 10, 'liked_by_limit': 5, 'highest_elo_weight': 0.15,
                            'lowest_elo_weight': 0, 'similar_elo_weight': 0.4, 'new_people_weight': 0.25,
                            'most_active_weight': 1,
                            'geographically_close_weight': 0.5, 'liked_by_weight': 0.1, 'include_role': user_role_matching, 'exclude_professional': user_professionals_req}).fetchall()

                    db_conn.commit()

                    # Append returned user ids to the output
                    for row in result:
                        uid_set_for_filters.append(row._mapping.get('suggest_users_weighted_returnonly'))

                    logging.info(f"Postgres returned uids: {len(uid_set_for_filters)}")

            except Exception as e:
                logging.exception(e)
                db.collection('SQLExceptions').add(
                    {'function': 'generateMatches -> SQL matching function', 'user': uid, 'error': str(e)})

            except:
                print("Fail Generate Matches New SQL Matching Function")

        generate_suggestions_with_retry()

    set_for_filters_pre = []
    print(uid_set_for_filters)

    # adding a certain randomness component into the results so that two people trying together don't get same suggestions
    for ps_uid in list(set(uid_set_for_filters)):
        ps_user_doc = db.collection('users').document(ps_uid).get()
        if ps_user_doc.exists:
            ps_user_data = ps_user_doc.to_dict()
            ps_user_publicProfile = ps_user_data.get('publicProfile')
            if ps_user_publicProfile != None:
                set_for_filters_pre.append(ps_user_data)

    set_for_filters_1 = set_for_filters_pre[0:10]
    set_for_filters_2 = set_for_filters_pre[10:40]
    random.shuffle(set_for_filters_1)
    random.shuffle(set_for_filters_2)

    set_for_filters = set_for_filters_1 + set_for_filters_2


    necessary_filtered_profiles = filter_profiles_locally_basis(set_for_filters, user_passed_uids,
                                                                user_blocked_uids, user_liked_uids,
                                                                user_blocked_phones, uid)

    logging.info(f"Necessary filtered profiles returned uids: {len(necessary_filtered_profiles)}")

    characs_filtered_profiles = filter_profiles_locally_characs(necessary_filtered_profiles, user_min_age,
                                                                user_max_age)

    logging.info(f"Characs filtered profiles returned uids: {len(characs_filtered_profiles)}")

    final_filtered_profiles = characs_filtered_profiles

    # Apply premium filters only if woman or evolved subscriber (deprecated, will be removed soon)
    if user_gender == "Female" or user_evolved_subscriber == True:
        if user_min_height_req != 0 and user_max_height_req != 0:
            final_filtered_profiles = filter_profiles_premium(characs_filtered_profiles, user_hobbies_req,
                                                              user_min_height_req, user_max_height_req)
        else:
            final_filtered_profiles = characs_filtered_profiles
    else:
        final_filtered_profiles = characs_filtered_profiles

    return_filtered_profiles = [{"publicProfile": p.get('publicProfile'), "uid": p.get("uid")} for p in
                                final_filtered_profiles]

    logging.info(f"Final filtered profiles returned uids: {len(return_filtered_profiles)}")

    # if enough profiles were found, write to user doc and return
    if len(final_filtered_profiles) >= 1:
        return_filtered_profiles.insert(0, {"publicProfile": feedback_agent_ref, "uid": "feedbackAgent"})
        user_ref.update({"matchingSuggestions": return_filtered_profiles,
                         "passedProfiles": user_passed_profiles, "lastSavedSuggestions": user_saved_profiles,
                         "latestMatchesGenerated": firestore.SERVER_TIMESTAMP,
                         "incognitoLikedBy": user_incognito_likes, "lowerHeightReq": user_min_height_req,
                         "upperHeightReq": user_max_height_req, "pnMa": future_pnMa, "pnMe": future_pnMe})

        publicProfile.update({"pnMa": future_pnMa, "pnMe": future_pnMe})

        try:
            pool.dispose()
            connector.close()
        except Exception as e:
            logging.exception(e)
            db.collection('SQLExceptions').add(
                {'function': 'generateMatches -> disposing db connection', 'user': uid, 'error': str(e),
                 'time': firestore.SERVER_TIMESTAMP})

        return

    else:
        user_ref.update({"matchingSuggestions": [], "sawEverybody": True,
                         "passedProfiles": user_passed_profiles, "lastSavedSuggestions": user_saved_profiles,
                         "latestMatchesGenerated": firestore.SERVER_TIMESTAMP,
                         "incognitoLikedBy": user_incognito_likes, "lowerHeightReq": user_min_height_req,
                         "upperHeightReq": user_max_height_req, "pnMa": future_pnMa, "pnMe": future_pnMe})

        publicProfile.update({"pnMa": future_pnMa, "pnMe": future_pnMe})

    # destroy Postgres connection
    try:
        pool.dispose()
        connector.close()
    except Exception as e:
        logging.exception(e)
        db.collection('SQLExceptions').add(
            {'function': 'generateMatches -> disposing db connection', 'user': uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})


@on_document_created(document="likes/{matchId}/messages/{messageId}", region="europe-west1", timeout_sec=180, min_instances=1,
                     memory=options.MemoryOption.GB_1)
def uponMessageCreation(event: Event[DocumentSnapshot]) -> None:
    db = firestore.client()
    message_data = event.data.to_dict()

    match_id = event.params['matchId']
    message_id = event.params['messageId']

    if 'ogSender' not in message_data:
        return

    # get uid from the ogSender attribute; since this is triggered cf, not available through req
    og_sender = message_data['ogSender']
    uid = og_sender.id

    # get message from event object
    message = message_data['body']

    # get user document
    user_ref = db.collection("users").document(uid)
    user_doc = user_ref.get()
    user_data = user_doc.to_dict()

    user_gender = user_data.get("gender")

    user_name = user_data.get("publicName")

    # retrieves match and finds if message needs to get scanned
    match_ref = db.collection("likes").document(match_id)
    match_doc = match_ref.get()
    match_data = match_doc.to_dict()

    mutual = match_data.get("mutual", False)

    # retrieves other user data for scanning & notification, typo is on purpose
    other_uid = [u for u in match_data.get('involedUIDs') if u != uid][0]
    other_doc = db.collection('users').document(other_uid).get()

    other_data = other_doc.to_dict()
    other_gender = other_data.get('gender')
    other_name = other_data.get('publicName')
    other_full_paused = other_data.get('fullPaused')

    # determine if message needs to get scanned
    nsfw_allowed = match_data.get("nsfwAllow", False)
    short_messages_allow = match_data.get("shortMessagesAllow", False)
    name_allow = match_data.get("nameAddressAllow", False)

    is_male = user_gender == "Male"

    scan_need = is_male and not nsfw_allowed
    short_scan_need = is_male and not short_messages_allow
    name_scan_need = is_male and not name_allow

    import ModerationHelper as mh

    def fail_message_send(reason):
        db.collection('likes').document(match_id).collection('messages').document(
            message_id).update({
            'processing': False,
            'failedSymbol': True,
            'messageHint': reason
        })

    fail_checks = [
        (scan_need and not mh.moderate_content_leaner(message),
         "This message could not be sent. Explicit messages are currently disallowed in the chat."),
        (short_scan_need and len(message) < 10,
         "This message could not be sent. Short messages are currently disabled in the chat."),
        (name_scan_need and other_name in message,
         f"This message could not be sent. You are currently not allowed to use {other_name}'s name in the chat."),
        (other_full_paused,
         f"This message could not be sent. {other_name} has paused all their likes and chats.")
    ]

    for condition, reason in fail_checks:
        if condition:
            fail_message_send(reason)
            return

    # set the batch document

    batch = db.batch()

    # create message doc

    match_ref = db.collection("likes").document(match_id)
    message_ref = match_ref.collection("messages").document(message_id)

    batch.update(message_ref, {"isSysMessage": False, "sender": user_ref, "processing": False})

    # update match doc

    batch.update(match_ref, {"recentMessageText": message,
                             "recentMessageDate": firestore.firestore.SERVER_TIMESTAMP, "chatting": True,
                             "recentMessageSender": user_ref, "recentMessageSeenBy": [user_ref],
                             "recentMessageNotSeenBy": [u for u in match_data.get("involvedUsers") if u.id != uid]})


    formatted_date = datetime.now().strftime('%Y%m%d')

    # update user docs & send notifications
    batch.update(user_ref, {f"messageTodayPerMatchSent.{match_id}.otherGender": other_gender,
                                           f"messageTodayPerMatchSent.{match_id}.count": firestore.Increment(1),
                                           "latestDayMessaging": formatted_date})

    # one batched write for all changes

    batch.commit()

    # send push notification where relevant
    if other_data.get("pnMe"):
        push_notifications_collection: firestore.CollectionReference = db.collection("ff_user_push_notifications")
        title = get_translated_text("New message", other_data.get("language"))
        truncated = message[:77] + "..." if len(message) > 80 else message
        formatted = f"{user_name} {get_translated_text("just wrote:", other_data.get("language"))} {truncated}"
        sendPushNotificationWithPayload(uid=other_uid, title=title, text=formatted, push_notifications_collection=push_notifications_collection, payload={},
                                        initial_page_name="Chat")

    event = BaseEvent(event_type="Matching: Sent Message", user_id=uid,
                      event_properties={"Match ID": match_id, "Receiving Gender": other_gender})

    amplitude_client.track(event)

    checkChatLimit(uid)
    checkChatLimit(other_uid)

    # exit
    return

def fetch_liked_profiles(user_ref, user_liked_profiles, uid):

    current_liked_uids = [profile.get('uid') for profile in user_liked_profiles]

    MAX_DOCS = 5
    meta_collection = user_ref.collection("meta")

    try:
        # 1. Fetch all existing liked UIDs across documents
        all_liked_uids = []
        last_doc_index = 0

        for doc_index in range(MAX_DOCS):
            # Construct document name (likedProfiles, likedProfiles1, etc.)
            doc_name = "likedProfiles" if doc_index == 0 else f"likedProfiles{doc_index}"
            doc = meta_collection.document(doc_name).get()

            if not doc.exists:
                break

            uids = doc.to_dict().get("likedUids", [])
            all_liked_uids.extend(uids)

        return all_liked_uids + current_liked_uids

    except Exception as e:
        logging.exception(f"Error updating liked profiles for user {uid}: {e}")
        # Return only new likes if we couldn't process existing ones

    return current_liked_uids

def update_liked_profiles(user_ref, user_liked_profiles, uid):
    """
    Updates a user's liked profiles in Firestore, handling pagination for large arrays.

    Args:
        user_ref: Firestore reference to the user's document
        user_liked_profiles: List of newly liked profile objects
        uid: User ID

    Returns:
        List of all liked UIDs (both existing and newly added)
    """
    # Extract UIDs from newly liked profiles
    new_liked_uids = [profile.get('uid') for profile in user_liked_profiles]
    if not new_liked_uids:
        return []

    MAX_DOCS = 5
    MAX_UIDS_PER_DOC = 15000
    meta_collection = user_ref.collection("meta")

    try:
        # 1. Fetch all existing liked UIDs across documents
        all_liked_uids = []
        last_doc_index = 0

        for doc_index in range(MAX_DOCS):
            # Construct document name (likedProfiles, likedProfiles1, etc.)
            doc_name = "likedProfiles" if doc_index == 0 else f"likedProfiles{doc_index}"
            doc = meta_collection.document(doc_name).get()

            if not doc.exists:
                break

            uids = doc.to_dict().get("likedUids", [])
            all_liked_uids.extend(uids)


        # 2. Determine the write document
        total_uids = len(all_liked_uids) + len(new_liked_uids)

        write_doc_index = total_uids // MAX_UIDS_PER_DOC

        logging.info(f"Current user liked profiles document: {write_doc_index}")

        # Handle the case when we need to create a new document

        write_doc_name = "likedProfiles" if write_doc_index == 0 else f"likedProfiles{write_doc_index}"
        meta_collection.document(write_doc_name).set({
            "likedUids": firestore.firestore.ArrayUnion(new_liked_uids)
        }, merge=True)

        if write_doc_index == MAX_DOCS - 1 and total_uids > MAX_UIDS_PER_DOC * MAX_DOCS:
            logging.warning(f"User {uid} approaching maximum supported likes storage capacity")

        # Return all liked UIDs (existing + new)
        return all_liked_uids + new_liked_uids

    except Exception as e:
        logging.exception(f"Error updating liked profiles for user {uid}: {e}")
        # Return only new likes if we couldn't process existing ones

    return new_liked_uids


def generateFindomMatches(eligible_dicts, user, uid, db, eligible_woman, encoding, age_lower, age_upper, distance):
    findom_result = []

    for curr_dict in eligible_dicts:

        print(curr_dict)

        for i in range(0, 5):

            try:
                if not (user.dealbreakers.get("distance") == True) and i == 3:
                    distance = 100000000

                if eligible_woman:
                    distance = 100000

            except Exception as e:
                logging.exception(e)

            try:
                findom_result = mh.call_suggest_old_users_function(
                    pool=random.choice([db_conn_new, db_conn_new_replica]),
                    user_id=uid,
                    highest_elo_limit=curr_dict.get("highest_elo_limit"),
                    lowest_elo_limit=curr_dict.get("lowest_elo_limit"),
                    similar_elo_limit=curr_dict.get("similar_elo_limit"),
                    new_people_limit=curr_dict.get("new_people_limit"),
                    most_active_limit=curr_dict.get("most_active_limit"),
                    geographically_close_limit=curr_dict.get("geographically_close_limit"),
                    liked_by_limit=curr_dict.get("liked_by_limit"),
                    highest_elo_weight=curr_dict.get("highest_elo_weight"),
                    lowest_elo_weight=curr_dict.get("lowest_elo_weight"),
                    similar_elo_weight=curr_dict.get("similar_elo_weight"),
                    new_people_weight=curr_dict.get("new_people_weight"),
                    most_active_weight=curr_dict.get("most_active_weight"),
                    geographically_close_weight=curr_dict.get("geographically_close_weight"),
                    liked_by_weight=curr_dict.get("liked_by_weight"),
                    vector_req=encoding,
                    vector_threshold=mh.auto_expansion_factors[i]["threshold"],
                    include_role=user.role_matching,
                    role_req=user.role_req,
                    exclude_professional=user.hide_findoms,
                    relationship_type_req=user.relationship_type if (
                            user.dealbreakers["relationship_type"] is True) else None,
                    # Example list
                    min_age=age_lower if (user.dealbreakers["age"] is True) else max(18, int(user.age_range.get(
                        "lower") -
                                                                                             mh.auto_expansion_factors[
                                                                                                 i]["age"])),
                    max_age=age_upper if (user.dealbreakers["age"] is True) else min(99, int(user.age_range.get(
                        "upper") +
                                                                                             mh.auto_expansion_factors[
                                                                                                 i]["age"])),
                    max_distance=distance if (user.dealbreakers["distance"] is True) else (
                            distance * mh.auto_expansion_factors[i]["distance"]),
                    min_height=user.height_range.get("lower") if (user.dealbreakers["height"] is True) else None,
                    # NULL in the query
                    max_height=user.height_range.get("upper") if (user.dealbreakers["height"] is True) else None,
                    # NULL in the query
                    ethnicity_req=user.ethnicity if (user.dealbreakers["ethnicity"] is True) else None,
                    # NULL in the query
                    religions_req=user.religion if (user.dealbreakers["religion"] is True) else None,
                    edu_req=user.education_level if (user.dealbreakers["education_level"] is True) else None,
                    children_req=user.children if (user.dealbreakers["children"] is True) else None,
                    family_req=user.family_plans if (user.dealbreakers["family_plans"] is True) else None,
                    politics_req=user.politics if (user.dealbreakers["politics"] is True) else None,
                    drinking_req=user.drinking if (user.dealbreakers["drinking"] is True) else None,
                    smoking_req=user.smoking if (user.dealbreakers["smoking"] is True) else None,
                    findom_preferred=user.findom_preferred
                )

                logging.info(f"try {i}, {len(findom_result)} length")

            except Exception as e:
                logging.exception(e)
                db.collection('SQLExceptions').add(
                    {'function': 'generateMatchesAdvanced -> matching function 1', 'user': uid,
                     'error': str(e),
                     'time': firestore.SERVER_TIMESTAMP})

            if len(findom_result) > 2:
                break

        if len(findom_result) > 2:
            break

    findom_suggested_uids = []

    for i, row in enumerate(findom_result):
        findom_suggested_uids.append(row._mapping.get('suggested_user_uid'))

        if i > 40:
            break

    print(f"findom suggested uids: {len(findom_suggested_uids)}")

    return findom_suggested_uids



def get_findom_interest_status(kinks_list: list[str], professionals_req: bool) -> Optional[bool]:
    """
    Based on a user's kink list and findom filter status, returns whether to classify them as a findom_interest.

    Args:
        kinks_list (list): A list of kinks associated with the user.
        findom_filter (bool): Indicates if the findom filter is enabled.

    Returns:
        Optional[bool]: True if the user is a findom_interest, False if explicitly opted out of findom interest, or None if undetermined.
    """

    if professionals_req:
        return False
    elif 'Findom' in kinks_list:
        return True
    else:
        return None
    
