# Ignore everything
*

# Explicitly include directories so specific files inside them can be added
!/.gitignore
!/
!/admin/
!analytics/
!forum/
!matching/
!purchase/
!user/
!verification/
!waitinglist/
!src/
!config/
!functions/
!js_functions/
!firebase.json
!firestore/

# Explicitly include files within those directories
!/admin/main.py
!/admin/requirements.txt
!/admin/shared.py
!/admin/prodgateway_curr.yaml
!/admin/devgateway.yaml
!analytics/main.py
!analytics/requirements.txt
!forum/main.py
!forum/requirements.txt
!matching/main.py
!matching/requirements.txt
!matching/MatchingHelper.py
!purchase/main.py
!purchase/requirements.txt
!user/main.py
!user/requirements.txt
!user/UserDataHelper.py
!user/nmatching.py
!user/mpe_helper.py
!verification/main.py
!verification/requirements.txt
!verification/nmatching.py
!verification/referral_helper.py
!verification/mpe_helper.py
!waitinglist/main.py
!waitinglist/requirements.txt
!README.md
!src/main.py
!config/config.yaml
!functions/main.py
!functions/requirements.txt
!js_functions/index.js
!js_functions/package.json
!js_functions/package-lock.json
!firestore.rules
!remoteconfig.template.json
!common/
!copy.py
!utils.py
!firestore/firestore_rules
!admin/translations.json
!common/translations.json
!common/translations.py
