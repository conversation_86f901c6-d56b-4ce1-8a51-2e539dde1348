# Imports in alphabetical order

from amplitude import Amplitude, BaseEvent, Identify, EventOptions
import copy
from datetime import datetime, timedelta
from firebase_admin import initialize_app
from firebase_functions import https_fn, identity_fn, options, scheduler_fn
from firebase_functions.firestore_fn import (
  on_document_created,
  on_document_deleted,
  on_document_updated,
  on_document_written,
  Event,
  Change,
  DocumentSnapshot,
)
from google.cloud import secretmanager, firestore
from firebase_admin import firestore
from firebase_admin import db as rtdb
import requests
from requests.adapters import HTTPAdapter, Retry
from google.cloud.firestore_v1 import FieldFilter, Or
import google.cloud.logging
from google.cloud.sql.connector import Connector
import json
import logging
import pg8000
import random
import sqlalchemy
import time
from typing import Any
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from common_translations import get_translated_text

# initializing log client
log_client = google.cloud.logging.Client()
log_client.setup_logging()

# defining common methods in global scope
def access_secret_version(secret_name):
    '''Access a secret from the project's secret manager'''
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/greenrocks-9abc3/secrets/{secret_name}/versions/latest"
    response = client.access_secret_version(name=name)
    return response.payload.data.decode('UTF-8')
def calculate_interval_range(position):
    """
    Calculate the interval range for a given position based on specified ranges.

    Args:
    - position (int): The position of an item in the array.

    Returns:
    - str: The interval range in which the position falls, in the format "start-end".
    """
    position = position * 5
    if position < 500:
        interval_start = (position // 100) * 100
        interval_end = interval_start + 100
    elif position < 2500:
        interval_start = ((position - 500) // 250) * 250 + 500
        interval_end = interval_start + 250
    elif position < 5000:
        interval_start = ((position - 2500) // 500) * 500 + 2500
        interval_end = interval_start + 500
    else:
        interval_start = ((position - 5000) // 1000) * 1000 + 5000
        interval_end = interval_start + 1000

    return f"{1 if interval_start == 0 else interval_start}-{interval_end}"

def connect_with_connector(connector:Connector) -> sqlalchemy.engine.base.Engine:

    """
    Initializes a connection pool for a Cloud SQL instance of Postgres.

    Uses the Cloud SQL Python Connector package.
    """
    def getconn() -> pg8000.dbapi.Connection:
        conn: pg8000.dbapi.Connection = connector.connect(
            "greenrocks-9abc3:europe-west2:chyrpe-prod1",
            "pg8000",
            user="<EMAIL>",
            db="chyrpe_main_db",
            enable_iam_auth=True,
        )
        return conn

    # The Cloud SQL Python Connector can be used with SQLAlchemy
    # using the 'creator' argument to 'create_engine'
    pool = sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        pool_size=2,
        max_overflow=2,
        # ...
    )
    return pool

def create_retry_session() -> requests.Session:
    session = requests.Session()
    retries = Retry(total=5, backoff_factor=0.1, status_forcelist=[429, 500, 502, 503, 504])
    session.mount('https://', HTTPAdapter(max_retries=retries))
    return session

def find_all_likes_of_two_users(user1_id: str, user2_id: str, likes_collection) -> list[DocumentSnapshot]:
    '''Returns all likes documents that include 2 given users by id'''
    filter_1 = FieldFilter("involedUIDs", "==", [user1_id, user2_id])
    filter_2 = FieldFilter("involedUIDs", "==", [user2_id, user1_id])

    # Create the union filter of the two filters (queries)
    or_filter = Or(filters=[filter_1, filter_2])

    # Execute the query
    docs = likes_collection.where(filter=or_filter).get()

    return docs

def grant_promotional_access_rc(entitlement_name: str, time_to_end: datetime, user_id: str, secret: str, s: requests.Session) -> bool:
    timestamp_end = int(time_to_end.timestamp() * 1000)

    url = f"https://api.revenuecat.com/v1/subscribers/{user_id}/entitlements/{entitlement_name}/promotional"

    payload = json.dumps({
        "end_time_ms": timestamp_end
    })
    headers = {
        'Authorization': f'Bearer {secret}',
        'Content-Type': 'application/json'
    }

    response = s.post(url, headers=headers, data=payload)

    if response.status_code in [200, 201]:
        return True
    else:
        return False

def sendPushNotification(uid, title, text, push_notifications_collection):
    push_notifications_collection.add({"notification_title": title, "notification_text": text,
                                       "user_refs": f'/users/{uid}',
                                       "timestamp": firestore.SERVER_TIMESTAMP})
# defining common variables
amplitude_client = Amplitude("65dbac83dec4e06df231ca34a83e4757")

def log_retry_attempt(retry_state):
    """Logs retry attempts before each retry."""
    logging.warning(f"Retrying... Attempt {retry_state.attempt_number}")

def with_retry(max_attempts=6, min_wait=2, max_wait=20, exception_types=(Exception)):
    """Decorator to add retry logic with exponential backoff."""
    return retry(
        retry=retry_if_exception_type(exception_types),
        wait=wait_exponential(multiplier=1, min=min_wait, max=max_wait),  # Initial wait 2s, doubles each time, max 10s
        stop=stop_after_attempt(max_attempts),  # Max 5 retry attempts
        before=log_retry_attempt,
        reraise=True
    )


### Actual cloud functions

initialize_app()

# ADD NEW USER GLOBAL WAITING LIST: Add calling user to the global waiting list
@https_fn.on_call(region="europe-west2", enforce_app_check=True, memory=options.MemoryOption.MB_512)
def addNewUserGlobalWaitingList(req: https_fn.CallableRequest) -> Any:
    """
    Add calling user to the global waiting list
    :param req: Callable request incl. user auth information - no extra params
    :return: None
    """
    if req.auth.uid == None:
        return "User not authenticated"
    uid = req.auth.uid

    # initialize firestore
    db = firestore.client()
    # set user doc ref and get user doc, extract data and from data region
    user_doc_ref = db.collection("users").document(uid)

    global_wl_ref = db.collection("waitingList").document("GLOBAL_WL")
    # get document for user's location
    transaction = db.transaction()

    # put user on global waiting list, incl. info about sublevel
    @firestore.transactional
    def update_in_transaction(transaction, wl_region_ref):
        master_wl_region_doc = wl_region_ref.get(transaction=transaction)

        users = master_wl_region_doc.to_dict().get("users", [])

        users.append({"reference": user_doc_ref, "sublevel": "100"})

        transaction.update(wl_region_ref, {"users": users})

        return len(users)

    index_of_user = update_in_transaction(transaction, global_wl_ref)

    user_interval = calculate_interval_range(index_of_user)

    user_doc_ref.update({"onGlobalWaitingList": True, "globalWlInterval": user_interval, "globalWlAdmitted": False})

    # send relevant Amplitude events
    try:
        identify_obj = Identify()
        identify_obj.set("Initial Position Global Wl", user_interval)
        amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

        identify_obj4 = Identify()
        identify_obj4.set("On Global Waiting List", True)
        amplitude_client.identify(identify_obj4, EventOptions(user_id=uid))

        event = BaseEvent(event_type="Waiting List: Put on Global Waiting List", user_id=uid, event_properties={
            "Initial Position Global Wl": user_interval
        })
        amplitude_client.track(event)
    except Exception as e:
        logging.exception(e)

    return

# ADD NEW USER GLOBAL WAITING LIST: Add calling user to their regional waiting list
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.MB_512, enforce_app_check=True)
def addNewUserWaitingList(req: https_fn.CallableRequest) -> Any:
    """
        Add calling user to their regional waiting list
        :param req: Callable request incl. user auth information - no extra params
        :return: None
    """
    if req.auth == None:
        return "User not authenticated"
    uid = req.auth.uid

    logging.info(f"Add new user waiting list for {uid}")

    # initialize firestore
    db = firestore.client()
    # set user doc ref and get user doc, extract data and from data region
    user_doc_ref = db.collection("users").document(uid)
    user_doc = user_doc_ref.get()
    user_doc_data = user_doc.to_dict()
    user_region = user_doc_data['wlRegion']

    # update RTDB (to be deprecated)
    rtdb_db = rtdb.reference('users')
    rtdb_user_ref = rtdb_db.child(uid)
    rtdb_user_ref.update({
        'location/latitude': user_doc_data['location'].latitude,
        'location/longitude': user_doc_data['location'].longitude,
        'location/wlRegion': user_doc_data['wlRegion']
    })

    try:
        u_location = user_doc_data.get('location')
        u_lat = u_location.latitude
        u_long = u_location.longitude

        here_key = access_secret_version("Here_Key")

        url = f"https://revgeocode.search.hereapi.com/v1/revgeocode?at={u_lat},{u_long}&lang=en-US&apiKey={here_key}"

        payload = {}
        headers = {}

        response = requests.request("GET", url, headers=headers, data=payload)

        try:
            country_name = response.json().get("items", {})[0].get("address").get("countryName")
            state = response.json().get("items", {})[0].get("address").get("state")
            city = response.json().get("items", {})[0].get("address").get("city")
        except:
            country_name = "Unknown"
            state = "Unknown"
            city = "Unknown"

        identify_obj1 = Identify()
        identify_obj1.set("Country_EN", country_name)
        amplitude_client.identify(identify_obj1, EventOptions(user_id=uid))

        identify_obj2 = Identify()
        identify_obj2.set("State_EN", state)
        amplitude_client.identify(identify_obj2, EventOptions(user_id=uid))

        identify_obj3 = Identify()
        identify_obj3.set("City_EN", city)
        amplitude_client.identify(identify_obj3, EventOptions(user_id=uid))

        identify_obj4 = Identify()
        identify_obj4.set("On Regional Waiting List", True)
        amplitude_client.identify(identify_obj4, EventOptions(user_id=uid))

        identify_obj5 = Identify()
        identify_obj5.set("Sign Up Finished", True)
        amplitude_client.identify(identify_obj5, EventOptions(user_id=uid))

        user_doc_ref.update({'city': city, 'country': country_name, 'state': state})
        user_doc_data.get('publicProfile').update({'city': city})
    except Exception as e:
        logging.exception(f"Amplitude failed: {e}")

    region_collection = db.collection("openRegions")
    his_region_query = region_collection.where("name", "==", user_region)
    his_region = his_region_query.get()

    # grant free standard for all
    retry_session = create_retry_session()
    grant_promotional_access_rc("standard_access", datetime(2099, 12, 31), uid,
                                    access_secret_version('Revenuecat_Key'), retry_session)
    grant_promotional_access_rc("free_standard_lifetime",datetime(2099, 12, 31), uid,
                                    access_secret_version('Revenuecat_Key'), retry_session)

    # determine standard bypass eligibility for all
    higher_free_limits_eligible = False  # random.choice([False, True])
    no_paywall_eligible = True # random.choice([False, True])
    standard_bypass_eligible = False

    waitinglist_bypass = True

    identify_obj1 = Identify()
    identify_obj1.set("Standard Bypassed", standard_bypass_eligible)

    identify_obj2 = Identify()
    identify_obj2.set("Waitinglist Bypassed", waitinglist_bypass)

    identify_obj3 = Identify()
    identify_obj3.set("Higher Free Likes Eligible Test 250330", higher_free_limits_eligible)

    identify_obj4 = Identify()
    identify_obj4.set("No Paywall Eligible Test 250525", no_paywall_eligible)

    amplitude_client.identify(identify_obj1, EventOptions(user_id=uid))
    amplitude_client.identify(identify_obj2, EventOptions(user_id=uid))
    amplitude_client.identify(identify_obj3, EventOptions(user_id=uid))
    amplitude_client.identify(identify_obj4, EventOptions(user_id=uid))

    event = BaseEvent(event_type="Standard: Bypassing decision made", user_id=uid,
                      event_properties={"Standard Bypassed": standard_bypass_eligible})

    amplitude_client.track(event)

    event2 = BaseEvent(event_type="Waiting List: Bypassing decision made", user_id=uid,
                      event_properties={"Waitlist Bypassed": waitinglist_bypass})

    amplitude_client.track(event2)

    event3 = BaseEvent(event_type="Free: Higher Free Free Likes Eligible Test 250330 decision made", user_id=uid,
                      event_properties={"Higher Free Likes Eligible Test 250330": higher_free_limits_eligible})

    amplitude_client.track(event3)

    event4 = BaseEvent(event_type="Purchases: No Paywall Eligible Test 250525 decision made", user_id=uid,
                       event_properties={"No Paywall Eligible Test 250525": no_paywall_eligible})

    amplitude_client.track(event4)

    if standard_bypass_eligible:

        grant_promotional_access_rc('standard_access', datetime(2099, 12, 31), uid,
                                    access_secret_version('Revenuecat_Key'), retry_session)
        grant_promotional_access_rc('paid_standard_lifetime', datetime(2099, 12, 31), uid,
                                    access_secret_version('Revenuecat_Key'), retry_session)


        identify_obj3 = Identify()
        identify_obj3.set("Standard & Waitlist Bypassed", True)
        amplitude_client.identify(identify_obj1, EventOptions(user_id=uid))

        event3 = BaseEvent(event_type="Waiting List: Bypassing decision made", user_id=uid,
                          event_properties={"Standard & Waitlist Bypassed": True})

        amplitude_client.track(event3)

    user_doc_ref.collection("meta").document(uid).set({"standardBypass": standard_bypass_eligible, "higherFreeLikes250330": higher_free_limits_eligible, "noPaywall250525": no_paywall_eligible, "waitlistBypass": waitinglist_bypass, "standardWaitlistBypass": (standard_bypass_eligible and waitinglist_bypass)})

    # only for users with local matching open region: enrol in test for bypassing waiting list & standard
    if len(his_region) > 0 or user_doc_data.get("email") == "<EMAIL>":
        local_update_dict = {"onWaitingList": False, "waitingListInterval": "0", "verificationPendingSignUp": True,
         "globalMatching": False, "localMatching": True, "localWlAdmitted": True, "globalWlAdmitted": False, "pnMa": True,
        "pnMe": True}

        if standard_bypass_eligible or no_paywall_eligible:
            local_update_dict["standardDecisionMade"] = datetime.now().strftime("%Y%m%d")

        if higher_free_limits_eligible:
            local_update_dict["currentLikeLimitsId"] = "higherfreetest250330"

        # only for those who bypass, update relevant user document, grant standard access, update Amplitude
        user_doc_ref.update(local_update_dict)

        return

    else:
        global_update_dict = {"onWaitingList": False, "waitingListInterval": "Wait for opening", "verificationPendingSignUp": True,
                             "globalMatching": True, "localMatching": False, "localWlAdmitted": False,
                             "globalWlAdmitted": True, "pnMa": True, "pnMe": True}

        if standard_bypass_eligible or no_paywall_eligible:
            global_update_dict["standardDecisionMade"] = datetime.now().strftime("%Y%m%d")

        if higher_free_limits_eligible:
            global_update_dict["currentLikeLimitsId"] = "higherfreetest250330"

        # only for those who bypass, update relevant user document, grant standard access, update Amplitude
        user_doc_ref.update(global_update_dict)

        return

    wl_region_ref = db.collection("waitingList").document(user_region)
    transaction = db.transaction()

    # add user to waiting list document, incl. sublevel
    @firestore.transactional
    def update_in_transaction(transaction, wl_region_ref):
        # get master doc for that region
        master_wl_region_doc = wl_region_ref.get(transaction=transaction)

        users = master_wl_region_doc.to_dict().get("users", [])

        users.append({"reference": user_doc_ref, "sublevel": "100"})

        transaction.update(wl_region_ref, {"users": users})

        return len(users)

    index_of_user = update_in_transaction(transaction, wl_region_ref)

    user_interval = calculate_interval_range(index_of_user)

    if len(his_region) > 0:
        # update user doc to reflect being put on waiting list
        user_doc_ref.collection("meta").document(uid).set({"standardWaitlistBypass": bypass_eligible})
        user_doc_ref.update(
                {"onWaitingList": True, "waitingListInterval": user_interval, "trueFirstInterval": user_interval})
    else:
        user_doc_ref.update(
            {"onWaitingList": True, "waitingListInterval": "Wait for matching", "trueFirstInterval": user_interval})

    identify_obj = Identify()
    identify_obj.set("Initial Position Regional Wl", user_interval)
    amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

    event = BaseEvent(event_type="Waiting List: Put on Regional Waiting List", user_id=uid, event_properties={
        "Initial Position Regional Wl": user_interval
    })

    amplitude_client.track(event)

    return

# ADMIT DEFERRED WL MEN: Every 2 hours, admits users from the applicable deferred waiting lists
@scheduler_fn.on_schedule(
    schedule="every 24 hours", region="europe-west1", memory=options.MemoryOption.GB_1)
def admitDeferredWlMen(event: scheduler_fn.ScheduledEvent) -> None:
    """
    Every 2 hours, admits users from the applicable deferred waiting lists
    :param event: Due to scheduled trigger of cloud function, no practical relevance
    :return: None
    """

    db = firestore.client()

    # gets all deferred waiting lists where remaining admits > 0
    waiting_list_admission_collection = db.collection("waitingListAdmission")
    waiting_list_deferred_admission_collection = db.collection("wlDeferredAdmission")
    men_to_admit_query = waiting_list_deferred_admission_collection.where(filter=FieldFilter("remainingAdmits", ">", 0))
    men_to_admit_docs = men_to_admit_query.get()

    # create a waiting list admissions collection document with the number of users to be admitted in each region
    for m_doc in men_to_admit_docs:
        m_data = m_doc.to_dict()
        remaining_admits = m_data.get("remainingAdmits")
        region_name = m_data.get("region")

        if remaining_admits > 200:
            waiting_list_admission_collection.add({"region": region_name, "numberOfAdmits": 200,
                                                   "subscriberExtra": 0})
            m_doc.reference.update({"remainingAdmits": remaining_admits - 200})

        else:
            waiting_list_admission_collection.add({"region": region_name, "numberOfAdmits": remaining_admits,
                                                   "subscriberExtra": 0})
            m_doc.reference.update({"remainingAdmits": 10})

# ADMIT USERS WAITING LIST: When a deferred waiting list document is created, gets the users to be admitted based on its info
@on_document_created(document="waitingListAdmission/{docId}", region="europe-west1", timeout_sec=300, memory=options.MemoryOption.GB_1)
def admitUsersWaitingList(event: Event[DocumentSnapshot]) -> None:
    """
    When a deferred waiting list document is created, gets the users to be admitted based on its info
    :param event: Includes affected waitingListAdmission's information
    :return: None
    """
    new_value = event.data.to_dict()

    region = new_value["region"]
    number_of_admits = new_value["numberOfAdmits"]
    subscriber_extra = new_value["subscriberExtra"]

    db = firestore.client()

    transaction = db.transaction()
    wl_region_ref = db.collection("waitingList").document(region)

    # updates users from applicable waiting list, ensuring subscribers have a fixed quota to get in and speed up in the remaining waiting list
    @firestore.transactional
    def update_in_transaction(transaction, wl_region_ref):

        users_to_admit = []
        waitinglist_after_admission = []
        # get master doc for that region
        master_wl_region_doc = wl_region_ref.get(transaction=transaction)

        users_before_admission = master_wl_region_doc.get("users")
        users_after_admission = copy.deepcopy(users_before_admission)

        indices_after_admission = {}

        for i, user in enumerate(users_after_admission):
            try:
                user_uid = user["reference"].id
                indices_after_admission[str(user_uid)] = i
            except:
                continue


        users_without_subscription = []
        users_with150_sub = []
        users_with200_sub = []
        users_with300_sub = []
        users_with_sub = []

        for element in users_after_admission:
            user_uid = str(element["reference"].id)
            if element['sublevel'] == '100':
                users_without_subscription.append(user_uid)
                users_with_sub.append(user_uid)
            elif element['sublevel'] == '150':
                users_with150_sub.append(user_uid)
                users_with_sub.append(user_uid)
            elif element['sublevel'] == '200':
                users_with200_sub.append(user_uid)
                users_with_sub.append(user_uid)
            elif element['sublevel'] == '300':
                users_with300_sub.append(user_uid)
                users_with_sub.append(user_uid)

        admits_without_subscription = round(number_of_admits/3)
        admits_with_subscription = round(number_of_admits - admits_without_subscription)

        users_without_subscription_to_admit = users_without_subscription[0:admits_without_subscription]
        for u in users_without_subscription_to_admit:
            users_with_sub.remove(u)

        users_with_subscription_to_admit = users_with_sub[0:admits_with_subscription]
        for u in users_with_subscription_to_admit:
            users_with_sub.remove(u)

        users_to_admit = users_with_subscription_to_admit + users_without_subscription_to_admit

        for element in users_without_subscription_to_admit:
            user_index = indices_after_admission[element]
            del users_after_admission[user_index]
            for i, user in enumerate(users_after_admission):
                user_uid = user["reference"].id
                indices_after_admission[str(user_uid)] = i

        for element in users_with_subscription_to_admit:
            if element in users_with150_sub:
                users_with150_sub.remove(element)
            elif element in users_with200_sub:
                users_with200_sub.remove(element)
            elif element in users_with300_sub:
                users_with300_sub.remove(element)

            user_index = indices_after_admission[element]
            del users_after_admission[user_index]
            for i, user in enumerate(users_after_admission):
                user_uid = user["reference"].id
                indices_after_admission[str(user_uid)] = i

        users_push_150 = int(number_of_admits * 0.5)
        users_push_200 = int(number_of_admits)
        users_push_300 = int(number_of_admits * 2)

        for element in users_with150_sub:
            old_index = indices_after_admission[element]
            if old_index - users_push_150 > 0:
                new_index = old_index - users_push_150
                del users_after_admission[old_index]
                element_to_insert = {"reference": db.collection("users").document(element), "sublevel": "150"}
                users_after_admission.insert(new_index, element_to_insert)
                for i, user in enumerate(users_after_admission):
                    user_uid = user["reference"].id
                    indices_after_admission[str(user_uid)] = i

        for element in users_with200_sub:
            old_index = indices_after_admission[element]
            if old_index - users_push_200 > 0:
                new_index = old_index - users_push_200
                del users_after_admission[old_index]
                element_to_insert = {"reference": db.collection("users").document(element), "sublevel": "200"}
                users_after_admission.insert(new_index, element_to_insert)
                for i, user in enumerate(users_after_admission):
                    user_uid = user["reference"].id
                    indices_after_admission[str(user_uid)] = i

        for element in users_with300_sub:
            old_index = indices_after_admission[element]
            if old_index - users_push_300 > 0:
                new_index = old_index - users_push_300
                del users_after_admission[old_index]
                element_to_insert = {"reference": db.collection("users").document(element), "sublevel": "300"}
                users_after_admission.insert(new_index, element_to_insert)
                for i, user in enumerate(users_after_admission):
                    user_uid = user["reference"].id
                    indices_after_admission[str(user_uid)] = i

        db.collection("userBatchesToAdmit").add({"usersToAdmit": users_to_admit, "region": region})
        db.collection("updateUserDocWlPlaces").add({"region": region})

        transaction.update(wl_region_ref, {"users": users_after_admission})

    update_in_transaction(transaction, wl_region_ref)

    return

# UPDATE USER WL PLACES: Updates the waiting list interval of users who remain on waiting list after admission cycle
@on_document_created(document="updateUserDocWlPlaces/{docId}", region="europe-west1", timeout_sec=300, memory=options.MemoryOption.MB_512)
def updateUserWlPlaces(event: Event[DocumentSnapshot]) -> None:
    """
    Updates the waiting list interval of users who remain on waiting list after admission cycle
    :param event: Includes affected updateUserDocWlPlaces's information
    :return: None
    """

    new_value = event.data.to_dict()

    region = new_value["region"]

    db = firestore.client()

    global_wl_ref = db.collection("waitingList").document(region)
    # get document for user's location
    global_wl_doc = global_wl_ref.get()
    global_wl_data = global_wl_doc.to_dict()

    global_wl_users_array = global_wl_data["users"]

    logging.info(global_wl_users_array)

    # updating all users on the applicable remaining waiting list
    for user in global_wl_users_array:
        logging.info("now updating user: ", user["reference"].id)
        index_of_user = global_wl_users_array.index(user)
        user_interval = calculate_interval_range(index_of_user)

        try:
            if region == "GLOBAL_WL":
                user["reference"].update({"globalWlInterval": user_interval})
            else:
                user["reference"].update({"waitingListInterval": user_interval})

        except Exception as e:
            logging.exception(e)
            continue

    return

# UPDATE ADMITTED USERS WAITING LIST: Updates users who have been admitted from waiting list to reflect the right user document status
@on_document_created(document="userBatchesToAdmit/{docId}", region="europe-west1", timeout_sec=300, memory=options.MemoryOption.GB_1, cpu=2, concurrency=1)
def updateAdmittedUsersWaitingList(event: Event[DocumentSnapshot]) -> None:
    """
    Updates users who have been admitted from waiting list to reflect the right user document status
    :param event: Includes affected users to admit
    :return: None
    """
    new_value = event.data.to_dict()

    db = firestore.client()

    # retrieves the affected region and users
    region = new_value["region"]
    users_to_admit = new_value["usersToAdmit"]

    # creates a connector to Postgres
    @with_retry()
    def get_connector_and_pool_with_retry():
        connector = Connector(refresh_strategy="lazy")
        pool = connect_with_connector(connector)
        return connector, pool

    connector, pool = get_connector_and_pool_with_retry()

    # loops over each user to be admitted
    for element in users_to_admit:
        try:

            logging.info(f"Admitting user: {element}")

            user = db.collection("users").document(element)
            user_doc = user.get()
            user_data = user_doc.to_dict()
            user_verified = user_data.get('verified', False)
            user_location = user_data.get('location')
            previous_local_matching = user_data.get("localMatching", False)
            user_public_profile = user_data.get("publicProfile")
            user_lang = user_data.get("language", "en")

            # if a user doc does not exist, will not be recognised
            if user_doc.exists == False:
                continue

            # if a user does not have location, will not be recognised
            if user_location == None:
                print(f"Update User Admitted Waiting List No Location - {user_data.get('uid')}")
                continue

            # if the user is not verified, update the user document accordingly
            if not user_verified:
                # differences in global and local waiting list admissions
                if region == 'GLOBAL_WL':
                    if previous_local_matching:
                        user.update({"onGlobalWaitingList": False, "globalWlInterval": "0", "verificationPendingSignUp": True,
                                     "globalWlAdmitted": True,
                                     "normalLikeDailyLimit": 5})
                    else:
                        user.update({"onGlobalWaitingList": False, "globalWlInterval": "0", "verificationPendingSignUp": True,
                                     "globalMatching": True, "localMatching": False, "globalWlAdmitted": True,
                                     "normalLikeDailyLimit": 5, "lowerHeightReq": 0,
                                     "upperHeightReq": 250, "pnMa": True, "pnMe": True})

                        try:
                            user_public_profile.update({"cityHidden": False})
                        except Exception as e:
                            logging.exception(e)

                else:
                    user.update(
                        {"onWaitingList": False, "waitingListInterval": "0", "verificationPendingSignUp": True,
                        "globalMatching": False, "localMatching": True, "localWlAdmitted": True,
                         "normalLikeDailyLimit": 5, "lowerHeightReq": 0, "upperHeightReq": 250, "pnMa": True,
                         "pnMe": True})

                    user_public_profile.update({"cityHidden": False})

                push_notifications_collection = db.collection("ff_user_push_notifications")

                

                if region == 'GLOBAL_WL':
                    try:

                        user_notification_cohort = user_data.get("notificationTestingCohort")
                        if user_notification_cohort == "Emoji":

                            sendPushNotification(element, "Chyrpe",
                                                 f"{get_translated_text("Congratulations, you’ve made it through the global waiting list! Get started matching now!", user_lang)} 🎉", push_notifications_collection)

                            event = BaseEvent(event_type="Notifications: Sent Admitted Global Waiting List Notification Emoji",
                                              user_id=element)
                            amplitude_client.track(event)

                        else:
                            sendPushNotification(element, "Chyrpe",
                                                 f"{get_translated_text("Congratulations, you’ve made it through the global waiting list! Get started matching now!", user_lang)}", push_notifications_collection)

                            event = BaseEvent(
                                event_type="Notifications: Sent Admitted Global Waiting List Notification NoEmoji",
                                user_id=element)
                            amplitude_client.track(event)

                    except Exception as e:
                        logging.exception(e)

                continue

            if user_doc.to_dict().get('chyrpeMatchRef', False) is False:
                chyrpe_user = db.collection("users").document('fehBchlcouUcVwb66DJL03ZQJPH3')
                update_time, like_ref = db.collection('likes').add({'likingUser': chyrpe_user,
                                                                    'likedUser': user,
                                                                    'evolvedLike': False,
                                                                    'speakAllow': True,
                                                                    'chatting': True,
                                                                    'unmatched': False,
                                                                    'mutual': True,
                                                                    'recentMessageText': get_translated_text("chyrpe_welcome_title", user_data.get("language", "en")),
                                                                    'involvedUsersProfileRefOnly':
                                                                        [user_doc.get('publicProfile'),
                                                                         chyrpe_user.collection('publicProfile').document(
                                                                             '80KmP3ztM6AJVozLcujS')],
                                                                    'recentMessageSender': chyrpe_user,
                                                                    'recentMessageDate': firestore.SERVER_TIMESTAMP,
                                                                    'involvedUsers':
                                                                        [chyrpe_user, user],
                                                                    'involedUIDs': ['fehBchlcouUcVwb66DJL03ZQJPH3', element]
                                                                    })
                update_time_mes, message_ref = like_ref.collection('messages').add({
                                                                                       'body': get_translated_text("chyrpe_welcome", user_data.get("language", "en")),
                                                                                       'time':
                                                                                           firestore.SERVER_TIMESTAMP,
                                                                                       'sender':
                                                                                           chyrpe_user,
                                                                                       'isSysMessage':
                                                                                           False})
            else:
                like_ref = user_doc.get('chyrpeMatchRef')

            if region == 'GLOBAL_WL':
                if previous_local_matching:
                    user.update({"onGlobalWaitingList": False, "globalWlInterval": "0", "signUpFinished": True,
                                 "chyrpeMatchRef": like_ref, "globalWlAdmitted": True,
                                 "normalLikeDailyLimit": 5}, )
                else:
                    user.update({"onGlobalWaitingList": False, "globalWlInterval": "0", "signUpFinished": True,
                                 "chyrpeMatchRef": like_ref, "accountCreationFinished": firestore.SERVER_TIMESTAMP,
                                 "globalMatching": True, "localMatching": False, "globalWlAdmitted": True,
                                 "normalLikeDailyLimit": 5, "lowerHeightReq": 0, "upperHeightReq": 250, "pnMa": True, "pnMe": True})

                    try:
                        @with_retry()
                        def admit_users_with_retry():
                            with pool.connect() as db_conn:

                                logging.info(f"Admitting user SQL: {element}")

                                user_long = user_data.get('location').longitude
                                user_lat = user_data.get('location').latitude


                                # query database
                                stmt = sqlalchemy.text(
                                    """SELECT add_user(:user_id, :age, :date_of_birth, :gender, :role, :gender_preference, :longitude, :latitude, :upper_age_band, :lower_age_band,
                                    :proximity_preference, :elo_score, :elo_score_raw, :match_status, :incognito,
                                    :started_matching, :latest_suggestions)""")

                                # Execute with the parameters
                                result = db_conn.execute(stmt, {
                                    'user_id': user_data.get('uid'),
                                    'age': user_data.get('age'),
                                    'date_of_birth': user_data.get('birthday').strftime("%Y-%m-%d"),
                                    'gender': user_data.get('gender'),
                                    'role': user_data.get('position').capitalize(),
                                    'elo_score': 0.5,
                                    'started_matching': datetime.now().strftime("%Y-%m-%d"),
                                    'latest_suggestions': datetime.now().strftime("%Y-%m-%d"),
                                    'longitude': round(user_long, 2),
                                    'latitude': round(user_lat, 2),
                                    'upper_age_band': user_data.get('upperAgeReq'),
                                    'lower_age_band': user_data.get('lowerAgeReq'),
                                    'proximity_preference': 100000000,  # in m, not in km
                                    'gender_preference': 'Female' if user_data.get('genderReq') == 'Women' else (
                                        'Male' if user_data.get('genderReq') == 'Men' else 'Everyone'),
                                    'match_status': 'GlobalMatch',
                                    'incognito': user_data.get('incognito', False),
                                    'elo_score_raw': 1000}).fetchall()

                                db_conn.commit()

                                # Do something with the results
                                for row in result:
                                    logging.info(f"Committed admitting user SQL: {element}")

                        admit_users_with_retry()

                    except Exception as e:
                        logging.exception(e)
                        db.collection('SQLExceptions').add(
                            {'function': 'updateAdmittedUsersWaitingList', 'user': user_data.get('uid'),
                             'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

                    except:
                        print("Fail Update Admitted Users Waitinglist")
            else:
                user.update(
                    {"onWaitingList": False, "waitingListInterval": "0", "signUpFinished": True, "chyrpeMatchRef": like_ref,
                     "accountCreationFinished": firestore.SERVER_TIMESTAMP,
                     "globalMatching": False, "localMatching": True, "localWlAdmitted": True,
                     "normalLikeDailyLimit": 5, "lowerHeightReq": 0, "upperHeightReq": 250, "pnMa": True, "pnMe": True, "localAdmittedFromWl": True})

                try:

                    @with_retry()
                    def admit_users_with_retry():
                        with pool.connect() as db_conn:

                            logging.info(f"Admitting user SQL: {element}")

                            user_long = user_data.get('location').longitude
                            user_lat = user_data.get('location').latitude


                            # query database
                            stmt = sqlalchemy.text(
                                """SELECT add_user(:user_id, :age, :date_of_birth, :gender, :role, :gender_preference, :longitude, :latitude, :upper_age_band, :lower_age_band,
                                :proximity_preference, :elo_score, :elo_score_raw, :match_status, :incognito,
                                :started_matching, :latest_suggestions)""")

                            # Execute with the parameters
                            result = db_conn.execute(stmt, {
                                'user_id': user_data.get('uid'),
                                'age': user_data.get('age'),
                                'date_of_birth': user_data.get('birthday').strftime("%Y-%m-%d"),
                                'gender': user_data.get('gender'),
                                'role': user_data.get('position').capitalize(),
                                'elo_score': 0.5,
                                'started_matching': datetime.now().strftime("%Y-%m-%d"),
                                'latest_suggestions': datetime.now().strftime("%Y-%m-%d"),
                                'longitude': round(user_long, 2),
                                'latitude': round(user_lat, 2),
                                'upper_age_band': user_data.get('upperAgeReq'),
                                'lower_age_band': user_data.get('lowerAgeReq'),
                                'proximity_preference': 100000000,  # in m, not in km
                                'gender_preference': 'Female' if user_data.get('genderReq') == 'Women' else (
                                    'Male' if user_data.get('genderReq') == 'Men' else 'Everyone'),
                                'match_status': 'GlobalMatch',
                                'incognito': user_data.get('incognito', False),
                                'elo_score_raw': 1000}).fetchall()

                            db_conn.commit()

                            # Do something with the results
                            for row in result:
                                logging.info(f"Committed admitting user SQL: {element}")

                    admit_users_with_retry()

                except Exception as e:
                    logging.exception(e)
                    db.collection('SQLExceptions').add(
                        {'function': 'updateAdmittedUsersWaitingList', 'user': user_data.get('uid'),
                         'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

            rtdb_db = rtdb.reference('users')
            rtdb_user_ref = rtdb_db.child(element)
            rtdb_user_ref.update({
                "details/gender": user_doc.get("gender"),
                "details/signUpFinished": True,
            })

            push_notifications_collection = db.collection("ff_user_push_notifications")

            try:

                if region == 'GLOBAL_WL':

                    identify_obj = Identify()
                    identify_obj.set("Global Wl Admitted", True)
                    amplitude_client.identify(identify_obj, EventOptions(user_id=element))

                    identify_obj4 = Identify()
                    identify_obj4.set("On Global Waiting List", False)
                    amplitude_client.identify(identify_obj4, EventOptions(user_id=element))

                    # free_gold_eligible = random.choice([True, False])
                    #
                    # identify_obj5 = Identify()
                    # identify_obj5.set("Free Gold Eligible", free_gold_eligible)
                    # amplitude_client.identify(identify_obj5, EventOptions(user_id=element))
                    #
                    # user.update({"freeGoldShown": True, "freeGold2Shown": True})
                    #
                    # event = BaseEvent(event_type="Waiting List: Admitted from Global Wl", user_id=element)
                    # amplitude_client.track(event)
                    #
                    # if free_gold_eligible:
                    #     event = BaseEvent(event_type="Free Gold: Eligibility Granted", user_id=element)
                    #     amplitude_client.track(event)
                    # else:
                    #     event = BaseEvent(event_type="Free Gold: Eligibility Not Granted", user_id=element)
                    #     amplitude_client.track(event)

                else:
                    identify_obj = Identify()
                    identify_obj.set("Regional Wl Admitted", True)
                    amplitude_client.identify(identify_obj, EventOptions(user_id=element))

                    identify_obj4 = Identify()
                    identify_obj4.set("On Regional Waiting List", False)
                    amplitude_client.identify(identify_obj4, EventOptions(user_id=element))

                    event = BaseEvent(event_type="Waiting List: Admitted From Regional Wl", user_id=element)
                    amplitude_client.track(event)

                rc_secret = access_secret_version("Revenuecat_Key")
                session = create_retry_session()

                time_to_end = datetime(2099, 12, 31)

                grant_promotional_access_rc("standard_access", time_to_end, element, rc_secret, session)
                grant_promotional_access_rc("free_standard_lifetime", time_to_end, element, rc_secret, session)

            except:
                logging.info("Amplitude failed")

            if region == 'GLOBAL_WL':

                try:

                    user_notification_cohort = user_data.get("notificationTestingCohort")
                    if user_notification_cohort == "Emoji":

                        sendPushNotification(element, "Chyrpe",
                                             f"{get_translated_text("Congratulations, you’ve made it through the global waiting list! Get started matching now!", user_lang)} 🎉", push_notifications_collection)

                        event = BaseEvent(event_type="Notifications: Sent Admitted Global Waiting List Notification Emoji", user_id=element)
                        amplitude_client.track(event)

                    else:
                        sendPushNotification(element, "Chyrpe",
                                             f"{get_translated_text("Congratulations, you’ve made it through the global waiting list! Get started matching now!", user_lang)}", push_notifications_collection)

                        event = BaseEvent(event_type="Notifications: Sent Admitted Global Waiting List Notification NoEmoji", user_id=element)
                        amplitude_client.track(event)

                    event = BaseEvent(event_type="Notifications: Sent Admitted Global Waiting List Notification", user_id=element, event_properties={"Notification Testing Cohort": user_notification_cohort})
                    amplitude_client.track(event)

                except Exception as e:
                    logging.exception(e)


        except Exception as e:
            logging.exception(e)
            continue

        time.sleep(0.5)

    pool.dispose()
    connector.close()

