# openapi2-functions.yaml
swagger: '2.0'
info:
  title: Dev API Gateway
  description: Dev API Gateway for protected endpoints
  version: 1.0.0
schemes:
  - https
produces:
  - application/json
paths:
  /searchuser:
    get:
      summary: Search a user
      operationId: searchuser
      x-google-backend:
        address: https://europe-west1-greenrocks-9abc3.cloudfunctions.net/searchUsers
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /updateuserlocation:
    post:
      summary: Update user location
      operationId: updateuserlocation
      x-google-backend:
        address: https://europe-west2-greenrocks-9abc3.cloudfunctions.net/updateUserLocation
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /updateusergender:
    post:
      summary: Update user gender
      operationId: updateusergender
      x-google-backend:
        address: https://europe-west2-greenrocks-9abc3.cloudfunctions.net/updateUserGender
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /verificationGetBucket:
    get:
      summary: verificationGetBucket
      operationId: verificationGetBucket
      x-google-backend:
        address: https://verificationgetbucket-etxwrwwv3a-ew.a.run.app
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /verificationSubmitBucket:
    post:
      summary: verificationSubmitBucket
      operationId: verificationSubmitBucket
      x-google-backend:
        address: https://verificationsubmitbucket-etxwrwwv3a-ew.a.run.app
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /updateusername:
    post:
      summary: Update user name
      operationId: updateusername
      x-google-backend:
        address:  https://europe-west2-greenrocks-9abc3.cloudfunctions.net/updateUserName
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /updateuserage:
    post:
      summary: Update user age
      operationId: updateuserage
      x-google-backend:
        address: https://europe-west2-greenrocks-9abc3.cloudfunctions.net/updateUserAge
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /resetowndislikeswrapper:
    post:
      summary: Reset own dislikes wrapper
      operationId: resetowndislikeswrapper
      x-google-backend:
        address: https://europe-west2-greenrocks-9abc3.cloudfunctions.net/resetOwnDislikesWrapper
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /deleteuserimage:
    delete:
      summary: Delete user image
      operationId: deleteuserimage
      x-google-backend:
        address: https://europe-west2-greenrocks-9abc3.cloudfunctions.net/deleteUserImage
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /secureuserprofiledata:
    post:
      summary: Secure user profile data
      operationId: secureuserprofiledata
      x-google-backend:
        address: https://europe-west2-greenrocks-9abc3.cloudfunctions.net/secureUserProfileData
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /updateusernonbinary:
    post:
      summary: Update user non-binary
      operationId: updateusernonbinary
      x-google-backend:
        address: https://europe-west2-greenrocks-9abc3.cloudfunctions.net/updateUserNonBinary
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /getuserprofileformpe:
    get:
      summary: Get user profile for MPE
      operationId: getuserprofileformpe
      x-google-backend:
        address: https://europe-west2-greenrocks-9abc3.cloudfunctions.net/getUserProfileForMpe
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /moderatorrejectprofilewrapper:
    post:
      summary: Moderator reject profile wrapper
      operationId: moderatorrejectprofilewrapper
      x-google-backend:
        address: https://europe-west2-greenrocks-9abc3.cloudfunctions.net/moderatorRejectProfileWrapper
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /moderatorrerequestverification:
    post:
      summary: Moderator rerequest verification from user
      operationId: moderatorrerequestverification
      x-google-backend:
        address: https://moderatorrerequestverification-etxwrwwv3a-nw.a.run.app
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /getAttributions:
    get:
      summary: Get attributions for creator conversions
      operationId: getAttributions
      x-google-backend:
        address: https://getattributions-etxwrwwv3a-ew.a.run.app
      responses:
        '200':
          description: A successful response
          schema:
            type: string

securityDefinitions:
  dev_auth:
    authorizationUrl: ""
    flow: "implicit"
    type: "oauth2"
    # The value below should be unique
    x-google-issuer: "<EMAIL>"
    x-google-jwks_uri: "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>"
