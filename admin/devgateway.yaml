# openapi2-functions.yaml
swagger: '2.0'
info:
  title: Dev API Gateway
  description: Dev API Gateway for protected endpoints
  version: 1.0.0
schemes:
  - https
produces:
  - application/json
paths:
  /helloworld:
    get:
      summary: Test endpoint for Gateway
      operationId: helloworld
      x-google-backend:
        address: https://helloworld-44912806133.europe-west2.run.app
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /searchuser:
    get:
      summary: Search a user
      operationId: searchuser
      x-google-backend:
        address: https://searchusers-jgq2sox5xa-nw.a.run.app
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /verificationGetBucket:
    get:
      summary: verificationGetBucket
      operationId: verificationGetBucket
      x-google-backend:
        address: https://verificationgetbucket-jgq2sox5xa-ew.a.run.app
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /verificationSubmitBucket:
    post:
      summary: verificationSubmitBucket
      operationId: verificationSubmitBucket
      x-google-backend:
        address: https://verificationsubmitbucket-jgq2sox5xa-ew.a.run.app
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /updateuserage:
    post:
      summary: Update user age
      operationId: updateuserage
      x-google-backend:
        address: https://updateuserage-jgq2sox5xa-nw.a.run.app/
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /updateuserlocation:
    post:
      summary: Update user location
      operationId: updateuserlocation
      x-google-backend:
        address: https://updateuserlocation-jgq2sox5xa-nw.a.run.app/
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /updateusergender:
    post:
      summary: Update user gender
      operationId: updateusergender
      x-google-backend:
        address: https://updateusergender-jgq2sox5xa-nw.a.run.app/
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /updateusernonbinary:
    post:
      summary: Update user non-binary
      operationId: updateusernonbinary
      x-google-backend:
        address: https://updateusernonbinary-jgq2sox5xa-nw.a.run.app/
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /getuserprofileformpe:
    get:
      summary: Get user profile for MPE
      operationId: getuserprofileformpe
      x-google-backend:
        address: https://getuserprofileformpe-jgq2sox5xa-nw.a.run.app
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /moderatorrejectprofilewrapper:
    post:
      summary: Moderator reject profile wrapper
      operationId: moderatorrejectprofilewrapper
      x-google-backend:
        address: https://moderatorrejectprofilewrapper-jgq2sox5xa-nw.a.run.app/
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /moderatorapproveprofilewrapper:
    post:
      summary: Moderator approve profile wrapper
      operationId: moderatorapproveprofilewrapper
      x-google-backend:
        address: https://moderatorapproveprofilewrapper-jgq2sox5xa-nw.a.run.app/
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /updateusername:
    post:
      summary: Update user name
      operationId: updateusername
      x-google-backend:
        address: https://updateusername-jgq2sox5xa-nw.a.run.app/
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /resetowndislikeswrapper:
    post:
      summary: Reset own dislikes wrapper
      operationId: resetowndislikeswrapper
      x-google-backend:
        address: https://resetowndislikeswrapper-jgq2sox5xa-nw.a.run.app/
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /deleteuserimage:
    delete:
      summary: Delete user image
      operationId: deleteuserimage
      x-google-backend:
        address: https://deleteuserimage-jgq2sox5xa-nw.a.run.app/
      responses:
        '200':
          description: A successful response
          schema:
            type: string
  /secureuserprofiledata:
    post:
      summary: Secure user profile data
      operationId: secureuserprofiledata
      x-google-backend:
        address: https://secureuserprofiledata-jgq2sox5xa-nw.a.run.app/
      responses:
        '200':
          description: A successful response
          schema:
            type: string
securityDefinitions:
  dev_auth:
    authorizationUrl: ""
    flow: "implicit"
    type: "oauth2"
    # The value below should be unique
    x-google-issuer: "<EMAIL>"
    x-google-jwks_uri: "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>"
