"""
Endpoints used for data collection by admins
"""
from collections import Counter
from datetime import datetime, timedelta

import dateutil
from amplitude import Amplitude, BaseEvent, Identify, EventOptions
from firebase_admin import firestore, initialize_app
from firebase_functions import https_fn, options
import google.auth
from google.cloud import storage
from google.cloud.firestore_v1 import FieldFilter, And, aggregation, GeoPoint, \
    DocumentSnapshot, DocumentReference
import google.cloud.logging
from google.cloud.sql.connector import Connector
import json
import logging
import requests
from typing import Any
from shared import is_dev_environment, find_all_likes_of_two_users, connect_with_connector, \
    convert_firebase_url_to_gs, make_serializable, upload_json_from_string, \
    access_secret_version, with_retry, generateMpeTitleAndBody, sendPushNotification
from statistics import median
import sqlalchemy
from requests.adapters import HTTPAdapter, Retry
from google.cloud.sql.connector import Connector, IPTypes
import logging
import pg8000
from common_translations import get_translated_text

initialize_app()

amplitude_client = Amplitude("********************************") if not is_dev_environment() else Amplitude("********************************")

db_conn_new = None
def connect_with_connector_persist():
    # initialize Cloud SQL Python Connector object
    connector = Connector(refresh_strategy="LAZY")

    def getconn() -> pg8000.dbapi.Connection:
        conn: pg8000.dbapi.Connection = connector.connect(
            "greenrocks-9abc3:europe-west2:chyrpe-prod1",
            "pg8000",
            user="<EMAIL>",
            db="chyrpe_main_db",
            enable_iam_auth=True,
            ip_type=IPTypes.PUBLIC
        )
        return conn

    # The Cloud SQL Python Connector can be used with SQLAlchemy
    # using the 'creator' argument to 'create_engine'
    pool = sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        # [START_EXCLUDE]
        # Pool size is the maximum number of permanent connections to keep.
        pool_size=50,
        # Temporarily exceeds the set pool_size if no connections are available.
        max_overflow=20,
        # The total number of concurrent connections for your application will be
        # a total of pool_size and max_overflow.
        # 'pool_timeout' is the maximum number of seconds to wait when retrieving a
        # new connection from the pool. After the specified amount of time, an
        # exception will be thrown.
        pool_timeout=30,  # 30 seconds
        # 'pool_recycle' is the maximum number of seconds a connection can persist.
        # Connections that live longer than the specified amount of time will be
        # re-established
        pool_recycle=1800,  # 30 minutes
        # [END_EXCLUDE]
    )
    return pool


def detect_findom_content(scannable_content):
    findom = False

    from pydantic import BaseModel
    from openai import AzureOpenAI, BadRequestError

    client = AzureOpenAI(
        azure_endpoint="https://chyrpegptp.openai.azure.com/",
        api_key=access_secret_version("OpenAI_Key"),
        api_version="2024-10-21"
    )

    class LanguageEvaluation(BaseModel):
        input_text: str
        evaluation_result: int

    try:
        # Call the GPT model with the schema
        completion = client.beta.chat.completions.parse(
            model="gpt-4o-mini",  # replace with the actual model deployment name
            messages=[
                {"role": "system",
                 "content": """Act as a findom & professional detector that strictly and only outputs: 1 for true it is a findom/professional. or: 0 for false it is not a findom/professional. or: 2 for unsure / edgecase. A findom is somebody that wants to financially dominate another, we need to detect these people. A professional is someone trying to sell a service/good or a professional dominatrix, etc. Look for signs in this piece of text that this person is a findom/professional. Just because someone calls herself domme, dominatrix or dominant does not mean she is a professional, she is an edge case then. If she uses the prefix "pro" or "professional" in conjunction with any of the terms domme, dominatrix, dominant, she is to be classes as professional. Calling herself a findom (or any derivative of it) is a clear sign to class her as professional."""},
                {"role": "user", "content": scannable_content}
            ],
            response_format=LanguageEvaluation,  # Using the schema here
        )

        # Parse and print the result
        evaluation = completion.choices[0].message.to_dict().get("parsed").get("evaluation_result")

        logging.info(evaluation)

        if evaluation == 0 or evaluation == 2:
            findom = False
        else:
            findom = True

    except Exception as e:
        logging.error(e)
        findom = False

    return findom


def grant_promotional_access_rc(entitlement_name: str, time_to_end: datetime, user_id: str, secret: str,
                                s: requests.Session) -> bool:
    timestamp_end = int(time_to_end.timestamp() * 1000)

    url = f"https://api.revenuecat.com/v1/subscribers/{user_id}/entitlements/{entitlement_name}/promotional"

    payload = json.dumps({
        "end_time_ms": timestamp_end
    })
    headers = {
        'Authorization': f'Bearer {secret}',
        'Content-Type': 'application/json'
    }

    response = s.post(url, headers=headers, data=payload)

    if response.status_code in [200, 201]:
        return True
    else:
        return False



def create_retry_session() -> requests.Session:
    session = requests.Session()
    retries = Retry(total=5, backoff_factor=0.1, status_forcelist=[429, 500, 502, 503, 504])
    session.mount('https://', HTTPAdapter(max_retries=retries))
    return session


def incrementRateLimitDay():
    """Increase requests made on current day, counting towards admin rate quotas"""

    db = firestore.client()

    rate_limit_collection = db.collection("adminRateLimits")

    datetime_string = datetime.now().strftime("%Y%m%d")

    rate_limit_collection.document(datetime_string).set({"quotaCount": firestore.firestore.Increment(1)},
                                                            merge=True)

def isRateLimited() -> bool:
    """Determines if quota has been reached for given day"""

    db = firestore.client()

    rate_limit_collection = db.collection("adminRateLimits")

    # get today's used quota
    datetime_string = datetime.now().strftime("%Y%m%d")
    today_limit = rate_limit_collection.document(datetime_string).get()
    today_quota_count = today_limit.get("quotaCount") if today_limit.exists else 0

    # get limit quota
    quota_doc = rate_limit_collection.document("quota").get()
    quota = quota_doc.get("quota")

    # return if quota has been exceeded
    return today_quota_count >= quota

def search_user_return_fields(user: DocumentSnapshot) -> dict:
    """
    Get `uid`, `name`, `birthday`, `age`, `city`, `location`, `email`, `phone number` from user record
    """
    user_dict = user.to_dict()
    desired_keys = ['uid', 'name', 'age', 'location', 'city', 'gender', 'genderChoice']
    res_data = {key: user_dict[key] for key in desired_keys if key in user_dict}
    # convert birthday to ISO date string
    if 'birthday' in res_data:
        birthday: datetime = res_data['birthday']
        res_data['birthday'] = birthday.isoformat()
    # convert GeoPoint to lat & long
    if 'location' in res_data:
        location: GeoPoint = res_data['location']
        res_data['location'] = {'latitude': int(location.latitude), 'longitude': int(location.longitude)}
    # add uid
    res_data['uid'] = user.id
    return res_data

def send_ampli_event(name, uid, params=None):
    """Sends an event to Amplitude with optional parameters"""

    try:

        event = BaseEvent(event_type=name, user_id=uid,
                          event_properties=params)
        amplitude_client.track(event)

    except Exception as e:
        logging.exception(e)


def set_ampli_user_property(property, value, uid):
    """Sets a new user property for given user in Amplitude"""

    try:

        identify_obj = Identify()
        identify_obj.set(property, value)
        amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

    except Exception as e:
        logging.exception(e)


def track_and_notify_verification_successful(uid, manual, language):
    set_ampli_user_property('Verified', True, uid)
    send_ampli_event('Verification: Successful', uid, {'Manual': manual})

    sendPushNotification(uid, "Chyrpe",
                         get_translated_text("Congratulations, you've been verified successfully!", language))

    return


def track_and_notify_verification_failed(uid, manual, reason, outcome='Retry Manual', language='en'):
    if manual:
        set_ampli_user_property('Manual Verification', True, uid)

    send_ampli_event('Verification: Failed', uid,
                     {'Manual': manual, 'Reason': reason, 'Outcome': outcome})

    sendPushNotification(uid, get_translated_text("Your verification was reviewed", language), get_translated_text("Please open the Chyrpe app to see details.", language))

    return


from urllib.parse import unquote
@https_fn.on_request(region="europe-west1", cors=options.CorsOptions(cors_origins="*", cors_methods=["get"]), memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def verificationGetBucket(req: https_fn.Request) -> https_fn.Response:
    """Gets given bucket of verifications """

    db = firestore.client()
    users_collection = db.collection("users")

    bucket_type = unquote(req.args.get('bucket'))

    buckets = ["Images", "Age", "High Risk", "Gender Match", "Face Match", "Image Liveness", "Permanently Pending", "User Request", "Old Style"]

    print(bucket_type)

    users_return_list = []
    return_uids = []

    if bucket_type not in buckets:
        return https_fn.Response("Bucket does not exist", 400)

    for i in range(1, 5):

        if bucket_type == "Old Style":
            users_query = users_collection.where(filter=FieldFilter("manualVerification", "==", True)).where(
                filter=FieldFilter("verificationGoingOn", "==", True)).limit(20*i)
        else:
            users_query = users_collection.where(filter=FieldFilter("manualVerification", "==", True)).where(
                filter=FieldFilter("verificationGoingOn", "==", True)).where(
                filter=FieldFilter("manualVerificationReason", "==", bucket_type)).limit(20*i)


        users_snapshots = users_query.get()

        for u in users_snapshots:

            user_dict = {}

            user_data = u.to_dict()

            # this ensures if the user has recently been made a decision on, they are not shown again - important because of highly asynchronous submit bucket operation
            if user_data.get('latestVerificationDecisionTime', '') > (datetime.now() - timedelta(minutes=30)).strftime(
                    "%m/%d/%Y, %H:%M:%S") or u.id in return_uids:
                continue

            user_basics = ["uid", "name", "timesRejected", "gender", "age", "wlRegion", "scammerFace"]

            for b in user_basics:
                user_dict[b] = user_data.get(b, 0)

            user_public_profile = user_data.get("publicProfile").get().to_dict()
            n_pictures = user_public_profile.get("nPictures", [])
            picture_dicts = []

            for n in n_pictures:

                n_data = n.get().to_dict()

                picture_dicts.append({"imageId": n.id, "imageUrl": n_data.get("url"), "face": n_data.get("face", False), "explicit": n_data.get("explicit", False), "ownFace": n_data.get("ownFace", False)})

            user_dict["profileImages"] = picture_dicts
            user_dict["verificationImages"] = [{"imageId": "0", "imageUrl": user_data.get("verificationPhoto")}]

            if user_data.get("verificationPhoto") == None:
                try:
                    verifications_collection = db.collection('verifications')

                    latest_verification = verifications_collection.where(filter=FieldFilter("uid", "==", u.id)).order_by("timeCreated", direction=firestore.Query.DESCENDING).limit(1).get()[0].to_dict()

                    user_dict["verificationImages"] = [{"imageId": "0", "imageUrl": latest_verification.get("verificationPhoto")}]

                except Exception as e:
                    logging.exception(e)

            # get past verification images where reasonable
            
            past_verifications_log = db.collection("users").document(u.id).collection("meta").document("verificationRequests").get()
            past_verifications_photos = []

            if past_verifications_log.exists:
                past_verifications_data = past_verifications_log.to_dict()
                
                for key, value in past_verifications_data.items():
                    for verification in value:
                        past_verifications_photos.append(verification.get('imageUrl'))

            user_dict["pastVerificationImages"] = past_verifications_photos

            users_return_list.append(user_dict)
            return_uids.append(u.id)

            if len(users_return_list) > 20:
                break

    return https_fn.Response(json.dumps({"bucket": bucket_type, "users": users_return_list}), 200)

def ordinal(n):
    """Convert an integer into its ordinal representation: 1 -> 1st, 2 -> 2nd, etc."""
    return f"{n}{'tsnrhtdd'[(n//10%10!=1)*(n%10<4)*n%10::4]}"

def generate_image_reject_title_body(all_image_ids, violating_image_ids, face):
    explicit_items = {}

    if violating_image_ids:
        indices = [all_image_ids.index(img_id) + 1 for img_id in violating_image_ids if img_id in all_image_ids]
        ordinals = [ordinal(i) for i in indices]
        explicit_items["image"] = ordinals

    explicit_entries = list(explicit_items.keys())

    if not explicit_entries and not face:
        title = "Add a clear face picture to get verified"
        description = "Every Chyrpe profile must include a clear photo of your face"
        return title, description

    elif not explicit_entries:
        title = "Edit your profile to get verified"
        description = """It features content not allowed outside the kink area, like sexualising or kinky themes.
            Kink is welcome in the new profile kink area or when a woman allows it in your chat."""
        return title, description

    else:
        image_ordinals = explicit_items["image"]
        if len(image_ordinals) > 1:
            ordinal_str = ", ".join(image_ordinals[:-1]) + " and " + image_ordinals[-1]
        else:
            ordinal_str = image_ordinals[0]
        title = f"Edit your {ordinal_str} image to get verified"

        description = "These images feature sexualising or kinky themes. This is not allowed on Chyrpe."
        if not face:
            description += "\n\nPlease also add at least one image of your face. "

        return title, description

def disapprove_image_verifiation(uid, n_pictures, explicit_images, face_images, title_override=None, description_override=None):
    """Handles updating user state to disapprove verification images, whether moderator (can override title & description) or not"""

    db = firestore.client()

    users_reference = db.collection("users").document(uid)

    title, description = generate_image_reject_title_body(n_pictures, explicit_images, bool(face_images))

    users_reference.update({
        "imageVeriHasFace": True if face_images else False,
        "imageVeriMadeChanges": False,
        "imageVeriHasExplicit": True if explicit_images else False,
        "imageVerified": False,
        "imageVeriFailed": True,
        "imageVeriImprovementImages": explicit_images,
        "verificationCustomTitle": title_override if title_override else title,
        "verificationCustomSubtitle": description_override if description_override else description,
        "verificationFailReason": description_override if description_override else f"{title}. {description}.",
    })

import nmatching as nm
def request_retry_manual(uid, title_override=None, description_override=None):
    """Handles updating user state to disapprove verification images, whether moderator (can override title & description) or not"""

    db = firestore.client()

    users_reference = db.collection("users").document(uid)

    fallback_title = "We could not verify you yet"

    fallback_description = "Please try again"

    users_reference.update({
        "verificationCustomTitle": title_override if title_override else fallback_title,
        "verificationCustomSubtitle": description_override if description_override else fallback_description,
        "verificationFailReason": description_override if description_override else "Please upload a new verification image below. Ensure you are in a well-lit room and your face is clearly visible."
    })

def request_id_manual(uid, title_override=None, description_override=None):
    """Handles updating user state to disapprove verification images, whether moderator (can override title & description) or not"""

    db = firestore.client()

    users_reference = db.collection("users").document(uid)

    fallback_title = "Please upload your government-issued ID"

    fallback_description = "We would like to verify your age."

    users_reference.update({
        "verificationCustomTitle": title_override if title_override else fallback_title,
        "verificationCustomSubtitle": description_override if description_override else fallback_description,
        "verificationFailReason": fallback_title
    })

def profileSetUpMen(uid, pool) -> Any:
    """
    Series of steps and database update to make when a man is verified & ready to start matching
    :param uid: User's uid
    :return: None
    """

    # initialize firestore
    db = firestore.client()

    # set user doc ref and get user doc, extract data
    user_doc_ref = db.collection("users").document(uid)
    user_doc = user_doc_ref.get()
    user_doc_data = user_doc.to_dict()
    user_data = user_doc_data

    user_publicProfile = user_doc_data.get("publicProfile")

    # extract data from user doc, to be used in process
    gender = user_doc_data.get("gender")
    location = user_doc_data.get("location", False)
    location_city = user_doc_data.get("city", False)
    location_region = user_doc_data.get("wlRegion", False)
    user_local_matching = user_doc_data.get("localMatching", False)
    user_ref_code = user_data.get("refCode", "")
    chyrpeMatchRef = user_doc_data.get("chyrpeMatchRef", False)
    like_ref = chyrpeMatchRef


    # adds the Chyrpe Chat match if user is in the right state (location set) and Chyrpe Chat not set yet
    if gender == "Male" and location != False and location_city != False and location_region != False and chyrpeMatchRef == False:
        chyrpe_user = db.collection("users").document('fehBchlcouUcVwb66DJL03ZQJPH3')
        update_time, like_ref = db.collection('likes').add({'likingUser': chyrpe_user,
                                                            'likedUser': user_doc_ref,
                                                            'evolvedLike': False,
                                                            'speakAllow': True,
                                                            'chatting': True,
                                                            'unmatched': False,
                                                            'mutual': True,
                                                            'recentMessageText': "Welcome to chyrpe!",
                                                            'recentMessageSeenBy': [
                                                                chyrpe_user, user_doc_ref
                                                            ],
                                                            'involvedUsersProfileRefOnly':
                                                                [user_doc.get('publicProfile'),
                                                                 chyrpe_user.collection(
                                                                     'publicProfile').document(
                                                                     '80KmP3ztM6AJVozLcujS')],
                                                            'involvedUsers':
                                                                [chyrpe_user, user_doc_ref],
                                                            'involedUIDs': [
                                                                'fehBchlcouUcVwb66DJL03ZQJPH3', uid]
                                                            })

        update_time_mes, message_ref = like_ref.collection('messages').add({
            'body': "Welcome to chyrpe! We hope you will enjoy your experience. This chat connects you directly to the chyrpe team. If you have any questions, you can tell us here and a real person will get back to you. For reporting a user, please use the dedicated button on each profile, not this chat.",
            'time':
                firestore.SERVER_TIMESTAMP,
            'sender':
                chyrpe_user,
            'isSysMessage':
                False})

    if gender == "Male" and location != False and location_city != False and location_region != False:
        user_doc_ref.update(
            {'signUpFinished': True, 'freeGoldShown': True, 'freeGold2Shown': True,
             'chyrpeStandardSubscriber': True, "chyrpeMatchRef": like_ref,
             "accountCreationFinished": firestore.SERVER_TIMESTAMP, "lowerHeightReq": 0,
             "upperHeightReq": 250, "pnMa": True,
             "pnMe": True, "distanceReq": 500.1, "globalWlAdmitted": True})

        try:

            u_location = user_doc_data.get('location')
            u_lat = u_location.latitude
            u_long = u_location.longitude

            here_key = access_secret_version("Here_Key")

            url = f"https://revgeocode.search.hereapi.com/v1/revgeocode?at={u_lat},{u_long}&lang=en-US&apiKey={here_key}"
            payload = {}
            headers = {}
            response = requests.request("GET", url, headers=headers, data=payload)

            try:

                country_name = response.json().get("items", {})[0].get("address").get("countryName")
                state = response.json().get("items", {})[0].get("address").get("state")
                city = response.json().get("items", {})[0].get("address").get("city")

            except:

                country_name = "Unknown"
                state = "Unknown"
                city = "Unknown"

            identify_obj1 = Identify()
            identify_obj1.set("Country_EN", country_name)
            amplitude_client.identify(identify_obj1, EventOptions(user_id=uid))
            identify_obj2 = Identify()
            identify_obj2.set("State_EN", state)
            amplitude_client.identify(identify_obj2, EventOptions(user_id=uid))
            identify_obj3 = Identify()
            identify_obj3.set("City_EN", city)
            amplitude_client.identify(identify_obj3, EventOptions(user_id=uid))
            identify_obj = Identify()
            identify_obj.set("Global Wl Admitted", True)
            amplitude_client.identify(identify_obj, EventOptions(user_id=uid))
            identify_obj5 = Identify()
            identify_obj5.set("Sign Up Finished", True)
            amplitude_client.identify(identify_obj5, EventOptions(user_id=uid))

            if user_local_matching:
                identify_obj = Identify()
                identify_obj.set("Regional Wl Admitted", True)
                amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

            user_doc_ref.update({'city': city, 'country': country_name, 'state': state})

            event1 = BaseEvent(event_type="Started Matching", user_id=uid)
            amplitude_client.track(event1)

            event2 = BaseEvent(event_type="Man Started Matching", user_id=uid)
            amplitude_client.track(event2)

            user_publicProfile.update({'city': city})

        except Exception as e:
            logging.exception(e)

            logging.info("Amplitude failed")

        # add him to Postgres
        try:
            try:

                @with_retry()
                def admitUser(pool):
                    user_long = user_data.get('location').longitude
                    user_lat = user_data.get('location').latitude

                    with pool.connect() as db_conn:
                        stmt = sqlalchemy.text(

                            """SELECT add_user(:user_id, :age, :date_of_birth, :gender, :role, :gender_preference, :longitude, :latitude, :upper_age_band, :lower_age_band,
                            :proximity_preference, :elo_score, :elo_score_raw, :match_status, :incognito,
                            :started_matching, :latest_suggestions, :findom_interest)""")

                        # Execute with the parameters

                        result = db_conn.execute(stmt, {
                            'user_id': user_data.get('uid'),
                            'age': user_data.get('age'),
                            'date_of_birth': user_data.get('birthday').strftime("%Y-%m-%d"),
                            'gender': user_data.get('gender'),
                            'role': user_data.get('position').capitalize(),
                            'elo_score': 0.5,
                            'started_matching': datetime.now().strftime("%Y-%m-%d"),
                            'latest_suggestions': datetime.now().strftime("%Y-%m-%d"),
                            'longitude': round(user_long, 2),
                            'latitude': round(user_lat, 2),
                            'upper_age_band': user_data.get('upperAgeReq'),
                            'lower_age_band': user_data.get('lowerAgeReq'),
                            'proximity_preference': 100000000,  # in m, not in km
                            'gender_preference': 'Female' if user_data.get(
                                'genderReq') == 'Women' else (
                                'Male' if user_data.get('genderReq') == 'Men' else 'Everyone'),
                            'match_status': 'RegionalMatch' if user_local_matching else 'GlobalMatch',
                            'incognito': user_data.get('incognito', False),
                            'findom_interest': get_findom_interest_status(user_data.get("kinks", []), user_data.get("professionalsReq", False)),
                            'elo_score_raw': 1000}).fetchall()

                        db_conn.commit()

                if not is_dev_environment():
                    admitUser(pool)


            except Exception as e:

                logging.exception(e)
                db.collection('SQLExceptions').add(
                    {'function': 'profileSetUpMen', 'user': user_data.get('uid'),
                     'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

            except:
                logging.exception(f"Fail Profile Setup Women: {uid}")

        except:
            logging.exception("SQL Error")
        if not is_dev_environment():
            try:
              nm.update_user_current(uid)
            except Exception as e:
                logging.exception(e)

    if not is_dev_environment():
        # if the user has a valid referral code
        if user_ref_code != '' and user_ref_code != 'default':
            try:
                import referral_helper as rh

                rh.attributeReferredUser(uid)
            except Exception as e:
                logging.error(e)

    return


def profileSetUpWomen(uid, pool) -> Any:
    """
        Series of steps and database update to make when a woman is verified & ready to start matching
        :param uid: User's uid
        :return: None
    """
    # initialize firestore
    db = firestore.client()

    # set user doc ref and get user doc, extract data
    user_doc_ref = db.collection("users").document(uid)
    user_doc = user_doc_ref.get()
    user_doc_data = user_doc.to_dict()
    user_data = user_doc_data

    user_publicProfile = user_doc_data.get("publicProfile")

    gender = user_doc_data.get("gender")
    location = user_doc_data.get("location", False)
    location_city = user_doc_data.get("city", False)
    location_region = user_doc_data.get("wlRegion", False)
    chyrpeMatchRef = user_doc_data.get("chyrpeMatchRef", False)
    like_ref = chyrpeMatchRef

    findom = False

    try:
        user_public_profile_data = user_publicProfile.get().to_dict()
        user_bio = user_public_profile_data.get("bio", "")
        prompts = user_public_profile_data.get("prompts", [])
        promptsEdited = ". ".join(prompt.get('answer', '') for prompt in prompts if 'answer' in prompt)
        scannable_content = ", ".join([user_bio, promptsEdited])

        print(scannable_content)

        findom = detect_findom_content(scannable_content)

    except Exception as e:
        logging.exception(e)



    # adds the Chyrpe Chat match if user is in the right state (location set) and Chyrpe Chat not set yet
    if gender == "Female" and location != False and location_city != False and location_region != False:
        chyrpe_user = db.collection("users").document('fehBchlcouUcVwb66DJL03ZQJPH3')
        if chyrpeMatchRef == False:
            update_time, like_ref = db.collection('likes').add({'likingUser': chyrpe_user,
                                                                'likedUser': user_doc_ref,
                                                                'evolvedLike': False,
                                                                'speakAllow': True,
                                                                'chatting': True,
                                                                'unmatched': False,
                                                                'mutual': True,
                                                                'recentMessageText': "Welcome to chyrpe!",
                                                                'recentMessageSeenBy': [
                                                                    chyrpe_user, user_doc_ref
                                                                ],
                                                                'involvedUsersProfileRefOnly':
                                                                    [user_doc.get('publicProfile'),
                                                                     chyrpe_user.collection(
                                                                         'publicProfile').document(
                                                                         '80KmP3ztM6AJVozLcujS')],
                                                                'involvedUsers':
                                                                    [chyrpe_user, user_doc_ref],
                                                                'involedUIDs': [
                                                                    'fehBchlcouUcVwb66DJL03ZQJPH3', uid]
                                                                })
            update_time_mes, message_ref = like_ref.collection('messages').add({
                'body': "Welcome to chyrpe! We hope you will enjoy your experience. This chat connects you directly to the chyrpe team. If you have any questions, you can tell us here and a real person will get back to you. For reporting a user, please use the dedicated button on each profile, not this chat.",
                'time':
                    firestore.SERVER_TIMESTAMP,
                'sender':
                    chyrpe_user,
                'isSysMessage':
                    False})

    if gender == "Female" and location != False and location_city != False and location_region != False:
        # query whether her region is available and she should get into global or local matching
        region_collection = db.collection("openRegions")
        her_region_query = region_collection.where("name", "==", location_region)
        her_region = her_region_query.get()

        # some regions are open as a preview only to women, also note that
        region_collection_pre = db.collection("openRegionsPre")
        her_pre_region_query = region_collection_pre.where("name", "==", location_region)
        her_pre_region = her_pre_region_query.get()

        waiting_list_deferred_admission_collection = db.collection("wlDeferredAdmission")

        if len(her_region) > 0:
            user_doc_ref.update(
                {'signUpFinished': True, 'freeGoldShown': True, 'freeGold2Shown': True,
                 'chyrpeStandardSubscriber': True, "chyrpeMatchRef": like_ref,
                 "accountCreationFinished": firestore.SERVER_TIMESTAMP, "globalMatching": False,
                 "localMatching": True, "lowerHeightReq": 0, "upperHeightReq": 250, "pnMa": True,
                 "pnMe": True, "distanceReq": 100, "localWlAdmitted": True, "globalWlAdmitted": True, "findom": findom})
            get_deferred_list_query = waiting_list_deferred_admission_collection.where(
                filter=FieldFilter("region", "==", location_region))
            deferred_admission_docs = get_deferred_list_query.get()

            # admit men into regional matching (deferred)
            if len(deferred_admission_docs) > 0:
                deferred_admission_doc = deferred_admission_docs[0]
                deferred_admission_doc.reference.update(
                    {"remainingAdmits": firestore.Increment(10)})
            else:
                waiting_list_deferred_admission_collection.add(
                    {"region": location_region, "remainingAdmits": 10})

        elif len(her_pre_region) > 0:
            user_doc_ref.update(
                {'signUpFinished': True, 'freeGoldShown': True, 'freeGold2Shown': True,
                 'chyrpeStandardSubscriber': True, "chyrpeMatchRef": like_ref,
                 "accountCreationFinished": firestore.SERVER_TIMESTAMP, "globalMatching": False,
                 "localMatching": True, "lowerHeightReq": 0, "upperHeightReq": 250, "pnMa": True,
                 "pnMe": True, "distanceReq": 500.1, "localWlAdmitted": True, "globalWlAdmitted": True,
                 "findom": findom})

        else:
            # put her into global matching if regional is not available + admit men globally
            user_doc_ref.update({'signUpFinished': True, 'chyrpeStandardSubscriber': True,
                                 "chyrpeMatchRef": like_ref,
                                 "accountCreationFinished": firestore.SERVER_TIMESTAMP,
                                 "globalMatching": True,
                                 "localMatching": False, "lowerHeightReq": 0,
                                 "upperHeightReq": 250, "pnMa": True, "pnMe": True, "distanceReq": 500.1,
                                 "findom": findom})

            get_deferred_list_query = waiting_list_deferred_admission_collection.where(
                filter=FieldFilter("region", "==", "GLOBAL_WL"))
            deferred_admission_docs = get_deferred_list_query.get()

            try:
                user_data.get("publicProfile").update({"cityHidden": False})
            except Exception as e:
                logging.exception(e)

            if len(deferred_admission_docs) > 0:
                deferred_admission_doc = deferred_admission_docs[0]
                deferred_admission_doc.reference.update(
                    {"remainingAdmits": firestore.Increment(10)})  # for test
            else:
                waiting_list_deferred_admission_collection.add(
                    {"region": "GLOBAL_WL", "remainingAdmits": 10})

        try:

            u_location = user_doc_data.get('location')
            u_lat = u_location.latitude
            u_long = u_location.longitude

            here_key = access_secret_version("Here_Key")
            url = f"https://revgeocode.search.hereapi.com/v1/revgeocode?at={u_lat},{u_long}&lang=en-US&apiKey={here_key}"
            payload = {}
            headers = {}

            response = requests.request("GET", url, headers=headers, data=payload)

            try:
                country_name = response.json().get("items", {})[0].get("address").get("countryName")
                state = response.json().get("items", {})[0].get("address").get("state")
                city = response.json().get("items", {})[0].get("address").get("city")
            except:
                country_name = "Unknown"
                state = "Unknown"
                city = "Unknown"

            identify_obj1 = Identify()
            identify_obj1.set("Country_EN", country_name)
            amplitude_client.identify(identify_obj1, EventOptions(user_id=uid))

            identify_obj2 = Identify()
            identify_obj2.set("State_EN", state)
            amplitude_client.identify(identify_obj2, EventOptions(user_id=uid))

            identify_obj3 = Identify()
            identify_obj3.set("City_EN", city)
            amplitude_client.identify(identify_obj3, EventOptions(user_id=uid))

            identify_obj = Identify()
            identify_obj.set("Global Wl Admitted", True)
            amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

            identify_obj5 = Identify()
            identify_obj5.set("Sign Up Finished", True)
            amplitude_client.identify(identify_obj5, EventOptions(user_id=uid))

            if len(her_region) > 0 or len(her_pre_region) > 0:
                identify_obj = Identify()
                identify_obj.set("Regional Wl Admitted", True)
                amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

            user_doc_ref.update({'city': city, 'country': country_name, 'state': state})
            user_publicProfile.update({'city': city, "findom": findom})

            event1 = BaseEvent(event_type="Started Matching", user_id=uid)
            amplitude_client.track(event1)

            event2 = BaseEvent(event_type="Woman Started Matching", user_id=uid)
            amplitude_client.track(event2)
        except:
            logging.info("Amplitude failed")

        # add her to Postgres
        try:
            try:

                @with_retry()
                def admitUserW(pool):

                    user_long = user_data.get('location').longitude
                    user_lat = user_data.get('location').latitude

                    with pool.connect() as db_conn:
                        # query database
                        stmt = sqlalchemy.text(
                            """SELECT add_user(:user_id, :age, :date_of_birth, :gender, :role, :gender_preference, :longitude, :latitude, :upper_age_band, :lower_age_band,
                            :proximity_preference, :elo_score, :elo_score_raw, :match_status, :incognito,
                            :started_matching, :latest_suggestions)""")

                        result = db_conn.execute(stmt, {
                            'user_id': user_data.get('uid'),
                            'age': user_data.get('age'),
                            'date_of_birth': user_data.get('birthday').strftime("%Y-%m-%d"),
                            'gender': user_data.get('gender'),
                            'role': user_data.get('position').capitalize(),
                            'elo_score': 0.5,
                            'started_matching': datetime.now().strftime("%Y-%m-%d"),
                            'latest_suggestions': datetime.now().strftime("%Y-%m-%d"),
                            'longitude': round(user_long, 2),
                            'latitude': round(user_lat, 2),
                            'upper_age_band': user_data.get('upperAgeReq'),
                            'lower_age_band': user_data.get('lowerAgeReq'),
                            'proximity_preference': 100000000,  # in m, not in km
                            'gender_preference': 'Female' if user_data.get(
                                'genderReq') == 'Women' else (
                                'Male' if user_data.get('genderReq') == 'Men' else 'Everyone'),
                            'match_status': 'RegionalMatch' if (
                                    len(her_region) > 0 or len(her_pre_region) > 0) else 'GlobalMatch',
                            'incognito': user_data.get('incognito', False),
                            'elo_score_raw': 1000}).fetchall()

                        db_conn.commit()

                if not is_dev_environment():
                    admitUserW(pool)

            except Exception as e:
                logging.exception(e)
                db.collection('SQLExceptions').add(
                    {'function': 'profileSetUpWomen', 'user': user_data.get('uid'),
                     'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

            except:
                logging.exception(f"Fail Profile Setup Women: {uid}")
        except:
            print("SQL Error")

        try:
            if not is_dev_environment():
                nm.update_user_current(uid)

            if findom:
                db.collection("findomEnable").add({"uid": uid})

        except Exception as e:
            logging.exception(e)

    return


@https_fn.on_request(region="europe-west1", cors=options.CorsOptions(cors_origins="*", cors_methods=["post"]), memory=options.MemoryOption.GB_16, timeout_sec=3600, cpu=4)
def verificationSubmitBucket(req: https_fn.Request) -> https_fn.Response:

    db = firestore.client()
    users_collection = db.collection("users")

    global db_conn_new

    if db_conn_new == None:
        db_conn_new = connect_with_connector_persist()

    json_request = req.get_json()

    logging.info(json_request)

    logging.info(req)

    logging.info(req.json)

    users_list = json_request.get("data").get("users", [])

    for u in users_list:

        latest_verifications_time = datetime.now().strftime("%m/%d/%Y, %H:%M:%S")

        try:
        # updates the user right away with the recent decision time

            users_collection.document(u.get("uid")).update({
                "latestVerificationDecisionTime": latest_verifications_time
            })

        except Exception as e:
            logging.exception(e)
            continue


    for u in users_list:

        try:

            uid = u.get("uid")
            print(uid)
            user_reference = users_collection.document(uid)

            action = u.get("action")

            title = u.get("title")
            subtitle = u.get("subtitle")

            own_face_images = u.get("ownFaceImages", [])
            not_own_face_images = u.get("notOwnFaceImages", []) # these are images a moderator has reclassified from own face image -> not own face image

            for o in own_face_images:
                user_reference.collection("images").document(o).update({'face': True, 'ownFace': True})
                user_reference.collection('meta').document('imageCompliance').update({f'currentImages.{o}.ownFace': True, f'currentImages.{o}.face': True})

            for no in not_own_face_images: 
                user_reference.collection("images").document(no).update({'ownFace': False})
                user_reference.collection('meta').document('imageCompliance').update({f'currentImages.{no}.ownFace': False})

            verification_photo_replacement = u.get("verificationPhotoReplacement", None)
            if verification_photo_replacement:
                user_reference.update({"verificationPhoto": verification_photo_replacement})  

            # for backward compatibility to old verifications system, update any verifications document to maintain compatibility
            try:
                verifications_collection = db.collection('verifications')

                latest_verification = verifications_collection.where(filter=FieldFilter("uid", "==", uid)).order_by("timeCreated",
                                                                                               direction=firestore.Query.DESCENDING).limit(1).get()[0]

                latest_verification.reference.update({'handled': True, 'handledByNewStyle': True})

            except Exception as e:
                logging.exception(e)

            match action:

                case "Reject: Marked Images or No Face":

                    logging.info(f"Updating user {action} {uid}")

                    explicit_images = [i.get('imageId') for i in u.get("flaggedImages")] if 'flaggedImages' in u else []
                    face_images = not u.get("noFace")
                    user_data = user_reference.get().to_dict()

                    public_profile = db.collection('users').document(uid).collection("publicProfile").limit(1).get()[
                        0].to_dict()
                    n_pictures_raw = public_profile.get("nPictures", [])
                    n_pictures = [n.id for n in n_pictures_raw]

                    disapprove_image_verifiation(uid, n_pictures, explicit_images, face_images, title, subtitle)

                    user_reference.update({"verificationGoingOn": False, "imageVeriHumanRejected": True, "timesRejected": firestore.firestore.Increment(1),
                                           "verificationFailed": True, "manualVerification": True, "autoVerificationStarted": False})

                    track_and_notify_verification_failed(uid, True, 'Images', user_data.get("language", "en"))

                case "Reject: Retry Manual":
                    user_data = user_reference.get().to_dict()
                    logging.info(f"Updating user {action} {uid}")

                    request_retry_manual(uid, title, subtitle)

                    user_reference.update({"verificationGoingOn": False, "autoVerificationStarted": False, "timesRejected": firestore.firestore.Increment(1),
                                           "verificationFailed": True, "manualVerification": True})

                    track_and_notify_verification_failed(uid, True, 'General/Technical', user_data.get("language", "en"))

                case "Reject: Id Check":
                    user_data = user_reference.get().to_dict()
                    logging.info(f"Updating user {action} {uid}")

                    request_id_manual(uid, title, subtitle)

                    user_reference.update({"verificationGoingOn": False, "idVerification": True, "autoVerificationStarted": False, "timesRejected": firestore.firestore.Increment(1),
                                           "verificationFailed": True, "manualVerification": True})

                    track_and_notify_verification_failed(uid, True, 'ID Check', 'ID Manual', user_data.get("language", "en"))

                case "Reject: Permanent":


                    logging.info(f"Updating user {action} {uid}")

                    user_reference.update({"verificationGoingOn": True, "idVerification": False, "autoVerificationStarted": False, "timesRejected": firestore.firestore.Increment(1),
                                           "verificationFailed": True, "manualVerification": True,
                                           "manualVerificationReason": "Permanently Pending"})

                    # do not notify user here
                    send_ampli_event('Verification: Failed', uid, {'Manual': True, 'Reason': 'Permanent', 'Outcome': 'Permanently Pending'})


                case "Accept: Verified":

                    logging.info(f"Updating user {action} {uid}")

                    non_binary = u.get("nonBinary")
                    gender = u.get("gender")

                    if gender:
                        user_reference.update({"gender": gender, "standardDecisionMade": datetime.now().strftime("%Y%m%d")})

                        send_ampli_event('Profile: Update Gender', uid, {'Moderator': True})

                    if non_binary:
                        user_reference.update({"genderConfirm": True})

                        send_ampli_event('Profile: Add Non-Binary and Request Gender Confirm', uid, {'Moderator': True})


                    user_data = user_reference.get().to_dict()

                    user_gender = user_data.get("gender")

                    n_pictures = user_data.get("publicProfile").get().to_dict().get("nPictures", [])

                    for n in n_pictures:
                        user_reference.collection("images").document(n.id).update({'explicit': False})
                        user_reference.collection('meta').document('imageCompliance').update({f'currentImages.{n.id}.explicit': False})


                    if user_gender == "Female":
                        profileSetUpWomen(uid, db_conn_new)


                    elif user_gender == "Male":

                        # relevant for old app versions, deprecated

                        profileSetUpMen(uid, db_conn_new)
                        rc_secret = access_secret_version("Revenuecat_Key")
                        session = create_retry_session()
                        time_to_end = datetime(2099, 12, 31)

                        grant_promotional_access_rc("standard_access", time_to_end, uid, rc_secret, session)
                        grant_promotional_access_rc("free_standard_lifetime", time_to_end, uid, rc_secret, session)

                    user_reference.update(
                        {"verificationGoingOn": False, "verified": True, "verificationCustomTitle": "Congratulations, you are verified",
                             "verificationCustomSubtitle": "Thank you for helping us keep Chyrpe safe & welcoming for all."})
                    
                    user_data.get("publicProfile").update({"verified": True})

                    track_and_notify_verification_successful(uid, True, user_data.get("language", "en"))

                case "Accept: Images Only":

                    logging.info(f"Updating user {action} {uid}")

                    user_reference.update(
                        {"imageVeriMadeChanges": True, "verificationCustomTitle": "You can now re-attempt verification",
                         "verificationCustomSubtitle": "Tap below to retry", "imageVeriHasFace": True,
                         "autoVerificationStarted": False,
                         "imageVeriHasExplicit": False, "verificationGoingOn": False})

                    # does not fit into strict successful/failed categories
                    send_ampli_event('Verification: Moderator Image Review Successful', uid, {'Manual': True})

                case _:

                    logging.info(f"Other action: {action}")

        except Exception as e:
            logging.exception(e)
            continue


    return https_fn.Response("Complete", 200)

@https_fn.on_request(region="europe-west1", concurrency=1)
def on_request_example(req: https_fn.Request) -> https_fn.Response:
    token = req.headers.get("X-Custom-Gateway-Token")
    if token != "Certified Gateway Auth invocation":
        return https_fn.Response("Unauthorized", status=403)

    return https_fn.Response("Hello world!!!?")

@https_fn.on_request(region="europe-west1", cors=options.CorsOptions(cors_origins="*", cors_methods=["get"]), memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def searchUsers(req: https_fn.Request) -> https_fn.Response:
    """
    Search users in Firestore by email/phone number

    Returns `uid`, `name`, `birthday`, `age`, `city`, `location`, `email`, `phone number`, `gender`
    """

    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()

    log_client = google.cloud.logging.Client()
    log_client.setup_logging()

    search_term = req.args.get('searchTerm')
    if search_term is None:
        return https_fn.Response("Empty Search Term", 400)
    match_type = req.args.get('matchType')
    if match_type is None:
        return https_fn.Response("No Match Type", 400)
    if match_type not in ['email', 'phone', 'uid']:
        return https_fn.Response("Invalid Match Type", 400)
    
    logging.info(f"Calling function searchUsers for search term '{search_term}'")
    
    try:
        db = firestore.client()
        users_collection = db.collection('users')
        if match_type == 'email':
            results = users_collection.where('email', '==', search_term).get()
        elif match_type == 'phone':
            results = users_collection.where('phone_number', '==', search_term).get()
        else:
            user_doc = users_collection.document(search_term).get()
            results = [user_doc] if user_doc.exists else []
        return https_fn.Response(json.dumps([search_user_return_fields(i) for i in results]), 200)
    except Exception as e:
        logging.exception(f'Error accessing Firestore in searchUsers: {e}')
        return https_fn.Response("Server Error", 500)

def calculate_age(birthday_iso: str) -> tuple[datetime, int]:
    """
    Calculate age from ISO date string

    :param birthday_iso: ISO date string
    :return: birthday as datetime, age
    """
    birthday = datetime.fromisoformat(birthday_iso)
    today = datetime.today()
    age = today.year - birthday.year - ((today.month, today.day) < (birthday.month, birthday.day))
    return birthday, age

@https_fn.on_request(region="europe-west2", cors=options.CorsOptions(cors_origins="*", cors_methods=["post"]), memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def updateUserAge(req: https_fn.Request) -> https_fn.Response:
    """
    Update a user's age in Firestore & Postgres
    """

    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()

    log_client = google.cloud.logging.Client()
    log_client.setup_logging()

    uid = req.form.get('uid')
    if uid is None:
        return https_fn.Response("No UID", 400)
    birthday_iso_str = req.form.get('birthday')
    if birthday_iso_str is None:
        return https_fn.Response("No birthday", 400)
    try:
        birthday, age = calculate_age(birthday_iso_str)
    except Exception as e:
        logging.exception(f'Error calculating age in updateUserAge: {e}')
        return https_fn.Response("Bad Request", 400)

    logging.info(f"Calling function updateUserAge for {uid} with new value {age} ({birthday})")

    connector = Connector(refresh_strategy="lazy")
    pool = connect_with_connector(connector)
    # make the write to Firestore & Postgres atomic; if either fails, rollback
    with pool.connect() as postgres_conn:
        try:
            # # update made to Postgres transaction but not yet committed
            stmt = sqlalchemy.text('UPDATE "public"."User" SET age = :age WHERE user_id = :uid;')
            postgres_conn.execute(stmt, {'age': age, 'uid': uid})

            # update made to Firestore
            firestore_conn = firestore.client()
            user_doc = firestore_conn.collection('users').document(uid)
            if not user_doc.get().exists:
                logging.warning(f"User {uid} not found in Firestore from updateUserAge")
                # bail out of the current context so that the transaction is rolled back
                raise AssertionError
            user_doc.update({'age': age, 'birthday': birthday})
            public_profile_doc = user_doc.get().to_dict().get('publicProfile')
            public_profile_doc.update({'age': age})
            
            # everything went well so commit the transaction
            postgres_conn.commit()
        except Exception as e:
            logging.exception(f'Error writing to databases in updateUserAge: {e}')
            postgres_conn.rollback()
            connector.close()
            pool.dispose()
            # if exception was AssertionError, we threw it because the user did not exist
            if isinstance(e, AssertionError):
                return https_fn.Response("User Not Found", 404)
            return https_fn.Response("Server Error", 500)

    connector.close()
    pool.dispose()
    return https_fn.Response("OK", 200)

def make_here_api_request(lat: float, long: float) -> dict|None:
    """
    Make a request to the HERE API to get the user's location

    :return: `dict` with `country_name, city_name, state_name` on success or `None` on failure
    """
    try:
        # TODO: no here key in dev env, needs to be added to test
        here_key = access_secret_version("Here_Key")
        url = f"https://revgeocode.search.hereapi.com/v1/revgeocode?at={lat},{long}&apiKey={here_key}"
        response = requests.get(url).json()
        country_name = response.get("items")[0].get("address").get("countryName")
        city_name = response.get("items")[0].get("address").get("city")
        state_name = response.get("items")[0].get("address").get("state")
        return {
            'country_name': country_name, 
            'city_name': city_name,
            'state_name': state_name
        }
    except Exception as e:
        logging.exception(f'Error making HERE API request: {e}')
        return None
    
def get_us_region(us_state: str) -> str:
    """
    Get the US region based on the user's state
    """
    state_to_region_dict = {
        'Connecticut': 'Northeast',
        'Maine': 'Northeast',
        'Massachusetts': 'Northeast',
        'New Hampshire': 'Northeast',
        'Rhode Island': 'Northeast',
        'Vermont': 'Northeast',
        'New Jersey': 'Northeast',
        'New York': 'Northeast',
        'Pennsylvania': 'Northeast',
        'Illinois': 'Midwest',
        'Indiana': 'Midwest',
        'Michigan': 'Midwest',
        'Ohio': 'Midwest',
        'Wisconsin': 'Midwest',
        'Iowa': 'Midwest',
        'Kansas': 'Midwest',
        'Minnesota': 'Midwest',
        'Missouri': 'Midwest',
        'Nebraska': 'Midwest',
        'North Dakota': 'Midwest',
        'South Dakota': 'Midwest',
        'Delaware': 'South',
        'Florida': 'South',
        'Georgia': 'South',
        'Maryland': 'South',
        'North Carolina': 'South',
        'South Carolina': 'South',
        'Virginia': 'South',
        'District of Columbia': 'South',
        'West Virginia': 'South',
        'Alabama': 'South',
        'Kentucky': 'South',
        'Mississippi': 'South',
        'Tennessee': 'South',
        'Arkansas': 'South',
        'Louisiana': 'South',
        'Oklahoma': 'South',
        'Texas': 'South',
        'Arizona': 'West',
        'Colorado': 'West',
        'Idaho': 'West',
        'Montana': 'West',
        'Nevada': 'West',
        'New Mexico': 'West',
        'Utah': 'West',
        'Wyoming': 'West',
        'Alaska': 'West',
        'California': 'West',
        'Hawaii': 'West',
        'Oregon': 'West',
        'Washington': 'West'
    }
    region = state_to_region_dict.get(us_state)
    return f'US-{region}' if region else 'Unknown'

@https_fn.on_request(region="europe-west2", cors=options.CorsOptions(cors_origins="*", cors_methods=["post"]), memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def updateUserLocation(req: https_fn.Request) -> https_fn.Response:
    """
    Update a user's location in Firestore
    """

    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()

    log_client = google.cloud.logging.Client()
    log_client.setup_logging()

    req_data = req.get_json()
    uid = req_data.get('uid')
    if uid is None:
        return https_fn.Response("No UID", 400)
    latitude = req_data.get('latitude')
    longitude = req_data.get('longitude')
    if latitude is None or longitude is None:
        return https_fn.Response("No location data", 400)
    try:
        location = GeoPoint(latitude, longitude)
    except Exception as e:
        logging.exception(f'Error creating GeoPoint in updateUserLocation: {e}')
        return https_fn.Response("Bad Request", 400)
    # optionally update city if the param was passed
    city_param = req_data.get('city')
    city = city_param if city_param not in ['', None] else None
    
    logging.info(f"Calling function updateUserLocation for {uid} with new value (lat={latitude}, long={longitude}, city={city or 'No change'})")

    try:
        db = firestore.client()
        user_doc = db.collection('users').document(uid)
        if not user_doc.get().exists:
            logging.warning(f"User {uid} not found in Firestore from updateUserLocation")
            return https_fn.Response("User not found", 404)
        update_params = {'location': location, 'matchingSuggestions': []}
        if city:
            update_params['city'] = city
        
        # attempt to update region & local matching
        res_data = make_here_api_request(latitude, longitude)
        if res_data is not None:
            if res_data['country_name'] == 'United States':
                region = get_us_region(res_data['state_name'])
            else:
                region = res_data['country_name']
            update_params['wlRegion'] = region
            open_regions_collection = db.collection('openRegions')
            open_region_query = open_regions_collection.where('name', '==', region).get()
            if len(open_region_query) > 0:
                # enable local matching
                update_params['localMatching'] = True
                update_params['localWlAdmitted'] = True
            
        user_doc.update(update_params)
    except Exception as e:
        logging.exception(f'Error accessing Firestore in updateUserLocation: {e}')
        return https_fn.Response("Server Error", 500)
    
    return https_fn.Response("OK", 200)

@https_fn.on_request(region="europe-west2", cors=options.CorsOptions(cors_origins="*", cors_methods=["post"]), memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def updateUserProfessional(req: https_fn.Request) -> https_fn.Response:

    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()

    log_client = google.cloud.logging.Client()
    log_client.setup_logging()

    uid = req.form.get('uid')

    db = firestore.client()

    try:
        user_doc_ref = db.collection('users').document(uid)
        user_doc = user_doc_ref.get()
        public_profile_ref = user_doc.get('publicProfile')
        user_doc_ref.update({'findom': True})
        public_profile_ref.update({'findom': True})

        connector = Connector(refresh_strategy="lazy")
        pool = connect_with_connector(connector)
        try:
            with pool.connect() as db_conn:
                # TODO: create table & `insert_professional_uid`
                stmt = sqlalchemy.text('SELECT insert_professional_uid(:user_id)')
                db_conn.execute(stmt, {'user_id': uid})
        except Exception as e:
            logging.exception(f'Error accessing Postgres in updateUserProfessional: {e}')
            connector.close()
            pool.dispose()
            return https_fn.Response("Server Error", 500)
        connector.close()
        pool.dispose()
    except Exception as e:
        logging.exception(f'Error accessing Firestore in updateUserProfessional: {e}')
        connector.close()
        pool.dispose()
        return https_fn.Response("Server Error", 500)
    return https_fn.Response("OK", 200)

@https_fn.on_request(region="europe-west2", cors=options.CorsOptions(cors_origins="*", cors_methods=["post"]), memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def updateUserGender(req: https_fn.Request) -> https_fn.Response:

    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()

    log_client = google.cloud.logging.Client()
    log_client.setup_logging()

    uid = req.form.get('uid')
    if uid is None:
        return https_fn.Response("No UID", 400)
    real_gender = req.form.get('realGender')
    if real_gender is None:
        return https_fn.Response("No Gender", 400)
    
    logging.info(f"Calling function updateUserGender for {uid} with new value {real_gender}")

    db = firestore.client()

    send_ampli_event('Profile: Update Gender', uid, {'Moderator': True})

    try:
        user_doc_ref = db.collection('users').document(uid)
        user_data = user_doc_ref.get().to_dict()
        # check if user in open region
        open_regions_collection = db.collection('openRegions')
        user_region = user_data.get('wlRegion')
        user_region_query = open_regions_collection.where('name', '==', user_region).get()
        # set user as locally admitted if in open region, else set as globally admitted
        region_type = 'localWlAdmitted' if len(user_region_query) > 0 else 'globalWlAdmitted'
        user_doc_ref.update({'gender': real_gender, region_type: True, "globalMatching": True if region_type == "globalWlAdmitted" else False, "localMatching": True if region_type == "localWlAdmitted" else False})
    except Exception as e:
        logging.exception(f'Error accessing Firestore in updateUserGender: {e}')
        return https_fn.Response("Server Error", 500)
    return https_fn.Response("OK", 200)

@https_fn.on_request(region="europe-west2", cors=options.CorsOptions(cors_origins="*", cors_methods=["post"]), memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def updateUserNonBinary(req: https_fn.Request) -> https_fn.Response:

    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()

    log_client = google.cloud.logging.Client()
    log_client.setup_logging()

    uid = req.form.get('uid')
    if uid is None:
        return https_fn.Response("No UID", 400)
    
    logging.info(f"Calling function updateUserNonBinary for {uid}")

    db = firestore.client()

    send_ampli_event('Profile: Add Non-Binary and Request Gender Confirm', uid, {'Moderator': True})

    try:
        user_doc_ref = db.collection('users').document(uid)
        user_doc_ref.update({'genderConfirm': True, 'autoVerificationStarted': False, 'manualVerification': True})
    except Exception as e:
        logging.exception(f'Error accessing Firestore in updateUserNonBinary: {e}')
        return https_fn.Response("Server Error", 500)
    return https_fn.Response("OK", 200)

@https_fn.on_request(region="europe-west2", cors=options.CorsOptions(cors_origins="*", cors_methods=["post"]), memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def updateUserName(req: https_fn.Request) -> https_fn.Response:

    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()

    log_client = google.cloud.logging.Client()
    log_client.setup_logging()

    uid = req.form.get('uid')
    if uid is None:
        return https_fn.Response("No UID", 400)
    name = req.form.get('name')
    if name is None:
        return https_fn.Response("No Name", 400)
    
    logging.info(f"Calling function updateUserName for {uid} with new value {name}")

    db = firestore.client()

    try:
        user_doc_ref = db.collection('users').document(uid)
        user_doc = user_doc_ref.get()
        public_profile_ref = user_doc.get('publicProfile')
        user_doc_ref.update({'name': name, 'publicName': name})
        public_profile_ref.update({'publicName': name})
    except Exception as e:
        logging.exception(f'Error accessing Firestore in updateUserName: {e}')
        return https_fn.Response("Server Error", 500)
    return https_fn.Response("OK", 200)

@with_retry()
def reset_own_dislikes_sql(uid):
    with Connector(refresh_strategy="lazy") as connector:
        pool = connect_with_connector(connector)
        try:
            with pool.connect() as db_conn:
                logging.info(f"Attempting to dislike in SQL: calling user: {uid}")
                stmt = sqlalchemy.text(
                    """SELECT delete_own_rejections(:user_id)""")

                result = db_conn.execute(stmt, {
                    'user_id': uid,
                }).fetchall()

                db_conn.commit()
                logging.info(f"Committed deletion of own rejections in SQL: calling user: {uid}")
        finally:
            # dispose pool
            pool.dispose()

@https_fn.on_request(region="europe-west2", cors=options.CorsOptions(cors_origins="*", cors_methods=["post"]), memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def resetOwnDislikesWrapper(req: https_fn.Request) -> https_fn.Response:

    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()

    import time
    uid = req.form.get('uid')
    if uid is None:
        return https_fn.Response("No UID", 400)
    db = firestore.client()

    users_collection = db.collection("users")
    user_ref = users_collection.document(uid)

    # call SQL function to delete all but matches there
    try:
        try:
            reset_own_dislikes_sql(uid)
        except Exception as e:
            logging.exception(e)
            db.collection('SQLExceptions').add(
                {'function': 'resetOwnDislikes', 'user': uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})
    except:
        logging.exception(f"Fail Reset Own Dislikes: User: {uid}")
    # delete all passed profiles in Firestore
    user_ref.update({"passedProfiles": firestore.DELETE_FIELD, "matchingSuggestions": firestore.DELETE_FIELD, "sawEverybody": firestore.DELETE_FIELD})
    time.sleep(1)

    return https_fn.Response("OK", 200)

@https_fn.on_request(region="europe-west2", cors=options.CorsOptions(cors_origins="*", cors_methods=["delete"]), memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def deleteUserImage(req: https_fn.Request) -> https_fn.Response:

    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()

    log_client = google.cloud.logging.Client()
    log_client.setup_logging()

    uid = req.form.get('uid')
    if uid is None:
        return https_fn.Response("No UID", 400)
    image_url = req.form.get('image_url')
    if image_url is None:
        return https_fn.Response("No Image URL", 400)
    
    logging.info(f"Calling function deleteUserImage for user {uid} with image URL {image_url}")
    
    db = firestore.client()

    try:
        bucket_name, image_uri = convert_firebase_url_to_gs(image_url)

        # remove gs:// and bucket prefix from URI
        image_uri = image_uri.split('.com/')[1]
        storage_client = storage.Client()
        bucket = storage_client.get_bucket(bucket_name)
        image_blob = bucket.get_blob(image_uri)
        if image_blob is None:
            logging.warning(f"Image {image_uri} not found in Cloud Storage bucket {bucket_name}")
            return https_fn.Response(f"Image {image_uri} not found", 404)
        
        # get the reference to the image in publicProfile
        user_doc_ref = db.collection('users').document(uid)
        user_doc = user_doc_ref.get()
        if not user_doc.exists:
            return https_fn.Response(f"User {uid} not found", 404)
        public_profile_ref = user_doc.get('publicProfile')
        user_npictures = public_profile_ref.get().to_dict().get('nPictures')

        # filter out the image with the given URL
        filtered_npictures = [i for i in user_npictures if i.get().get('url') != image_url]
        if len(filtered_npictures) == len(user_npictures):
            return https_fn.Response(f"Image {image_url} not found in user {uid}'s public profile", 404)
        # get ref to image in user collection
        image_ref: DocumentReference = [i for i in user_npictures if i.get().get('url') == image_url][0]

        # remove references and delete image
        image_ref.delete()
        public_profile_ref.update({'nPictures': filtered_npictures})
        image_blob.delete()
    except Exception as e:
        logging.exception(f'Error deleting image from Cloud Storage in deleteUserImage: {e}')
        return https_fn.Response("Server Error", 500)
    return https_fn.Response("OK", 200)

@https_fn.on_request(region="europe-west2", cors=options.CorsOptions(cors_origins="*", cors_methods=["post"]), memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def secureUserProfileData(req: https_fn.Request) -> https_fn.Response:

    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()

    log_client = google.cloud.logging.Client()
    log_client.setup_logging()

    uid = req.form.get('uid')
    if uid is None:
        return https_fn.Response("No UID", 400)

    logging.info(f"Calling function secureUserProfileData for {uid}")

    db = firestore.client()
    try:
        storage_client = storage.Client()
        bucket_name = 'greenrocks-dev-s1' if is_dev_environment() else 'greenrocks-s1'
        bucket = storage_client.bucket(bucket_name)
        timestamp = datetime.now().isoformat()
        folder_path = f'profileOnly/{uid}_{timestamp}'

        users_collection = db.collection("users")
        user_doc = users_collection.document(uid).get()
        logging.info(f"Uploading user document to {folder_path}")
        json_user_doc = make_serializable(user_doc.to_dict())
        upload_json_from_string(bucket, f"{folder_path}/user_doc.json", json_user_doc)
        logging.info(f"Uploading public profile document to {folder_path}")
        public_profile = user_doc.get("publicProfile").get()
        public_profile_doc_json = make_serializable(public_profile.to_dict())
        upload_json_from_string(
            bucket,
            f"{folder_path}/publicProfile_{public_profile.id}.json",
            public_profile_doc_json
        )
        logging.info(f"Uploading images to {folder_path}/images")
        images_docs = user_doc.reference.collection("images").get()
        for i in images_docs:
            upload_json_from_string(
                bucket,
                f"{folder_path}/images/{i.id}.json", 
                make_serializable(i.to_dict())
            )

        # actually copying over images

        source_bucket_name = "greenrocks-dev.appspot.com" if is_dev_environment() else "greenrocks-9abc3.appspot.com"
        destination_bucket_name = bucket_name

        # Source prefix and destination prefix
        source_prefix = f"users/{uid}/"
        destination_prefix = f"{folder_path}/images/"

        # Get the buckets
        source_bucket = storage_client.bucket(source_bucket_name)
        destination_bucket = storage_client.bucket(destination_bucket_name)

        # List and copy objects
        blobs = source_bucket.list_blobs(prefix=source_prefix)
        for blob in blobs:
            destination_blob_name = destination_prefix + blob.name[len(source_prefix):]
            source_bucket.copy_blob(blob, destination_bucket, destination_blob_name)
            logging.info(f"Copied {blob.name} to {destination_blob_name}")
    except Exception as e:
        logging.exception(f'Error accessing Firestore in secureUserProfileData: {e}')
        return https_fn.Response("Server Error", 500)
    return https_fn.Response("OK", 200)


def get_user_profile_terms(user: DocumentSnapshot) -> dict:
    """
    Get user profile terms used in Retool frontend
    """
    user_dict = user.to_dict()
    public_profile = user_dict.get('publicProfile').get().to_dict()
    try:
        # prompts formatted as array of [prompt, answer] in Retool
        prompts = [[p.get('prompt'), p.get('answer')] for p in public_profile.get('prompts')]
    except TypeError:
        prompts = []
        logging.warning("User has no prompts; using empty array")
    user_npictures = public_profile.get('nPictures', [])
    # get document ID and URL for each user picture
    npictures_data = [
        {'imageId': i.id, 'url': i.get().to_dict().get('url')} for i in user_npictures
    ]
    return {
        'name': user_dict.get('name'),
        'uid': user.id,
        'bio': public_profile.get('bio'),
        'prompts': prompts,
        'images': npictures_data
    }


@https_fn.on_request(region="europe-west2", cors=options.CorsOptions(cors_origins="*", cors_methods=["get"]),
                     memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def getUserProfileForMpe(req: https_fn.Request) -> https_fn.Response:

    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()

    log_client = google.cloud.logging.Client()
    log_client.setup_logging()

    uid = req.args.get('uid')
    if uid is None:
        return https_fn.Response("No UID", 400)

    logging.info(f"Calling function getUserProfileForMpe for {uid}")

    db = firestore.client()

    try:
        user_doc = db.collection('users').document(uid).get()
        if not user_doc.exists:
            return https_fn.Response("User not found", 404)
        return https_fn.Response(json.dumps(get_user_profile_terms(user_doc)), 200)
    except Exception as e:
        logging.exception(f'Error accessing Firestore in getUserProfileForMpe: {e}')
        return https_fn.Response("Server Error", 500)


@https_fn.on_request(region="europe-west2", cors=options.CorsOptions(cors_origins="*", cors_methods=["post"]),
                     memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def moderatorRejectProfileWrapper(req: https_fn.Request) -> https_fn.Response:

    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()


    log_client = google.cloud.logging.Client()
    log_client.setup_logging()

    req_data = req.get_json()
    uid = req_data.get('uid')
    if uid is None:
        return https_fn.Response("No UID", 400)

    logging.info(f"Calling function moderatorRejectProfileWrapper for {uid}")

    flagged_data = req_data.get('flaggedData')
    marked_attribute_ids = flagged_data.get('markedAttributeIds')
    marked_image_ids = flagged_data.get('markedImageIds')
    marked_prompt_ids = flagged_data.get('markedPromptIds')
    no_face = flagged_data.get('noFace')

    db = firestore.client()
    try:
        title, description = generateMpeTitleAndBody(
            marked_prompt_ids,
            marked_attribute_ids,
            marked_image_ids,
            no_face
        )
        logging.info(f"Generated MPE title and body: {title}, {description}")
        new_value = {
            "uid": uid,
            "markedImageIds": marked_image_ids,
            "markedPromptIds": marked_prompt_ids,
            "markedAttributeIds": marked_attribute_ids,
            "noFace": no_face,
            "displayTitle": title,
            "displayBody": description,
            "displayBtn": "Edit Profile",
            "timestamp": firestore.firestore.SERVER_TIMESTAMP
        }
        db.collection("moderatorRejectProfiles").add(new_value)
    except Exception as e:
        logging.exception(f'Error generating MPE title and body: {e}')
        return https_fn.Response("Server Error", 500)
    return https_fn.Response("OK", 200)


@https_fn.on_request(region="europe-west2", cors=options.CorsOptions(cors_origins="*", cors_methods=["post"]),
                     memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def moderatorApproveProfileWrapper(req: https_fn.Request) -> https_fn.Response:

    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()

    log_client = google.cloud.logging.Client()
    log_client.setup_logging()

    uid = req.form.get('uid')
    if uid is None:
        return https_fn.Response("No UID", 400)

    logging.info(f"Calling function moderatorApproveProfileWrapper for {uid}")

    db = firestore.client()
    try:
        db.collection("moderatorApproveProfiles").add({
            "uid": uid,
            "timestamp": firestore.firestore.SERVER_TIMESTAMP
        })
    except Exception as e:
        logging.exception(f'Error accessing Firestore in moderatorApproveProfileWrapper: {e}')
        return https_fn.Response("Server Error", 500)
    return https_fn.Response("OK", 200)

@https_fn.on_request(region="europe-west2", cors=options.CorsOptions(cors_origins="*", cors_methods=["post"]),
                     memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def moderatorRerequestVerification(req: https_fn.Request) -> https_fn.Response:

    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()

    log_client = google.cloud.logging.Client()
    log_client.setup_logging()

    uid = req.form.get('uid')
    if uid is None:
        return https_fn.Response("No UID", 400)

    logging.info(f"Calling function moderatorApproveProfileWrapper for {uid}")

    db = firestore.client()
    try:

        logging.info(f"Updating user rerequest verification {uid}")

        request_retry_manual(uid, None, None)

        db.collection('users').document(uid).update({"verificationGoingOn": False, "autoVerificationStarted": False,
                               "timesRejected": 555, "verified": False, "paused": True, "signUpFinished": False,
                               "verificationFailed": True, "manualVerification": True})

        track_and_notify_verification_failed(uid, True, 'General/Technical')

        @with_retry()
        def update_paused_with_retry():
            connector = Connector(refresh_strategy="lazy")
            pool = connect_with_connector(connector)

            try:

                with pool.connect() as db_conn:

                    stmt = sqlalchemy.text(
                        """SELECT update_user(p_user_id => :user_id, p_match_status => :match_status)""")

                    # Execute with the parameters
                    result = db_conn.execute(stmt, {
                        'user_id': uid,
                        'match_status': 'Neither'}).fetchall()

                    db_conn.commit()

            finally:
                pool.dispose()
                connector.close()

        update_paused_with_retry()

    except Exception as e:
        logging.exception(f'Error updating user rerequest verification: {e}')
        return https_fn.Response("Server Error", 500)
    return https_fn.Response("OK", 200)



from typing import Optional
def get_findom_interest_status(kinks_list: list[str], professionals_req: bool) -> Optional[bool]:
    """
    Based on a user's kink list and findom filter status, returns whether to classify them as a findom_interest.

    Args:
        kinks_list (list): A list of kinks associated with the user.
        findom_filter (bool): Indicates if the findom filter is enabled.

    Returns:
        Optional[bool]: True if the user is a findom_interest, False if explicitly opted out of findom interest, or None if undetermined.
    """

    if professionals_req:
        return False
    elif 'Findom' in kinks_list:
        return True
    else:
        return None

@https_fn.on_request(region="europe-west1", cors=options.CorsOptions(cors_origins="*", cors_methods=["get"]),
                     memory=options.MemoryOption.GB_1, timeout_sec=900, cpu=1)
def getAttributions(req: https_fn.Request) -> https_fn.Response:
    """
    This function returns the attribution count for creators within a specified time period.

    Args:
    - `start_date`: The earliest date included in the attribution report, formatted as YYYYMMDD and set to midnight UTC. Defaults to 30 days ago.
    - `end_date`: The earliest date not included in the attribution report, formatted as YYYYMMDD and set to midnight UTC. Defaults to the current date.

    Returns:
    A JSON dictionary containing the creator’s name as the key and the aggregate attributions for the specified time period as the value.
    """

    # initializing log client
    log_client = google.cloud.logging.Client()
    log_client.setup_logging()

    # determine if rate limit reached
    if isRateLimited():
        return https_fn.Response("Quota exceeded - contact admin for increase", 429)
    else:
        incrementRateLimitDay()

    # Authenticate Request using Bearer token from header
    auth_header = req.headers.get("Authorization")

    if not auth_header or not auth_header.startswith("Bearer "):
        return https_fn.Response(
            "Missing or invalid Authorization header", status=401
        )

    token = auth_header.split(" ")[1]  # Extract token after "Bearer "
    if token != access_secret_version("Attributions_Token"):
        return https_fn.Response(
            "Invalid token", status=403
        )

    today_string = datetime.now().strftime('%Y%m%d')
    past_string = (datetime.now() - timedelta(30)).strftime('%Y%m%d')

    start_date_string = req.args.get('start_date', past_string) # 20250501
    end_date_string = req.args.get('end_date', today_string) # 20250531

    start_date = dateutil.parser.parse(start_date_string)
    end_date = dateutil.parser.parse(end_date_string)

    db = firestore.client()

    referral_collection = db.collection("referralLog")

    r_query = referral_collection.where(filter=FieldFilter("eligible_referral_date", ">", start_date)).where(filter=FieldFilter("eligible_referral_date", "<", end_date)).get()

    total_dict = {}

    for r in r_query:

        r_data = r.to_dict()

        r_creator = r_data.get("eligible_referral_creator")

        if r_creator in total_dict:
            total_dict[r_creator] += 1

        else:
            total_dict[r_creator] = 1

    return https_fn.Response(json.dumps(total_dict), 200)
