"""
Functions and data types reused by multiple files in the repo
"""
from datetime import datetime
from google.auth import default
from google.cloud import secretmanager
from google.cloud.firestore_v1 import FieldFilter, Or
from google.cloud.sql.connector import Connector
from google.cloud.storage import Bucket
from firebase_admin import firestore
from firebase_functions.firestore_fn import DocumentSnapshot
import json
import pg8000
import requests
import logging
from requests.adapters import HTTPAdapter, Retry
import sqlalchemy
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from urllib.parse import urlparse, unquote

def access_secret_version(secret_name):
    '''Access a secret from the project's secret manager'''
    # use google.auth to get the project id
    _, project_id = default()
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
    response = client.access_secret_version(name=name)
    return response.payload.data.decode('UTF-8')

def is_dev_environment():
    """
    Check if running in dev environment using default credentials
    """
    _, project_id = default()
    return project_id == "greenrocks-dev"

def connect_with_connector(connector: Connector) -> sqlalchemy.engine.base.Engine:

    """
    Initializes a connection pool for a Cloud SQL instance of Postgres.

    Uses the Cloud SQL Python Connector package.
    """
    def getconn() -> pg8000.dbapi.Connection:
        conn: pg8000.dbapi.Connection = connector.connect(
            "greenrocks-9abc3:europe-west2:chyrpe-prod1",
            "pg8000",
            user="<EMAIL>",
            db="chyrpe_main_db",
            enable_iam_auth=True,
        )
        return conn

    # The Cloud SQL Python Connector can be used with SQLAlchemy
    # using the 'creator' argument to 'create_engine'
    pool = sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        pool_size=2,
        max_overflow=2,
        # ...
    )
    return pool

def find_all_likes_of_two_users(user1_id: str, user2_id: str, likes_collection) -> list[DocumentSnapshot]:
    '''Returns all likes documents that include 2 given users by id'''
    filter_1 = FieldFilter("involedUIDs", "==", [user1_id, user2_id])
    filter_2 = FieldFilter("involedUIDs", "==", [user2_id, user1_id])

    # Create the union filter of the two filters (queries)
    or_filter = Or(filters=[filter_1, filter_2])

    # Execute the query
    docs = likes_collection.where(filter=or_filter).get()

    return docs

def create_retry_session() -> requests.Session:
    session = requests.Session()
    retries = Retry(total=5, backoff_factor=0.1, status_forcelist=[429, 500, 502, 503, 504])
    session.mount('https://', HTTPAdapter(max_retries=retries))
    return session

def grant_promotional_access_rc(entitlement_name: str, time_to_end: datetime, user_id: str, secret: str, s: requests.Session) -> bool:
    timestamp_end = int(time_to_end.timestamp() * 1000)

    url = f"https://api.revenuecat.com/v1/subscribers/{user_id}/entitlements/{entitlement_name}/promotional"

    payload = json.dumps({
        "end_time_ms": timestamp_end
    })
    headers = {
        'Authorization': f'Bearer {secret}',
        'Content-Type': 'application/json'
    }

    response = s.post(url, headers=headers, data=payload)

    if response.status_code in [200, 201]:
        return True
    else:
        return False

def sendPushNotification(uid, title, text):

    db = firestore.client()

    db.collection("ff_user_push_notifications").add({"notification_title": title, "notification_text": text,
                                       "user_refs": f'/users/{uid}',
                                       "timestamp": firestore.firestore.SERVER_TIMESTAMP})

def get_cdn_url(firestore_url: str) -> str:
    """
    Take a Firestore URL and convert it into the URL for the
    CDN where we are storing user images
    """
    cdn_url = 'https://dev-cdn.chyrpe.com'
    parsed_firestore_url = urlparse(firestore_url)
    path_list = parsed_firestore_url.path.split("/")[-1].split("%2F")
    if len(path_list) != 4:
        logging.exception(f"Problem parsing firestore URL {firestore_url}")
        return
    cdn_path = '/'.join(path_list)
    return f'{cdn_url}/{cdn_path}'

def get_timestamp():
    timestamp_format = "%Y-%m-%d %H:%M:%S"
    current_datetime = datetime.now()
    timestamp_string = current_datetime.strftime(timestamp_format)
    return timestamp_string

def parse_timestamp(timestamp: str):
    timestamp_format = "%Y-%m-%d %H:%M:%S"
    return datetime.strptime(timestamp, timestamp_format)

def get_server_config() -> dict:
    """
    The `ServerConfig` collection contains the "main" document, which stores
    values that can be edited externally to change the behaviour of functions
    """
    db = firestore.client()
    server_config_ref = db.collection("ServerConfig").document("main")
    return server_config_ref.get().to_dict()

def convert_firebase_url_to_gs(firebase_url):
    """Converts the Firebase Token URL of an image to its Bucket location URL"""
    # Step 1: Parse the URL
    parsed_url = urlparse(firebase_url)

    # Step 2: Extract the bucket name from the path
    bucket_name = parsed_url.path.split('b/')[1].split('/')[0]  # Extract bucket name after 'b/'

    # Step 3: Extract the encoded file path and decode it
    encoded_file_path = parsed_url.path.split('/o/')[1]
    decoded_file_path = unquote(encoded_file_path)

    # Step 4: Construct the gs:// URL
    gs_url = f"gs://{bucket_name}/{decoded_file_path}"
    return bucket_name, gs_url

def make_serializable(input_data):
        """
        Recursively processes a dictionary or list:
        - Extracts the `.id` from Firestore DocumentReference objects.
        - Converts all non-serializable values to strings for full JSON compatibility.
        """
        if isinstance(input_data, dict):
            return {key: make_serializable(value) for key, value in input_data.items()}
        elif isinstance(input_data, set):
            return [make_serializable(element) for element in list(input_data)]
        elif isinstance(input_data, list):
            return [make_serializable(element) for element in input_data]
        elif isinstance(input_data, firestore.DocumentReference):
            # Extract the ID from the Firestore reference object
            return input_data.id
        elif isinstance(input_data, firestore.GeoPoint):
            return [input_data.latitude, input_data.longitude]
        else:
            try:
                try:
                    # Test if the input is JSON serializable
                    json.dumps(input_data)
                    return input_data
                except (TypeError, ValueError):
                    # Convert non-serializable values to strings
                    return str(input_data)
            except:
                return "Not serialisable"

def upload_json_from_string(bucket: Bucket, filepath, string):
    blob = bucket.blob(filepath)

    # Upload the JSON string
    blob.upload_from_string(json.dumps(string), content_type='application/json')

def log_retry_attempt(retry_state):
    """Logs retry attempts before each retry."""
    logging.warning(f"Retrying... Attempt {retry_state.attempt_number}")

def with_retry(max_attempts=3, min_wait=2, max_wait=15, exception_types=(Exception)):
    """Decorator to add retry logic with exponential backoff."""
    return retry(
        retry=retry_if_exception_type(exception_types),
        wait=wait_exponential(multiplier=1, min=min_wait, max=max_wait),  # Initial wait 2s, doubles each time, max 10s
        stop=stop_after_attempt(max_attempts),  # Max 5 retry attempts
        before=log_retry_attempt,
        reraise=True
    )


def generateMpeTitleAndBody(kinky_prompts, kinky_attributes, kinky_images, no_face):
    explicit_items = {
        "1st prompt": "prompts0" in kinky_prompts,
        "2nd prompt": "prompts1" in kinky_prompts,
        "3rd prompt": "prompts2" in kinky_prompts,
        "bio": "bio" in kinky_attributes,
        "images": bool(kinky_images),
    }

    explicit_entries = [key for key, value in explicit_items.items() if value]

    if len(explicit_entries) == 0 and no_face:
        title = "Add a clear face picture to continue matching"
        description = "Every Chyrpe profile must include a clear photo of your face"
        return title, description

    elif len(explicit_entries) == 0:
        title = "Edit your profile to continue matching"
        description = f"""It features content not allowed outside the kink area, like sexualising or kinky themes.
             Kink is welcome in the new profile kink area or when a woman allows it in your chat."""
        return title, description

    else:
        if len(explicit_entries) > 1:
            title = f"Edit your {', '.join(explicit_entries[:-1])} and {explicit_entries[-1]} to continue matching"
        else:
            title = f"Edit your {explicit_entries[0]} to continue matching"
        description = f"""Your profile features content not allowed in that place, like sexualising or kinky themes.
             Kink is welcome in the new profile kink area or when a woman allows it in your chat.\n\n
             {"Please also ensure that you add at least one face picture to your profile." if no_face else ""}"""

    return title, description


def has_face_explicitness(images_moderation):

    explicit_images = []
    face_images = []

    # loop over all images provided and return if any explicit or face ones among them
    for i in images_moderation:

        image_dict = images_moderation[i]

        face = image_dict.get("face")
        explicit = image_dict.get("explicit")

        if face:
            face_images.append(i)

        if explicit:
            explicit_images.append(i)

    return face_images, explicit_images

def get_current_compliance_images(uid):
    db = firestore.client()

    meta_image_reference = db.collection("users").document(uid).collection("meta").document("imageCompliance")

    meta_image_doc = meta_image_reference.get().to_dict()

    current_images = meta_image_doc.get("currentImages")

    return current_images

def ordinal(n):
    """Convert an integer into its ordinal representation: 1 -> 1st, 2 -> 2nd, etc."""
    return f"{n}{'tsnrhtdd'[(n//10%10!=1)*(n%10<4)*n%10::4]}"

def generate_image_reject_title_body(all_image_ids, violating_image_ids, face):
    explicit_items = {}

    if violating_image_ids:
        indices = [all_image_ids.index(img_id) + 1 for img_id in violating_image_ids if img_id in all_image_ids]
        ordinals = [ordinal(i) for i in indices]
        explicit_items["image"] = ordinals

    explicit_entries = list(explicit_items.keys())

    if not explicit_entries and not face:
        title = "Add a clear face picture to get verified"
        description = "Every Chyrpe profile must include a clear photo of your face"
        return title, description

    elif not explicit_entries:
        title = "Edit your profile to get verified"
        description = """It features content not allowed outside the kink area, like sexualising or kinky themes.
            Kink is welcome in the new profile kink area or when a woman allows it in your chat."""
        return title, description

    else:
        image_ordinals = explicit_items["image"]
        if len(image_ordinals) > 1:
            ordinal_str = ", ".join(image_ordinals[:-1]) + " and " + image_ordinals[-1]
        else:
            ordinal_str = image_ordinals[0]
        title = f"Edit your {ordinal_str} image to get verified"

        description = "These images feature sexualising or kinky themes. This is not allowed on Chyrpe."
        if not face:
            description += "\n\nPlease also add at least one image of your face. "

        return title, description

def disapprove_image_verifiation(uid, n_pictures, explicit_images, face_images, title_override=None, description_override=None):
    """Handles updating user state to disapprove verification images, whether moderator (can override title & description) or not"""

    db = firestore.client()

    users_reference = db.collection("users").document(uid)

    title, description = generate_image_reject_title_body(n_pictures, explicit_images, bool(face_images))

    users_reference.update({
        "imageVeriHasFace": True if face_images else False,
        "imageVeriMadeChanges": False,
        "imageVeriHasExplicit": True if explicit_images else False,
        "imageVerified": False,
        "imageVeriFailed": True,
        "imageVeriImprovementImages": explicit_images,
        "verificationCustomTitle": title_override if title_override else title,
        "verificationCustomSubtitle": description_override if description_override else description
    })

def approve_image_verification(uid):
    """Handles updating user state to approve verification images, whether moderator or not"""

    db = firestore.client()

    users_reference = db.collection("users").document(uid)

    users_reference.update({
        "imageVeriHasFace": True,
        "imageVeriHasExplicit": False,
        "imageVerified": True,
        "imageVeriFailed": False,
        "imageVeriImprovementImages": [],
        "verificationCustomTitle": "You can now re-attempt verification",
        "verificationCustomSubtitle": "Tap below to retry"
    })

def check_image_compliance_core(uid):
    db = firestore.client()

    # get public profile and full current nPictures array
    public_profile = db.collection('users').document(uid).collection("publicProfile").limit(1).get()[0].to_dict()
    n_pictures_raw = public_profile.get("nPictures")
    n_pictures = [n.id for n in n_pictures_raw] # gets the ids as needed for comp functions

    # show loading screen in between
    compliance_images = get_current_compliance_images(uid)

    # get face and explicitness

    face_images, explicit_images = has_face_explicitness(compliance_images)

    return (face_images and not explicit_images)



