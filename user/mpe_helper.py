# Imports in alphabetical order

from amplitude import Amplitude, BaseEvent, Identify, EventOptions
from datetime import datetime, timedelta
from firebase_admin import initialize_app
from firebase_functions import https_fn, identity_fn, options, scheduler_fn
from firebase_functions.firestore_fn import (
  on_document_created,
  on_document_deleted,
  on_document_updated,
  on_document_written,
  Event,
  Change,
  DocumentSnapshot,
)
from google.cloud import secretmanager, firestore
from google.cloud.firestore import DocumentReference
from firebase_admin import firestore
from firebase_admin import db as rtdb
from google.cloud.firestore_v1 import FieldFilter, Or, And
import google.cloud.logging
from google.cloud.sql.connector import Connector
import logging
import pg8000
import random
import requests
from requests.adapters import HTTPAdapter, Retry
import sqlalchemy
from typing import Any
import nmatching as nm
import re
from google.cloud import vision
import urllib.parse
from google.cloud import storage
from PIL import Image

# initializing log client
log_client = google.cloud.logging.Client()
log_client.setup_logging()

# defining common methods in global scope
amplitude_client = Amplitude("65dbac83dec4e06df231ca34a83e4757")

def access_secret_version(secret_name):
    '''Access a secret from the project's secret manager'''
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/greenrocks-9abc3/secrets/{secret_name}/versions/latest"
    response = client.access_secret_version(name=name)
    return response.payload.data.decode('UTF-8')

def moderate_content_leaner(scannable_content):
    valid = True

    from pydantic import BaseModel
    from openai import AzureOpenAI, BadRequestError

    client = AzureOpenAI(
        azure_endpoint="https://chyrpegptp.openai.azure.com/",
        api_key=access_secret_version("OpenAI_Key"),
        api_version="2024-10-21"
    )

    class LanguageEvaluation(BaseModel):
        input_text: str
        evaluation_result: int

    try:
        # Call the GPT model with the schema
        completion = client.beta.chat.completions.parse(
            model="gpt-4o-mini",  # replace with the actual model deployment name
            messages=[
                {"role": "system", "content": """You are a content classification assistant. Your task is to classify a given piece of text (a user’s bio) into one of three categories based on explicitness. You must strictly follow these rules:

- Output must be exactly one digit: 0, 1, or 2.
- Do not include any additional text or explanation beyond the digit.
- Use the following guidelines to determine the correct digit:
    0 = Not explicit (safe, no questionable content).
    1 = Explicit (clearly sexual, violent, or otherwise disallowed content).
    2 = Borderline or unclear (hints of explicitness, ambiguity, or if you are unsure).

If the bio is obviously explicit, output 1.
If it is obviously not explicit, output 0.
If it is ambiguous or borderline, output 2.

Be especially strict about finding kinky / BDSM / femdom content. Terms like 'mommy', 'dominant woman', 'submissive' if not used in a clearly harmless, non-kinky context, should be flagged as explicit.

Classify the following bio into the categories above and output only the appropriate digit:"""},
                {"role": "user", "content": scannable_content}
            ],
            response_format=LanguageEvaluation,  # Using the schema here
        )

        # Parse and print the result
        evaluation = completion.choices[0].message.to_dict().get("parsed").get("evaluation_result")

        if evaluation == 0 or evaluation == 2:
            valid = True
        else:
            valid = False

    except Exception as e:
        if e == BadRequestError:
            if "400" in str(e):
                valid = False
            else:
                valid = True

        logging.exception(e)

    return valid

def get_image_bytes(image_url):
    import io

    # Fetch the image from the URL
    response = requests.get(image_url)
    response.raise_for_status()  # Raise HTTPError for bad responses (4xx and 5xx)

    # Open the image content with Pillow
    image = Image.open(io.BytesIO(response.content))

    print(image)

    if image.width * image.height > 16000 * 16000:  # Prevent huge images
        raise ValueError("Image dimensions too large")

    # Convert the image to JPEG bytes
    jpeg_bytes = io.BytesIO()
    image.convert("RGB").save(jpeg_bytes, format="JPEG")

    print(jpeg_bytes)

    # Reset the buffer pointer to the beginning
    jpeg_bytes.seek(0)

    return jpeg_bytes.getvalue()

def convert_firebase_url_to_gs(firebase_url):
    """Converts the Firebase Token URL of an image to its Bucket location URL"""
    # Step 1: Parse the URL
    parsed_url = urllib.parse.urlparse(firebase_url)

    # Step 2: Extract the bucket name from the path
    bucket_name = parsed_url.path.split('b/')[1].split('/')[0]  # Extract bucket name after 'b/'

    # Step 3: Extract the encoded file path and decode it
    encoded_file_path = parsed_url.path.split('/o/')[1]
    decoded_file_path = urllib.parse.unquote(encoded_file_path)

    # Step 4: Construct the gs:// URL
    gs_url = f"gs://{bucket_name}/{decoded_file_path}"
    return bucket_name, gs_url
def safe_search(path):
    """
    Calls Google Cloud Vision API to detect explicit content and face presence in images.
    Returns a dict with:
    - adult: "VERY_LIKELY" if adult content detected
    - has_face: True if at least one face is detected
    """
    client = vision.ImageAnnotatorClient()

    image = vision.Image()
    image.source.image_uri = path

    # Define the features we want to detect
    features = [
        vision.Feature(type=vision.Feature.Type.SAFE_SEARCH_DETECTION),
        vision.Feature(type=vision.Feature.Type.FACE_DETECTION)
    ]

    # Create and send the request
    request = vision.AnnotateImageRequest(image=image, features=features)
    response = client.annotate_image(request=request)

    # Check for errors
    if response.error.message:
        logging.error(f"Error in safe_search: {response.error.message}")
        return {"adult": "VERY_UNLIKELY", "has_face": True}  # Conservative approach on error

    # Process SafeSearch results
    safe_search = response.safe_search_annotation
    adult_likelihood = vision.Likelihood(safe_search.adult).name

    # Process face detection results - True if at least one face detected
    has_face = len(response.face_annotations) > 0

    return {
        "adult": adult_likelihood,
        "has_face": has_face
    }

class ScanImageAdvancedResult:
    def __init__(self, explicit, face):
        self.explicit = explicit
        self.face = face

    explicit: bool
    face: bool

def explicitImagesDetected(uid: str, image_ids: [str]):
    db = firestore.client()

    user_ref = db.collection("users").document(uid)
    user_doc = user_ref.get()

    user_ref.update({"mpeImprovements.images": image_ids})

    user_doc = user_ref.get()

    mpe_blockers, mpe_blockers_content = checkBlockers(user_doc)

    process_profile_rejection(uid, mpe_blockers, mpe_blockers_content)




def imageScan(uid, image_id) -> ScanImageAdvancedResult:
    """
    Advanced image scanning using Google Cloud Vision API.
    Returns a dict with scan results including:
    - strictExplicitness: True if adult content detected
    - face: True if face detected
    """
    db = firestore.client()

    # Get image URL from image document
    image_doc = db.collection("users").document(uid).collection("images").document(image_id).get()
    if not image_doc.exists:
        logging.error(f"Image {image_id} not found for user {uid}")
        return ScanImageAdvancedResult(explicit=False, face=False)

    image_url = image_doc.get("url")
    if not image_url:
        logging.error(f"No URL found for image {image_id}")
        return ScanImageAdvancedResult(explicit=False, face=False)

    # Use safe_search to check image
    try:
        vision_result = safe_search(image_url)
        print(vision_result)
        db.collection("users").document(uid).collection("meta").document("imageChecks").set({"scannedImagesFaces": {image_id: vision_result.get("has_face", False)}}, merge=True)
        return ScanImageAdvancedResult(explicit= vision_result.get("adult") == "VERY_LIKELY" or vision_result.get("adult") == "LIKELY", face=vision_result.get("has_face", False))

    except Exception as e:
        logging.error(f"Error scanning image {image_id} for user {uid}: {e}")
        return ScanImageAdvancedResult(explicit=False, face=False)

def textAllowed(text) -> bool:
    return moderate_content_leaner(text)

def safeRemoveImageMpeBlock(uid, user_doc, image_id):
    db = firestore.client()

    mpe_images = user_doc.to_dict().get("mpeImprovements", []).get("images")
    changed = False

    if image_id in mpe_images:
        mpe_images.remove(image_id)
        changed = True

    if changed:
        db.collection("users").document(uid).update({"mpeImprovements.images": mpe_images})

    return


def safeAddImageMpeBlock(uid, image_id):
    db = firestore.client()

    db.collection("users").document(uid).update(
        {"mpeImprovements.images": firestore.firestore.ArrayUnion([image_id])})

    return


def faceOnProfile(uid, user_doc: DocumentSnapshot) -> bool:

    # TODO: Explicit image logic here is not correct

    db = firestore.client()

    # Get the user's current public profile
    user_public_profile_ref = user_doc.get("publicProfile")

    print(user_public_profile_ref)

    user_public_profile = user_public_profile_ref.get().to_dict()
    user_npictures = user_public_profile.get("nPictures", [])
    current_user_images = [i.id for i in user_npictures]

    # Get meta document for image checks
    meta_doc_ref = db.collection("users").document(uid).collection("meta").document("imageChecks")
    meta_doc = meta_doc_ref.get()

    # If meta document exists, get the images that have been scanned
    if meta_doc.exists:
        scanned_images = meta_doc.to_dict().get("scannedImagesFaces", {})
    else:
        scanned_images = {}

    current_images = []
    explicit_images = []

    # Loop over current images, find out if one has a face

    for c in current_user_images:
        if c in scanned_images.keys():
            current_images.append(scanned_images[c])
        else:
            new_image_result = imageScan(uid, c)
            print(new_image_result.explicit)

            scanned_images[c] = new_image_result.face
            current_images.append(scanned_images[c])

            if new_image_result.explicit:
                explicit_images.append(c)

    meta_doc_ref.set({"scannedImagesFaces": scanned_images})

    if len(explicit_images) > 0:
        # TODO: Just add those to the mpeImprovements; maybe return
        db.collection("users").document(uid).update({"mpeImprovements.images": firestore.firestore.ArrayUnion(explicit_images)})

    print(current_user_images)
    print(current_images)

    # returns True if at least one image on current user profile
    return True in current_images

class ProfileBlockers:
    def __init__(self, prompts: bool, attributes: bool, images: bool, no_face: bool):
        self.prompts = prompts
        self.attributes = attributes
        self.images = images
        self.no_face = no_face
        self.total = prompts or attributes or images or no_face

    def __repr__(self):
        return (
            f"ProfileBlockers(\n"
            f"  prompts={self.prompts},\n"
            f"  attributes={self.attributes},\n"
            f"  images={self.images},\n"
            f"  no_face={self.no_face},\n"
            f"  total={self.total}\n"
            f")"
        )

class ProfileBlockersContent:
    def __init__(self, prompts: list[str], attributes: list[str], images: list[str], no_face: bool):
        self.prompts = prompts
        self.attributes = ["bio"] if "bio" in attributes else []
        self.images = images
        self.no_face = no_face

    def __repr__(self):
        return (
            f"ProfileBlockersContent(\n"
            f"  prompts={self.prompts},\n"
            f"  attributes={self.attributes},\n"
            f"  images={self.images},\n"
            f"  no_face={self.no_face}\n"
            f")"
        )

def checkBlockers(user_doc: DocumentSnapshot, current_user_mpe_prompts: [str] = None, current_user_mpe_attributes: [str] = None, current_user_mpe_images: [str] = None, current_user_mpe_no_face: [str] = None) -> (ProfileBlockers, ProfileBlockersContent):
    """Checks whether there is a reason to keep user in mandatory profile editing block"""

    user_data = user_doc.to_dict()

    # get values from user document
    user_mpe_prompts_ids: list[str] = user_data.get("mpeImprovements", {}).get("prompts", [])
    user_mpe_attributes_ids: list[str] = user_data.get("mpeImprovements", {}).get("attributes", [])
    user_mpe_images_ids: list[str] = user_data.get("mpeImprovements", {}).get("images", [])
    user_mpe_no_face: bool = user_data.get("mpeNoFace", False)

    # get any overrides
    if current_user_mpe_prompts is not None:
        user_mpe_prompts_ids = current_user_mpe_prompts
    if current_user_mpe_attributes is not None:
        user_mpe_attributes_ids = current_user_mpe_attributes
    if current_user_mpe_images is not None:
        user_mpe_images_ids = current_user_mpe_images
    if current_user_mpe_no_face is not None:
        user_mpe_no_face = current_user_mpe_no_face

    # return accordingly
    return (ProfileBlockers(prompts=len(user_mpe_prompts_ids)>0,
                           attributes="bio" in user_mpe_attributes_ids,
                           images=len(user_mpe_images_ids)>0,
                           no_face=user_mpe_no_face),
            ProfileBlockersContent(prompts=user_mpe_prompts_ids,
                                   attributes=user_mpe_attributes_ids,
                                   images=user_mpe_images_ids,
                                   no_face=user_mpe_no_face
            ))

def generateMpeTitleAndBody(kinky_prompts, kinky_attributes, kinky_images, no_face):
    explicit_items = {
        "1st prompt": "prompts0" in kinky_prompts,
        "2nd prompt": "prompts1" in kinky_prompts,
        "3rd prompt": "prompts2" in kinky_prompts,
        "bio": "bio" in kinky_attributes,
        "images": bool(kinky_images),
    }

    explicit_entries = [key for key, value in explicit_items.items() if value]

    if len(explicit_entries) == 0 and no_face:
        title = "Add a clear face picture to continue matching"
        description = "Every Chyrpe profile must include a clear photo of your face"
        return title, description

    elif len(explicit_entries) == 0:
        title = "Edit your profile to continue matching"
        description = f"""It features content not allowed outside the kink area, like sexualising or kinky themes.
            Kink is welcome in the new profile kink area or when a woman allows it in your chat."""
        return title, description

    else:
        if len(explicit_entries) > 1:
            title = f"Edit your {', '.join(explicit_entries[:-1])} and {explicit_entries[-1]} to continue matching"
        else:
            title = f"Edit your {explicit_entries[0]} to continue matching"
        description = f"""Your profile features content not allowed in that place, like sexualising or kinky themes.
            Kink is welcome in the new profile kink area or when a woman allows it in your chat.\n\n
            {"Please also ensure that you add at least one face picture to your profile." if no_face else ""}"""

    return title, description


def process_profile_acceptance(uid):

    db = firestore.client()

    db.collection("moderatorApproveProfiles").add({"uid": uid, "timestamp": firestore.SERVER_TIMESTAMP})

    return

def process_profile_rejection(uid, blockers: ProfileBlockers, content: ProfileBlockersContent) -> None:
    """
    Given the blockers and their associated content, generate the appropriate display
    title/body and create the document that will trigger moderatorRejectProfile.
    """
    # Only continue if at least one blocker is flagged.
    if not blockers.total:
        logging.info("No blockers detected; no rejection needed.")
        return

    db = firestore.client()
    user_doc = db.collection("users").document(uid).get()

    # Generate display strings; note that we combine the no_face flag from either source.
    no_face_flag = blockers.no_face or content.no_face
    print(generateMpeTitleAndBody(
        content.prompts, content.attributes, content.images, no_face_flag
    ))
    display_title, display_body = generateMpeTitleAndBody(
        content.prompts, content.attributes, content.images, no_face_flag
    )
    display_btn = "Edit Profile"  # you may adjust the button text as needed

    # Create the "marked" lists based on which content areas were flagged.
    marked_prompt_ids = content.prompts if blockers.prompts else []
    marked_attribute_ids = content.attributes if blockers.attributes else []
    marked_image_ids = content.images if blockers.images else []

    # Build the document (the new_value dict) that will be used in the event.
    new_value = {
        "uid": uid,  # Replace with the actual user id
        "markedImageIds": marked_image_ids,
        "markedPromptIds": marked_prompt_ids,
        "markedAttributeIds": marked_attribute_ids,
        "noFace": content.no_face if blockers.no_face else False,
        "displayTitle": display_title,
        "displayBody": display_body,
        "displayBtn": display_btn,
        "timestamp": firestore.SERVER_TIMESTAMP
    }

    if user_doc.get("gender") == "Female":
        db.collection("moderatorExtraChecksW").add(new_value)
        return

    else:
        db.collection("moderatorRejectProfiles").add(new_value)
        datetime_string = datetime.now().strftime("%Y%m%d")

        db.collection("mpeQuota").document(datetime_string).set({"blocksCount": firestore.firestore.Increment(1)}, merge=True)
