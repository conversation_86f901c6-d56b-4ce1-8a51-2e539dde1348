from google.cloud import secretmanager, firestore
from firebase_admin import firestore
from google.cloud.firestore_v1 import Field<PERSON>ilter, Or
import google.cloud.logging
from google.cloud.sql.connector import Connector
import logging
import pg8000
import requests
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter, Retry
import sqlalchemy
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# initializing log client
log_client = google.cloud.logging.Client()
log_client.setup_logging()

from dataclasses import dataclass
from typing import Dict
from enum import Enum

def log_retry_attempt(retry_state):
    """Logs retry attempts before each retry."""
    logging.warning(f"Retrying... Attempt {retry_state.attempt_number}")

def with_retry(max_attempts=6, min_wait=2, max_wait=20, exception_types=(Exception)):
    """Decorator to add retry logic with exponential backoff."""
    return retry(
        retry=retry_if_exception_type(exception_types),
        wait=wait_exponential(multiplier=1, min=min_wait, max=max_wait),  # Initial wait 2s, doubles each time, max 10s
        stop=stop_after_attempt(max_attempts),  # Max 5 retry attempts
        before=log_retry_attempt,
        reraise=True
    )

class ConfigType(Enum):
    NUM = "num"
    SINGLE = "single"
    MULTI = "multi"
    SPECIAL = "special"

@dataclass
class UserConfig:
    type: ConfigType
    validate: bool
    postgres: bool
    amplitude_title: str
    amplitude_signup: bool

db_user_config: Dict[str, UserConfig] = {
    "height": UserConfig(type=ConfigType.NUM, validate=False, postgres=True, amplitude_title="Height", amplitude_signup=True),
    "publicRole": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Role",  amplitude_signup=False),
    "experienceLevel": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Experience Level",  amplitude_signup=True),
    "relationPreferences": UserConfig(type=ConfigType.MULTI, validate=False, postgres=True, amplitude_title="Relationship Preferences", amplitude_signup=True),
    "ethnicity": UserConfig(type=ConfigType.MULTI, validate=False, postgres=True, amplitude_title="Ethnicity", amplitude_signup=True),
    "children": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Children", amplitude_signup=True),
    "familyPlans": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Family Plans", amplitude_signup=True),
    "job": UserConfig(type=ConfigType.SPECIAL, validate=True, postgres=False, amplitude_title="Job", amplitude_signup=True),
    "employer": UserConfig(type=ConfigType.SPECIAL, validate=True, postgres=False, amplitude_title="Employer", amplitude_signup=True),
    "education": UserConfig(type=ConfigType.SPECIAL, validate=True, postgres=False, amplitude_title="School", amplitude_signup=True),
    "eduLevel": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Education Level", amplitude_signup=True),
    "religion": UserConfig(type=ConfigType.MULTI, validate=False, postgres=True, amplitude_title="Religion", amplitude_signup=True),
    "politics": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Politics", amplitude_signup=True),
    "drinking": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Drinking",amplitude_signup=True),
    "smoking": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Smoking", amplitude_signup=True),
    "workout": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=False, amplitude_title="Workout", amplitude_signup=True),
    "specificRole": UserConfig(type=ConfigType.SPECIAL, validate=True, postgres=False, amplitude_title="Specific Role", amplitude_signup=True),
    "specificGender": UserConfig(type=ConfigType.SPECIAL, validate=True, postgres=False, amplitude_title="Specific Gender", amplitude_signup=True),
    "personality": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=False, amplitude_title="Personality", amplitude_signup=True),
    "monoPolyRelationship": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=False, amplitude_title="Mono/Poly Relationship", amplitude_signup=True),
    "diet": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=False, amplitude_title="Diet", amplitude_signup=True),
    "zodiac": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=False, amplitude_title="Zodiac", amplitude_signup=True),
    "languages": UserConfig(type=ConfigType.MULTI, validate=False, postgres=False, amplitude_title="Languages",amplitude_signup=True),
    "pets": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=False, amplitude_title="Pets", amplitude_signup=True),
    "hobbies": UserConfig(type=ConfigType.MULTI, validate=False, postgres=False, amplitude_title="Hobbies", amplitude_signup=True),
    "bio": UserConfig(type=ConfigType.SPECIAL, validate=True, postgres=False, amplitude_title="Bio", amplitude_signup=False),
    "prompts": UserConfig(type=ConfigType.SPECIAL, validate=True, postgres=False, amplitude_title="Prompts", amplitude_signup=False),
}

def update_user_profile_dynamic(user_ref, public_ref, attribute, selection, hidden):
    user_ref.update({attribute: selection, f"{attribute}Hidden": hidden})
    if hidden:
        public_ref.update({attribute: firestore.firestore.DELETE_FIELD})
    else:
        public_ref.update({attribute: selection})

    return ''

def combine_job_employer(public_ref, attribute, selection):
    public_profile_data = public_ref.get().to_dict()

    user_employer = public_profile_data.get("employer", '')
    user_job = public_profile_data.get("job", '')

    if attribute == "employer":
        user_employer = selection
    else:
        user_job = selection

    if (user_job != '') and (user_employer != ''):
        return f'{user_job} at {user_employer}'
    elif user_job != '':
        return user_job
    elif user_employer != '':
        return user_employer
    else:
        return ''
def map_to_new(attribute_name, old_value):
    mappings = {
        "Degree": {
            "High school diploma": "High School",
            "Bachelor's degree": "Bachelor",
            "Master's degree": "Master or Doctorate",
            "PhD": "Master or Doctorate",
            "Vocational degree": "High School"  # No exact equivalent
        },
        "Diet": {
            "Omnivore": "Omnivore",
            "Vegetarian": "Vegetarian",
            "Vegan": "Vegan",
            "Pescatarian": "Pescatarian",
            "Keto": "Keto",
            "Paleo": "Paleo"
        },
        "Drinking": {
            "I don't drink alcohol": "No",
            "I drink socially": "Occasionally",
            "I drink occasionally": "Occasionally",
            "I drink regularly": "Yes"
        },
        "Family Plans": {
            "I want children": "Want children",
            "I do not want children": "Don’t want children",
            "I maybe want children": "Open to children",
            "I have children and want more": "Want children",
            "I have children and do not want more": "Don’t want children"
        },
        "Children": {
            "I want children": "",
            "I do not want children": "",
            "I maybe want children": "",
            "I have children and want more": "Yes",
            "I have children and do not want more": "Yes"
        },
        "Personality Type": {
            "INTJ": "INTJ", "INTP": "INTP", "ENTJ": "ENTJ", "ENTP": "ENTP",
            "INFJ": "INFJ", "INFP": "INFP", "ENFJ": "ENFJ", "ENFP": "ENFP",
            "ISTJ": "ISTJ", "ISFJ": "ISFJ", "ESTJ": "ESTJ", "ESFJ": "ESFJ",
            "ISTP": "ISTP", "ISFP": "ISFP", "ESTP": "ESTP", "ESFP": "ESFP"
        },
        "Pets": {
            "I don't have pets": "I don’t have pets",
            "I have one pet": "I have one pet",
            "I have two pets": "I have two pets",
            "I have three pets": "I have three pets",
            "I have 4+ pets": "I have 4+ pets"
        },
        "Smoking": {
            "I don't smoke": "No",
            "I smoke rarely": "Occasionally",
            "I smoke occasionally": "Occasionally",
            "I smoke regularly": "Yes"
        },
        "Workout": {
            "I don't workout": "I don’t workout",
            "I workout occasionally": "I workout occasionally",
            "I workout regularly": "I workout regularly",
            "I'm a fitness enthusiast": "I’m a fitness enthusiast"
        },
        "Zodiac": {
            "Aries": "Aries", "Taurus": "Taurus", "Gemini": "Gemini",
            "Cancer": "Cancer", "Leo": "Leo", "Virgo": "Virgo",
            "Libra": "Libra", "Scorpio": "Scorpio", "Sagittarius": "Sagittarius",
            "Capricorn": "Capricorn", "Aquarius": "Aquarius", "Pisces": "Pisces"
        },
    }

    if attribute_name in mappings:
        return mappings[attribute_name].get(old_value, None)
    return None

def connect_with_connector(connector:Connector) -> sqlalchemy.engine.base.Engine:

    """
    Initializes a connection pool for a Cloud SQL instance of Postgres.

    Uses the Cloud SQL Python Connector package.
    """
    def getconn() -> pg8000.dbapi.Connection:
        conn: pg8000.dbapi.Connection = connector.connect(
            "greenrocks-9abc3:europe-west2:chyrpe-prod1",
            "pg8000",
            user="<EMAIL>",
            db="chyrpe_main_db",
            enable_iam_auth=True,
        )
        return conn

    # The Cloud SQL Python Connector can be used with SQLAlchemy
    # using the 'creator' argument to 'create_engine'
    pool = sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        pool_size=2,
        max_overflow=2,
        # ...
    )
    return pool

title_to_key_mapper = {
    "Degree": "eduLevel",
    "Diet": "diet",
    "Drinking": "drinking",
    "Family Plans": "familyPlans",
    "Personality Type": "personality",
    "Pets": "pets",
    "Smoking": "smoking",
    "Workout": "workout",
    "Zodiac": "zodiac",
    "Experience Level": "experienceLevel",
    "Relationship Preferences": "relationPreferences",
    "Children": "children",
    "Hobbies": "hobbies",
    "Bio": "bio",
    "Prompts": "prompts"
}

PARAMETER_MAPPING = {
    "relationPreferences": "relationship_type",
    "ethnicity": "ethnicity",
    "religion": "religion",
    "eduLevel": "edu_level",
    "children": "children",
    "familyPlans": "family_plan",
    "politics": "politics",
    "drinking": "drinking",
    "smoking": "smoking",
    "height": "height",
    "findom_interest": "findom_interest",
    "user_vector": "vector"
}

def getUpdateParameters(user_dict: Dict):
    output_dict = []

    findom_interest = get_findom_interest_status(user_dict.get('kinks', []), user_dict.get('professionalsReq', False))

    for key in PARAMETER_MAPPING:
        value = user_dict.get(key)
        output_dict.append({"type": key, "value": findom_interest if key == 'findom_interest' else value})
        
    return output_dict

# Define possible values for each enum type
ENUM_DEFINITIONS = {
    "relationship_type": [
        "Long-term relationship", "Short-term relationship", "One night stand",
        "Play sessions", "Platonic relationship", "Virtual relationship"
    ],
    "ethnicity": [
        "Black/African Descent", "East Asian", "Hispanic/Latino", "Middle Eastern",
        "Native American", "Pacific Islander", "South Asian", "Southeast Asian",
        "White/Caucasian", "Other", "Prefer not to say"
    ],
    "religion": [
        "Agnostic", "Atheist", "Buddhist", "Catholic", "Christian", "Hindu",
        "Jewish", "Muslim", "Sikh", "Spiritual", "Other", "Prefer not to say"
    ],
    "edu_level": ["High School", "Bachelor", "Master or Doctorate", "Prefer not to say"],
    "children": ["Don’t have children", "Have children", "Prefer not to say"],
    "family_plan": [
        "Don’t want children", "Want children", "Open to children",
        "Not sure", "Prefer not to say"
    ],
    "politics": ["Left", "Center", "Right", "Not Political", "Other", "Prefer not to say"],
    "drinking": ["Yes", "Occasionally", "No", "Prefer not to say"],
    "smoking": ["Yes", "Occasionally", "No", "Prefer not to say"]
}

def create_one_multi_hot_vector(data):
    """
    Creates a one/multi-hot encoding vector based on the input data.

    Parameters:
        data (dict): A dictionary containing multi-hot arrays and single-hot values.
                     Example:
                     {
                         "RelationshipType": ["Short-term relationship", "One night stand"],
                         "Ethnicity": ["Hispanic/Latino"],
                         "Religion": None,  # None value
                         "EduLevel": "Bachelor",
                         "Children": None,  # None value
                         "FamilyPlan": "Want children",
                         "Politics": "Left",
                         "Drinking": "Yes",
                         "Smoking": "No"
                     }

    Returns:
        np.ndarray: A one-dimensional array representing the chained one/multi-hot encoding.
    """
    encoding = []

    for key, values in data.items():
        enum_values = ENUM_DEFINITIONS.get(key)
        if enum_values == None:
            continue
        vector = [0] * (len(enum_values) + 1)  # +1 for "missing data" position

        if values is None:
            # Set the "missing data" position to 1
            vector[-1] = 1
        elif isinstance(values, list):  # Multi-hot encoding
            for value in values:
                if value in enum_values:
                    vector[enum_values.index(value)] = 1
        else:  # Single-hot encoding
            if values in enum_values:
                vector[enum_values.index(values)] = 1

        encoding.extend(vector)

    return f'[{",".join(map(str, encoding))}]'

def update_user_extras_dynamic(pool, user_id: str, updates: list[dict]):
    """
    Dynamically calls the `update_user_advanced` SQL function based on a list of updates.

    Args:
        user_id (str): The user_id of the user to update.
        updates (list[dict]): A list of dictionaries with "type" and "value" keys.
            Example: [{"type": "children", "value": "Yes"}, {"type": "drinking", "value": "No"}]
    """

    # Initialize all parameters with None
    parameters = {param: None for param in PARAMETER_MAPPING.keys()}
    parameters["user_id"] = user_id

    # Map the updates to the corresponding parameters
    for update in updates:
        param_type = update.get("type")
        value = update.get("value")

        if param_type not in PARAMETER_MAPPING:
            logging.exception(f"Invalid parameter type: {param_type}")
            continue

        # Convert list values to PostgreSQL array format
        if isinstance(value, list):
            value = "{" + ",".join(value) + "}"

        # Assign the value to the correct parameter
        parameters[PARAMETER_MAPPING[param_type]] = value

    user_vector = create_one_multi_hot_vector(parameters)
    parameters["user_vector"] = user_vector

    print(parameters)

    # Prepare the SQL query
    stmt = sqlalchemy.text("""
        SELECT update_user_extras(
        :user_id,
        :relationship_type,
        :ethnicity,
        :religion,
        :edu_level,
        :children,
        :family_plan,
        :politics,
        :drinking,
        :smoking,
        :height,
        :user_vector,
        :findom_interest
        );
    """)

    # Execute the query with typecasting
    with pool.connect() as db_conn:
        result = db_conn.execute(stmt, parameters).fetchall()

        db_conn.commit()

        pool.dispose()

def update_user_current(uid):
    db = firestore.client()

    user_ref = db.collection("users").document(uid)
    user_doc = user_ref.get()
    user_data = user_doc.to_dict()
    public_reference = user_data.get("publicProfile")

    public_profile_doc = public_reference.get()
    public_data = public_profile_doc.to_dict()

    more_list = public_data.get("moreAboutMe", [])

    update_parameters = []

    # update the more about me attributes
    for m in more_list:
        name = m.get("type")
        option = m.get("option")

        new_option = map_to_new(name, option)

        new_key = title_to_key_mapper[name]

        update_user_profile_dynamic(user_ref, public_reference, new_key, new_option, False)


    relationship_types = public_data.get("relationPreferences")

    update_user_profile_dynamic(user_ref, public_reference, "relationPreferences", relationship_types, False)

    @with_retry()
    def update_user_current_with_retry():
        connector = Connector(refresh_strategy="lazy")
        pool = connect_with_connector(connector)

        user_doc_new = user_ref.get().to_dict()
        user_params_update = getUpdateParameters(user_doc_new)

        update_user_extras_dynamic(pool, uid, user_params_update)

        pool.dispose()
        connector.close()
    
    update_user_current_with_retry()


from typing import Optional
def get_findom_interest_status(kinks_list: list[str], professionals_req: bool) -> Optional[bool]:
    """
    Based on a user's kink list and findom filter status, returns whether to classify them as a findom_interest.

    Args:
        kinks_list (list): A list of kinks associated with the user.
        findom_filter (bool): Indicates if the findom filter is enabled.

    Returns:
        Optional[bool]: True if the user is a findom_interest, False if explicitly opted out of findom interest, or None if undetermined.
    """

    if professionals_req:
        return False
    elif 'Findom' in kinks_list:
        return True
    else:
        return None


