# Imports in alphabetical order

from amplitude import Amplitude, BaseEvent, Identify, EventOptions
from datetime import datetime, timedelta
from firebase_admin import initialize_app
from firebase_functions import https_fn, identity_fn, options, scheduler_fn
from firebase_functions.firestore_fn import (
  on_document_created,
  on_document_deleted,
  on_document_updated,
  on_document_written,
  Event,
  Change,
  DocumentSnapshot,
)
from google.cloud import secretmanager, firestore
from firebase_admin import firestore
from firebase_admin import db as rtdb
from google.cloud.firestore_v1 import FieldFilter, Or
import google.cloud.logging
from google.cloud.sql.connector import Connector, IPTypes
import logging
import pg8000
import requests
from requests.adapters import H<PERSON>PAdapter, Retry
import sqlalchemy
from typing import Any
import nmatching as nm
import UserDataHelper as udh
import mpe_helper
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import math
from math import radians, sin, cos, sqrt, atan2
from firebase_admin.firestore import GeoPoint
from common_utils import is_dev_environment


# initializing log client
log_client = google.cloud.logging.Client()
log_client.setup_logging()

# defining common methods in global scope
def access_secret_version(secret_name):
    '''Access a secret from the project's secret manager'''
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/greenrocks-9abc3/secrets/{secret_name}/versions/latest"
    response = client.access_secret_version(name=name)
    return response.payload.data.decode('UTF-8')

def log_retry_attempt(retry_state):
    """Logs retry attempts before each retry."""
    logging.warning(f"Retrying... Attempt {retry_state.attempt_number}")

def with_retry(max_attempts=10, min_wait=2, max_wait=20, exception_types=(Exception)):
    """Decorator to add retry logic with exponential backoff."""
    return retry(
        retry=retry_if_exception_type(exception_types),
        wait=wait_exponential(multiplier=1, min=min_wait, max=max_wait),  # Initial wait 2s, doubles each time, max 10s
        stop=stop_after_attempt(max_attempts),  # Max 5 retry attempts
        before=log_retry_attempt,
        reraise=True
    )

@with_retry()
def changeUserBlockStatusSql(uid: str, block: bool):
    """
    Sets the `mpeBlock` column in the User table
    """
    db = firestore.client()
    try:
        connector = Connector(refresh_strategy="lazy")
        pool = connect_with_connector(connector)

        block_text = "TRUE" if block else "FALSE"

        with pool.connect() as db_conn:
            stmt = sqlalchemy.text(f"""
                UPDATE "User" 
                SET mpeBlock = :block_text
                WHERE user_id = :user_id;"""
            )

            db_conn.execute(stmt, {
                'block_text': block_text,
                'user_id': uid
            })
            db_conn.commit()

    except Exception as e:
        logging.exception(e)
        db.collection('SQLExceptions').add({
            'function': 'change_user_block_status_in_sql',
            'user': uid,
            'error': str(e),
            'time': firestore.SERVER_TIMESTAMP
        })

def connect_with_connector(connector:Connector) -> sqlalchemy.engine.base.Engine:

    """
    Initializes a connection pool for a Cloud SQL instance of Postgres.

    Uses the Cloud SQL Python Connector package.
    """
    def getconn() -> pg8000.dbapi.Connection:
        conn: pg8000.dbapi.Connection = connector.connect(
            "greenrocks-9abc3:europe-west2:chyrpe-prod1",
            "pg8000",
            user="<EMAIL>",
            db="chyrpe_main_db",
            enable_iam_auth=True,
        )
        return conn

    # The Cloud SQL Python Connector can be used with SQLAlchemy
    # using the 'creator' argument to 'create_engine'
    pool = sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        pool_size=2,
        max_overflow=2,
        # ...
    )
    return pool

db_conn_new = None
def connect_with_connector_persist():
    # initialize Cloud SQL Python Connector object
    connector = Connector(refresh_strategy="LAZY")

    def getconn() -> pg8000.dbapi.Connection:
        conn: pg8000.dbapi.Connection = connector.connect(
            "greenrocks-9abc3:europe-west2:chyrpe-prod1",
            "pg8000",
            user="<EMAIL>",
            db="chyrpe_main_db",
            enable_iam_auth=True,
            ip_type=IPTypes.PUBLIC
        )
        return conn

    # The Cloud SQL Python Connector can be used with SQLAlchemy
    # using the 'creator' argument to 'create_engine'
    pool = sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        # [START_EXCLUDE]
        # Pool size is the maximum number of permanent connections to keep.
        pool_size=5,
        # Temporarily exceeds the set pool_size if no connections are available.
        max_overflow=2,
        # The total number of concurrent connections for your application will be
        # a total of pool_size and max_overflow.
        # 'pool_timeout' is the maximum number of seconds to wait when retrieving a
        # new connection from the pool. After the specified amount of time, an
        # exception will be thrown.
        pool_timeout=30,  # 30 seconds
        # 'pool_recycle' is the maximum number of seconds a connection can persist.
        # Connections that live longer than the specified amount of time will be
        # re-established
        pool_recycle=1800,  # 30 minutes
        # [END_EXCLUDE]
    )
    return pool

def create_retry_session() -> requests.Session:
    session = requests.Session()
    retries = Retry(total=5, backoff_factor=0.1, status_forcelist=[429, 500, 502, 503, 504])
    session.mount('https://', HTTPAdapter(max_retries=retries))
    return session
def find_all_likes_of_two_users(user1_id: str, user2_id: str, likes_collection) -> list[DocumentSnapshot]:
    '''Returns all likes documents that include 2 given users by id'''
    filter_1 = FieldFilter("involedUIDs", "==", [user1_id, user2_id])
    filter_2 = FieldFilter("involedUIDs", "==", [user2_id, user1_id])

    # Create the union filter of the two filters (queries)
    or_filter = Or(filters=[filter_1, filter_2])

    # Execute the query
    docs = likes_collection.where(filter=or_filter).get()

    return docs

def moderate_content(scannable_content):
    valid = True

    from pydantic import BaseModel
    from openai import AzureOpenAI, BadRequestError

    client = AzureOpenAI(
        azure_endpoint="https://chyrpegptp.openai.azure.com/",
        api_key=access_secret_version("OpenAI_Key"),
        api_version="2024-10-21"
    )

    class LanguageEvaluation(BaseModel):
        input_text: str
        evaluation_result: int

    try:
        # Call the GPT model with the schema
        completion = client.beta.chat.completions.parse(
            model="gpt-4o-mini",  # replace with the actual model deployment name
            messages=[
                {"role": "system", "content": "Evaluate the provided text for inappropriate language."},
                {"role": "user", "content": scannable_content}
            ],
            response_format=LanguageEvaluation,  # Using the schema here
        )

        # Parse and print the result
        evaluation = completion.choices[0].message.to_dict().get("parsed").get("evaluation_result")

        if evaluation == 0:
            valid = True
        else:
            valid = False

    except Exception as e:
        logging.exception(e)
        valid = False

    return valid


def sendPushNotification(uid, title, text, push_notifications_collection):
    push_notifications_collection.add({"notification_title": title, "notification_text": text,
                                       "user_refs": f'/users/{uid}',
                                       "timestamp": firestore.SERVER_TIMESTAMP})
# defining common variables
amplitude_client = Amplitude("65dbac83dec4e06df231ca34a83e4757")

### Actual cloud functions

initialize_app()

# CHANGE DISPLAY LOCATION: Queries API for location autocomplete suggestions for user
@https_fn.on_call(region="europe-west2", concurrency=20, memory=options.MemoryOption.MB_512, enforce_app_check=True)
def changeDisplayLocation(req: https_fn.CallableRequest) -> Any:
    """
    Queries API for location autocomplete suggestions for user
    :param req: Callable request incl. user auth information - incl. "inputString" (String)
    :return: None
    """
    if req.auth == None:
        return "User not authenticated"

    def generateTempToken(pk):
        """Generates a signed token from the private key"""
        import jwt
        import time

        key_id = 'TXUYCVJ6AL'
        team_id = 'LH6UJYUCPY'
        client_id = 'maps.com.chyrpe'

        headers = {
            "alg": "ES256",
            "kid": key_id
        }

        # Create JWT payload
        payload = {
            "iss": team_id,
            "iat": int(time.time()),
            "exp": int(time.time())+3600,  # 60 minutes expiration
            "origin": client_id
        }

        # Generate the token
        token = jwt.encode(payload, pk, algorithm='ES256', headers=headers)

        return token

    def obtainSessionToken(tempToken, session):
        """Uses the generated token to obtain a session token from the applicable Apple API"""
        url = "https://maps-api.apple.com/v1/token"

        payload = {}
        headers = {
            'Authorization': f'Bearer {tempToken}'
        }

        response = session.get(url, headers=headers, data=payload)

        if response.status_code == 200:
            data = response.json()
            return data
        else:
            return {}

    def obtainSuggestions(sessionToken, session, inputString):
        """Calls the actual autocomplete suggestions API"""
        import urllib.parse
        url = f"https://maps-api.apple.com/v1/searchAutocomplete?q={urllib.parse.quote(inputString)}"

        payload = {}
        headers = {
            'Authorization': f'Bearer {sessionToken}'
        }

        response = session.get(url, headers=headers, data=payload)

        if response.status_code == 200:
            data = response.json()
            return data
        else:
            return {}

    def obtainFurtherInfo(sessionToken, session, url):
        """Calls API endpoint given in autocomplete response"""
        payload = {}
        headers = {
            'Authorization': f'Bearer {sessionToken}'
        }

        response = session.get(f"https://maps-api.apple.com{url}", headers=headers, data=payload)

        if response.status_code == 200:
            data = response.json()
            return data
        else:
            return {}

    # create a session and get the secret from secretmanager
    session = create_retry_session()
    pk = access_secret_version("Apple_Maps_Private_Key")

    # get a temporary and session token
    temp_token = generateTempToken(pk)
    session_token = obtainSessionToken(temp_token, session).get("accessToken", '')

    if session_token == '':
        return []

    input_string = req.data['inputString']

    # get suggestions
    suggestions_dict = obtainSuggestions(session_token, session, input_string)

    suggestion_results = suggestions_dict.get("results", [])

    if suggestion_results == []:
        return []

    return_locations = []

    # for first 4 suggestions, obtain further info from the provided endpoints
    for r in suggestion_results[0:4]:
        further_link = r.get("completionUrl", '')
        if further_link == '':
            continue
        else:
            further_info = obtainFurtherInfo(session_token, session, further_link)

        further_info_results = further_info.get("results", [])

        if further_info_results == []:
            continue

        structured_address = further_info_results[0].get("structuredAddress")

        return_location1 = f"{structured_address.get('locality')+', ' if structured_address.get('locality', '') != '' else ''}{structured_address.get('administrativeAreaCode')+', ' if structured_address.get('administrativeAreaCode', '') != '' else ''}{further_info_results[0].get('country') if further_info_results[0].get('country', '') != '' else ''}"
        return_location2 = f"{structured_address.get('locality')}"
        return_location3 = f"{structured_address.get('administrativeArea')+', ' if structured_address.get('administrativeArea', '') != '' else ''}{further_info_results[0].get('country') if further_info_results[0].get('country', '') != '' else ''}"

        return_locations.append(return_location1)
        return_locations.append(return_location2)
        return_locations.append(return_location3)

    # make sure returned locations are unique
    unique_locations = list(set(return_locations))
    unique_locations.sort()

    return unique_locations


# CHANGE INCOGNITO MODE: Updates user status to incognito, incl. in Postgres
@https_fn.on_call(region="europe-west2", concurrency=1, memory=options.MemoryOption.MB_512, enforce_app_check=True)
def changeIncognitoMode(req: https_fn.CallableRequest) -> Any:
    """
    Updates user status to incognito, incl. in Postgres
    :param req: Callable request incl. user auth information - no extra params
    :return: None
    """
    if req.auth == None:
        return "User not authenticated"

    db = firestore.client()
    uid = req.auth.uid

    user_doc = db.collection('users').document(uid)
    user_data = user_doc.get().to_dict()

    # ascertains user's incognito status
    if user_data.get('incognito', False) == True:
        incognito_status = True
    else:
        incognito_status = False

    # updates incognito status in Postgres
    try:

        try:
            @with_retry()
            def update_incognito_with_retry():
                connector = Connector(refresh_strategy="lazy")
                pool = connect_with_connector(connector)

                try:

                    with pool.connect() as db_conn:

                        stmt = sqlalchemy.text(
                            """SELECT update_user(p_user_id => :user_id, p_incognito => :incognito)""")

                        # Execute with the parameters
                        result = db_conn.execute(stmt, {
                            'user_id': uid,
                            'incognito': incognito_status}).fetchall()

                        db_conn.commit()

                finally:
                    pool.dispose()
                    connector.close()

            update_incognito_with_retry()

        except Exception as e:
            logging.exception(e)
            db.collection('SQLExceptions').add(
                {'function': 'changeIncognitoMode', 'user': uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

    except:
        logging.exception(f"Fail: Change Incognito Mode: uid: {uid}")

    return

@https_fn.on_call(region="europe-west2", concurrency=1, enforce_app_check=True)
def changeKinkStatus(req: https_fn.CallableRequest) -> Any:
    return

# CHANGE PAUSED MODE: Updates user status to paused, incl. in Postgres
@https_fn.on_call(region="europe-west2", concurrency=80, memory=options.MemoryOption.MB_512, enforce_app_check=True)
def changePausedMode(req: https_fn.CallableRequest) -> Any:
    """
       Updates user status to paused, incl. in Postgres
       :param req: Callable request incl. user auth information - no extra params
       :return: None
    """
    if req.auth == None:
        return "User not authenticated"

    db = firestore.client()
    uid = req.auth.uid

    logging.info(f"Invoked for uid: {uid}")

    user_doc = db.collection('users').document(uid)
    user_data = user_doc.get().to_dict()

    if user_data.get('paused', False) == True:
        paused_status = True
    else:
        paused_status = False

    if user_data.get('globalMatching', False) == True:
        global_status = True
    else:
        global_status = False

    if user_data.get('localMatching', False) == True:
        regional_status = True
    else:
        regional_status = False

    if global_status == False and regional_status == False:
        return

    logging.info(f"Paused status for uid {uid}: {paused_status}")

    try:
        try:
            @with_retry()
            def update_paused_with_retry():
                connector = Connector(refresh_strategy="lazy")
                pool = connect_with_connector(connector)

                try:

                    with pool.connect() as db_conn:

                        stmt = sqlalchemy.text(
                            """SELECT update_user(p_user_id => :user_id, p_match_status => :match_status)""")

                        # Execute with the parameters
                        result = db_conn.execute(stmt, {
                            'user_id': uid,
                            'match_status': 'Neither' if paused_status else ('Both' if regional_status and global_status else (
                                'RegionalMatch' if regional_status else 'GlobalMatch'))}).fetchall()

                        db_conn.commit()

                finally:
                    pool.dispose()
                    connector.close()

            update_paused_with_retry()

        except Exception as e:
            logging.exception(e)
            db.collection('SQLExceptions').add(
                {'function': 'changePausedMode', 'user': uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})
    except:
        logging.exception("Fail: Change Paused Mode")

    return


def updateAllFindomLikes(uid):
    """Updates all likes ever made by findom to be declared as findom likes"""
    db = firestore.client()

    batch = db.batch()

    likes_collection = db.collection("likes")
    users_collection = db.collection("users")

    users_reference = users_collection.document(uid)

    likes_from_user = likes_collection.where(filter=FieldFilter("likingUser", "==", users_reference)).get()

    counter = 0
    for doc in likes_from_user:
        doc_ref = doc.reference
        batch.update(doc_ref, {'findom': True})
        counter += 1

        if counter == 500:
            batch.commit()
            batch = db.batch()
            counter = 0

    if counter > 0:
        batch.commit()


def changeProfessionalStatusCore(uid):
    db = firestore.client()

    user_doc = db.collection('users').document(uid)
    user_data = user_doc.get().to_dict()

    if user_data.get('findom', False) == True:
        findom_status = True
    else:
        findom_status = False

    event = BaseEvent(event_type=f"Findom: Set to {findom_status}", user_id=uid)
    amplitude_client.track(event)

    try:
        try:
            @with_retry()
            def update_professional_status_with_retry():
                connector = Connector(refresh_strategy="lazy")
                pool = connect_with_connector(connector)

                try:

                    with pool.connect() as db_conn:

                        if findom_status == True:

                            stmt = sqlalchemy.text(
                                """SELECT add_professional(p_user_id => :user_id)""")

                            # Execute with the parameters
                            result = db_conn.execute(stmt, {
                                'user_id': uid,
                            }).fetchall()

                            db_conn.commit()
                            logging.info(f"Committed updated user in SQL: calling user: {uid}")

                        else:

                            stmt = sqlalchemy.text(
                                """SELECT remove_professional(p_user_id => :user_id)""")

                            # Execute with the parameters
                            result = db_conn.execute(stmt, {
                                'user_id': uid,
                            }).fetchall()

                            db_conn.commit()
                            logging.info(f"Committed updated user in SQL: calling user: {uid}")

                finally:
                    pool.dispose()
                    connector.close()

            update_professional_status_with_retry()

            if findom_status:
                updateAllFindomLikes(uid)

        except Exception as e:
            logging.exception(e)
            db.collection('SQLExceptions').add(
                {'function': 'changeProfessionalStatus', 'user': uid, 'error': str(e),
                 'time': firestore.SERVER_TIMESTAMP})
    except:
        logging.exception("Fail: Change Paused Mode")

    return

@on_document_created(document="findomEnable/{documentId}", region="europe-west1", memory=options.MemoryOption.GB_1, timeout_sec=30)
def enableProfessionalStatusAdmin(event: Event[Change[DocumentSnapshot]]) -> None:

    new_value = event.data.to_dict()

    uid = new_value.get("uid")

    db = firestore.client()

    user_data = db.collection("users").document(uid).get().to_dict()

    db.collection("users").document(uid).update({"findom": True})
    user_data.get("publicProfile").update({"findom": True})

    changeProfessionalStatusCore(uid)

@on_document_created(document="findomChange/{documentId}", region="europe-west1", memory=options.MemoryOption.GB_1, timeout_sec=30)
def changeProfessionalStatusAdmin(event: Event[Change[DocumentSnapshot]]) -> None:

    new_value = event.data.to_dict()

    uid = new_value.get("uid")

    print("uid")

    changeProfessionalStatusCore(uid)


# CHANGE PROFESSIONAL STATUS: Updates findom Status, incl. in Postgres
@https_fn.on_call(region="europe-west1", memory=options.MemoryOption.GB_1, enforce_app_check=True)
def changeProfessionalStatus(req: https_fn.CallableRequest) -> Any:
    """
       Updates findom status, incl. in Postgres
       :param req: Callable request incl. user auth information - no extra params
       :return: None
    """
    if req.auth == None:
        return "User not authenticated"

    changeProfessionalStatusCore(req.auth.uid)


# CHANGE UR EMAIL: Updates user's self-declared email address for phone-authenticated users in RTDB (to be deprecated)
@https_fn.on_call(region="europe-west2", enforce_app_check=True)
def changeUrEmail(req: https_fn.CallableRequest) -> Any:
    """
       Updates user's self-declared email address for phone-authenticated users
       :param req: Callable request incl. user auth information - no extra params
       :return: None
    """
    if req.auth == None:
        return "User not authenticated"

    uid = req.auth.uid
    # initialize firestore
    db = firestore.client()

    # set user doc ref and get user doc, extract data and from data region
    user_doc_ref = db.collection("users").document(uid)

    user_doc = user_doc_ref.get()

    user_doc_data = user_doc.to_dict()

    user_email = user_doc_data['email']

    rtdb_db = rtdb.reference('users')
    rtdb_user_ref = rtdb_db.child(uid)
    rtdb_user_ref.update({
        'contact/email': user_email,
    })

# CHANGE UR PHONE NUMER: Updates user's authentication provider phone number for phone-authenticated users (to be deprecated)
@https_fn.on_call(region="europe-west2", enforce_app_check=True)
def changeUrPhoneNumber(req: https_fn.CallableRequest) -> Any:
    """
       Updates user's authentication provider phone number for phone-authenticated users
       :param req: Callable request incl. user auth information - no extra params
       :return: None
    """

    if req.auth == None:
        return "User not authenticated"

    uid = req.auth.uid
    # initialize firestore
    db = firestore.client()

    # set user doc ref and get user doc, extract data and from data region
    user_doc_ref = db.collection("users").document(uid)

    user_doc = user_doc_ref.get()

    user_doc_data = user_doc.to_dict()

    user_phone = user_doc_data['phone_number']

    rtdb_db = rtdb.reference('users')
    rtdb_user_ref = rtdb_db.child(uid)
    rtdb_user_ref.update({
        'contact/phoneNumber': user_phone,
    })

from firebase_admin import storage

def deleteAdminCore(uid):
    db = firestore.client()

    # 0. creates delete event in Amplitude
    try:
        user_doc = db.collection('users').document(uid).get().to_dict()
        user_gender = user_doc.get("gender")
        user_age = user_doc.get("age")
        user_on_waiting_list = user_doc.get("onWaitingList", False)
        user_on_global_waiting_list = user_doc.get("onGlobalWaitingList", False)
        user_likes_received = user_doc.get("likesReceived", 0)
        user_likes_given = user_doc.get("likesGiven", 0)
        user_dislikes_given = user_doc.get("dislikesGiven", 0)
        user_dislikes_received = user_doc.get("dlr", 0)
        user_region = user_doc.get("wlRegion")
        user_created = user_doc.get("created_time")
        user_subscriber = user_doc.get("chyrpeStandardSubscriber", False)

        identify_obj = Identify()
        identify_obj.set("Deleted", True)
        amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

        event = BaseEvent(event_type="Deleted Own Account", user_id=uid, event_properties={
            "Gender": user_gender,
            "Age": user_age,
            "On Waiting List": user_on_waiting_list,
            "On Global Waiting List": user_on_global_waiting_list,
            "Likes Received": user_likes_received,
            "Dislikes Received": user_dislikes_received,
            "Likes Given": user_likes_given,
            "Dislikes Given": user_dislikes_given,
            "Region": user_region,
            "Created Time": user_created,
            "Standard Subscriber": user_subscriber
        })
        amplitude_client.track(event)
    except Exception as e:
        print(f"Amplitude Tracking did not work: {e}")
        logging.exception(e)
    except:
        print("Other error in matching")

    try:
        db.collection("users").document(uid).update({"paused": True})

        batch = db.batch()

        def delete_collection(coll_ref, batch_size):
            if batch_size == 0:
                return

            docs = coll_ref.list_documents(page_size=batch_size)
            deleted = 0

            for doc in docs:
                batch.delete(doc)
                deleted = deleted + 1

            if deleted >= batch_size:
                return delete_collection(coll_ref, batch_size)

        # 1. get all likes with which the user is involved to delete them + associated messages
        likes_collection = db.collection("likes")

        involved_likes_query = likes_collection.where(filter=FieldFilter("involedUIDs", "array_contains", uid))
        involved_likes = involved_likes_query.get()

        if len(involved_likes) > 0:
            for like_doc in involved_likes:
                # delete all subcollections of each like (effectively all messages) in batches
                collections = like_doc.reference.collections()
                for collection in collections:
                    delete_collection(collection, 30000)
                # delete the like itself
                batch.delete(like_doc.reference)

                like_filters_query = db.collection("likeFilters").where(filter=FieldFilter("like", "==", like_doc.reference))
                like_filters = like_filters_query.get()
                for like_filter in like_filters:
                    batch.delete(like_filter.reference)

        # 2. delete all pictures of the user

        storage_client = storage
        bucket = storage_client.bucket()
        blobs = bucket.list_blobs(prefix='users/' + uid)
        for blob in blobs:
            blob.delete()

        # 3. delete all notifications for that user

        notifications_collection = db.collection("notifications")

        user_notifications_query = notifications_collection.where(filter=FieldFilter("receiverUID", "==", uid))
        user_notifications = user_notifications_query.get()

        if len(user_notifications) > 0:
            for notification_doc in user_notifications:
                batch.delete(notification_doc.reference)

        # 3b. delete all notifications that the user sent

        user_notifications_query2 = notifications_collection.where(filter=FieldFilter("senderUID", "==", uid))
        user_notifications2 = user_notifications_query2.get()

        if len(user_notifications2) > 0:
            for notification_doc in user_notifications2:
                batch.delete(notification_doc.reference)

        # 4. delete push notifications

        push_notifications_collection = db.collection("ff_user_push_notifications")

        pn_query = push_notifications_collection.where(filter=FieldFilter("user_refs", "==", f'/users/{uid}'))
        user_pns = pn_query.get()

        if len(user_pns) > 0:
            for pn_doc in user_pns:
                batch.delete(pn_doc.reference)

        # 5. delete verifications

        verifications_collection = db.collection("verifications")

        verifications_query = verifications_collection.where(filter=FieldFilter("uid", "==", uid))
        veri_docs = verifications_query.get()

        if len(veri_docs) > 0:
            for veri_doc in veri_docs:
                batch.delete(veri_doc.reference)

        # 6. delete rtdb record

        rtdb_db = rtdb.reference('users')
        rtdb_user_ref = rtdb_db.child(uid)
        rtdb_user_ref.delete()

        # 7. delete all subcollections of user document

        user_doc_ref = db.collection("users").document(uid)
        user_doc = user_doc_ref.get().to_dict()
        user_gender = user_doc.get("gender")

        user_subcollections = user_doc_ref.collections()
        for collection in user_subcollections:
            delete_collection(collection, 25)

        # 8. delete the user doc itself

        user_doc_ref.delete()

        # 9. write it down so that we can delete from BigQuery

        deleted_accounts_note_collection = db.collection('deletedAccountsNotes')
        deleted_accounts_note_collection.add(
            {'uid': uid, 'timeOfDeletion': firestore.SERVER_TIMESTAMP, "finished": False, "gender": user_gender})

        batch.commit()

    except Exception as e:
        logging.exception(e)

        failed_delete_collection = db.collection("failedDeletes")
        failed_delete_collection.add({'user': uid, 'error': str(e)})

    # 10. delete user in Postgres - delete is cascading, so deleting user row will delete all their data
    try:
        @with_retry()
        def delete_user_with_retry():
            connector = Connector(refresh_strategy="lazy")
            pool = connect_with_connector(connector)

            try:
                with pool.connect() as db_conn:
                        stmt = sqlalchemy.text(
                            """DELETE FROM "User" WHERE user_id = :user_id;""")

                        db_conn.execute(stmt, {
                            'user_id': uid
                        })
                        db_conn.commit()


            finally:
                pool.dispose()
                connector.close()

        delete_user_with_retry()


    except Exception as e:

        logging.exception(e)

        db.collection('SQLExceptions').add(

            {'function': 'deleteOwnAccount', 'user': uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

    # delete user in Firebase Authentication



    try:
        auth.delete_user(uid)
    except:
        logging.exception("Could not delete user")

    return

# DELETE ACCOUNT ADMIN: Deletes a given user's account incl. all their data, admin-induced
@on_document_created(document="adminDeletions/{documentId}", region="europe-west1", memory=options.MemoryOption.MB_512, timeout_sec=540)
def deleteAccountAdmin(event: Event[Change[DocumentSnapshot]]) -> None:
    """
        Deletes a given user's account incl. all their data, admin-induced
       :param event: Includes affected user's information
       :return: None
    """
    new_value = event.data.to_dict()

    uid = new_value.get("uid")

    deleteAdminCore(uid)


# DELETE OWN ACCOUNT: Deletes the calling user's account incl. all their data
@https_fn.on_call(region="europe-west2", concurrency=1, memory=options.MemoryOption.MB_512, enforce_app_check=True)
def deleteOwnAccount(req: https_fn.CallableRequest) -> Any:
    """
        Deletes the calling user's account incl. all their data
        :param req: Callable request incl. user auth information - no extra params
        :return: None
    """
    if req.auth == None:
        return "User not authenticated"

    uid = req.auth.uid
    # initialize firestore
    db = firestore.client()

    # 0. creates delete event in Amplitude
    try:
        user_doc = db.collection('users').document(uid).get().to_dict()
        user_gender = user_doc.get("gender")
        user_age = user_doc.get("age")
        user_on_waiting_list = user_doc.get("onWaitingList", False)
        user_on_global_waiting_list = user_doc.get("onGlobalWaitingList", False)
        user_likes_received = user_doc.get("likesReceived", 0)
        user_likes_given = user_doc.get("likesGiven", 0)
        user_dislikes_given = user_doc.get("dislikesGiven", 0)
        user_dislikes_received = user_doc.get("dlr", 0)
        user_region = user_doc.get("wlRegion")
        user_created = user_doc.get("created_time")
        user_subscriber = user_doc.get("chyrpeStandardSubscriber", False)


        amplitude_client = Amplitude("65dbac83dec4e06df231ca34a83e4757")

        identify_obj = Identify()
        identify_obj.set("Deleted", True)
        amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

        event = BaseEvent(event_type="Deleted Own Account", user_id=uid, event_properties={
        "Gender": user_gender,
        "Age": user_age,
        "On Waiting List": user_on_waiting_list,
        "On Global Waiting List": user_on_global_waiting_list,
        "Likes Received": user_likes_received,
        "Dislikes Received": user_dislikes_received,
        "Likes Given": user_likes_given,
        "Dislikes Given": user_dislikes_given,
        "Region": user_region,
        "Created Time": user_created,
        "Standard Subscriber": user_subscriber
        })
        amplitude_client.track(event)
    except Exception as e:
        print(f"Amplitude Tracking did not work: {e}")
    except:
        print("Other error in matching")

    try:
        db.collection("users").document(uid).update({"paused": True})
        def delete_collection(coll_ref, batch_size):
            if batch_size == 0:
                return

            docs = coll_ref.list_documents(page_size=batch_size)
            deleted = 0

            for doc in docs:
                doc.delete()
                deleted = deleted + 1

            if deleted >= batch_size:
                return delete_collection(coll_ref, batch_size)

        # 1. get all likes with which the user is involved to delete them + associated messages
        likes_collection = db.collection("likes")

        involved_likes_query = likes_collection.where(filter=FieldFilter("involedUIDs", "array_contains", uid))
        involved_likes = involved_likes_query.get()

        if len(involved_likes) > 0:
            for like_doc in involved_likes:
                # delete all subcollections of each like (effectively all messages) in batches
                collections = like_doc.reference.collections()
                for collection in collections:
                    delete_collection(collection, 30000)
                # delete the like itself
                like_doc.reference.delete()

                like_filters_query = db.collection("likeFilters").where(filter=FieldFilter("like", "==", like_doc.reference))
                like_filters = like_filters_query.get()
                for like_filter in like_filters:
                    like_filter.reference.delete()

        # 2. delete all pictures of the user

        storage_client = storage
        bucket = storage_client.bucket()
        blobs = bucket.list_blobs(prefix='users/'+uid)
        for blob in blobs:
            blob.delete()

        # 3. delete all notifications for that user

        notifications_collection = db.collection("notifications")

        user_notifications_query = notifications_collection.where(filter=FieldFilter("receiverUID", "==", uid))
        user_notifications = user_notifications_query.get()

        if len(user_notifications) > 0:
            for notification_doc in user_notifications:
                notification_doc.reference.delete()

        # 3b. delete all notifications that the user sent

        user_notifications_query2 = notifications_collection.where(filter=FieldFilter("senderUID", "==", uid))
        user_notifications2 = user_notifications_query2.get()

        if len(user_notifications2) > 0:
            for notification_doc in user_notifications2:
                notification_doc.reference.delete()


        # 4. delete push notifications

        push_notifications_collection = db.collection("ff_user_push_notifications")

        pn_query = push_notifications_collection.where(filter=FieldFilter("user_refs", "==", f'/users/{uid}'))
        user_pns = pn_query.get()

        if len(user_pns) > 0:
            for pn_doc in user_pns:
                pn_doc.reference.delete()

        # 5. delete verifications

        verifications_collection = db.collection("verifications")

        verifications_query = verifications_collection.where(filter=FieldFilter("uid", "==", uid))
        veri_docs = verifications_query.get()

        if len(veri_docs) > 0:
            for veri_doc in veri_docs:
                veri_doc.reference.delete()

        # 6. delete rtdb record

        rtdb_db = rtdb.reference('users')
        rtdb_user_ref = rtdb_db.child(uid)
        rtdb_user_ref.delete()

        # 7. delete all subcollections of user document

        user_doc_ref = db.collection("users").document(uid)
        user_doc = user_doc_ref.get().to_dict()
        user_gender = user_doc.get("gender")

        user_subcollections = user_doc_ref.collections()
        for collection in user_subcollections:
            delete_collection(collection, 25)

        # 8. delete the user doc itself

        user_doc_ref.delete()

        # 9. write it down so that we can delete from BigQuery

        deleted_accounts_note_collection = db.collection('deletedAccountsNotes')
        delete_reason = ""
        delete_reason_textfield = ""
        try:
            delete_reason = req.data.get("deleteReason")
            delete_reason_textfield = req.data.get("deleteReasonTextfield")
        except Exception as e:
            logging.exception(e)
        deleted_accounts_note_collection.add({'uid': uid, 'timeOfDeletion': firestore.SERVER_TIMESTAMP, "finished": False, "gender": user_gender, "deleteReason": delete_reason, "deleteReasonTextfield": delete_reason_textfield})

    except Exception as e:

        failed_delete_collection = db.collection("failedDeletes")
        failed_delete_collection.add({'user': uid, 'error': str(e)})

    # 10. delete user from Postgres - this is cascading in Postgres, so deleting user also deletes all data linked to them in Postgres

    if not is_dev_environment():
        try:
            @with_retry()
            def delete_user_with_retry():
                connector = Connector(refresh_strategy="lazy")
                pool = connect_with_connector(connector)

                try:
                    with pool.connect() as db_conn:
                        stmt = sqlalchemy.text(
                            """DELETE FROM "User" WHERE user_id = :user_id;""")

                        db_conn.execute(stmt, {
                            'user_id': uid
                        })
                        db_conn.commit()


                finally:
                    pool.dispose()
                    connector.close()

            delete_user_with_retry()


        except Exception as e:

            logging.exception(e)

            db.collection('SQLExceptions').add(

                {'function': 'deleteOwnAccount', 'user': uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

    # 11. Delete user from Firebase Authentication

    try:
        auth.delete_user(uid)
    except:
        logging.exception("Could not delete user")

    return


def secureUserDataCore(uid):
    # initializing Firestore & Storage Bucket
    db = firestore.client()
    storage_client = storage
    bucket = storage_client.bucket('greenrocks-s1')


    def make_serializable(input_data):
        """
        Recursively processes a dictionary or list:
        - Extracts the `.id` from Firestore DocumentReference objects.
        - Converts all non-serializable values to strings for full JSON compatibility.
        """
        if isinstance(input_data, dict):
            return {key: make_serializable(value) for key, value in input_data.items()}
        elif isinstance(input_data, set):
            return [make_serializable(element) for element in list(input_data)]
        elif isinstance(input_data, list):
            return [make_serializable(element) for element in input_data]
        elif isinstance(input_data, firestore.DocumentReference):
            # Extract the ID from the Firestore reference object
            return input_data.id
        elif isinstance(input_data, firestore.GeoPoint):
            return [input_data.latitude, input_data.longitude]
        else:
            try:
                try:
                    # Test if the input is JSON serializable
                    json.dumps(input_data)
                    return input_data
                except (TypeError, ValueError):
                    # Convert non-serializable values to strings
                    return str(input_data)
            except:
                return "Not serialisable"

    def upload_json_from_string(filename, string):
        blob = bucket.blob(f"{uid}/{filename}")

        # Upload the JSON string
        blob.upload_from_string(json.dumps(string), content_type='application/json')

    # get all of the user's likes
    likes_collection = db.collection("likes")

    user_likes = likes_collection.where(filter=FieldFilter("involedUIDs", "array_contains", uid)).get()

    chatting_with = []
    matched_with = []

    for l in user_likes:

        like_dict = l.to_dict()

        involed_uids = like_dict.get("involedUIDs")
        chatting = like_dict.get("chatting")
        unmatched = like_dict.get("unmatched")

        for i in involed_uids:
            if chatting and not unmatched and i != uid:
                chatting_with.append(i)
            elif not unmatched and i != uid:
                matched_with.append(i)

        upload_json_from_string(f"likes/{l.id}/{l.id}.json", make_serializable(like_dict))

        messages = l.reference.collection("messages").get()

        for m in messages:
            upload_json_from_string(f"likes/{l.id}/messages/{m.id}.json", make_serializable(m.to_dict()))

    upload_json_from_string(f"chatting_with.json", chatting_with)
    upload_json_from_string(f"matched_with.json", matched_with)

    source_bucket_name = "greenrocks-9abc3.appspot.com"
    destination_bucket_name = "greenrocks-s1"

    # Source prefix and destination prefix
    source_prefix = f"users/{uid}/"
    destination_prefix = f"{uid}/images/"

    # Get the buckets
    source_bucket = storage_client.bucket(source_bucket_name)
    destination_bucket = storage_client.bucket(destination_bucket_name)

    # List and copy objects
    blobs = source_bucket.list_blobs(prefix=source_prefix)
    for blob in blobs:
        destination_blob_name = destination_prefix + blob.name[len(source_prefix):]
        source_bucket.copy_blob(blob, destination_bucket, destination_blob_name)
        print(f"Copied {blob.name} to {destination_blob_name}")

    users_collection = db.collection("users")

    user_doc = users_collection.document(uid).get()

    json_user_doc = make_serializable(user_doc.to_dict())

    print(json_user_doc)

    upload_json_from_string(f"{uid}_user_doc.json", json_user_doc)

    public_profile = user_doc.get("publicProfile").get()

    public_profile_doc_json = make_serializable(public_profile.to_dict())

    upload_json_from_string(f"{uid}_publicProfile_{public_profile.id}.json",
                            public_profile_doc_json)

    images_docs = user_doc.reference.collection("images").get()

    for i in images_docs:
        upload_json_from_string(f"{uid}/images/{i.id}.json", make_serializable(i.to_dict()))

@on_document_created(document="secureBlockDelete/{documentId}", region="europe-west1", memory=options.MemoryOption.GB_1, timeout_sec=540)
def secureUserData(event: Event[Change[DocumentSnapshot]]) -> None:
    new_value = event.data.to_dict()

    uid = new_value.get("uid")

    secureUserDataCore(uid)


# @on_document_updated(document="users/{uid}/publicProfile/{doc_id}", region="europe-west1", memory=options.MemoryOption.MB_512, timeout_sec=60)
# def moderateProfiles(event: Event[Change[DocumentSnapshot]]) -> None:
#     new_value = event.data.after.to_dict()
#     old_value = event.data.before.to_dict()
#
#     def sendSlackMessage(name, bio, userUID):
#         """
#         Slack notification regarding report
#         :param name: Name of user
#         :param bio: Bio/Prompts with potentially explicit content
#         :param userUID: UID of the reported user
#         :return:
#         """
#         webhook_url = '*********************************************************************************'
#         message = {
#             'text': f"""New auto-report for bio/prompts:
# {name}
# {bio}
# {userUID}
# """
#         }
#
#         # Send message to Slack
#         try:
#             response = requests.post(webhook_url, data=json.dumps(message),
#                                      headers={'Content-Type': 'application/json'})
#             response.raise_for_status()  # Check for errors
#         except requests.exceptions.RequestException as error:
#             print("Error sending message to Slack", error)
#
#     bio_new = new_value.get("bio", "")
#     bio_old = old_value.get("bio", "")
#
#
#     prompts_new = new_value.get("prompts", [])
#     prompts_old = old_value.get("prompts", [])
#
#     more_old = old_value.get("moreAboutMe", [])
#     more_new = new_value.get("moreAboutMe", [])
#
#     city = new_value.get("city")
#
#     if city == None:
#         return
#
#     uid = new_value.get("uid")
#     name = new_value.get("publicName")
#
#
#     logging.info(f"Moderating content for {uid}")
#
#     if bio_old == bio_new and prompts_old == prompts_new and more_old == more_new:
#         logging.info(f"Content clean for {uid}")
#         return
#
#     answers = ["", ""]
#
#     if prompts_new != []:
#         answers = [p.get("answer") for p in prompts_new]
#
#     answers_final = " ".join(answers)
#     bio_final = bio_new
#
#     scannable_content = " ".join([answers_final, bio_final])
#
#     valid = moderate_content(scannable_content)
#
#     if valid == False:
#         db = firestore.client()
#         logging.info(f"Content potentially problematic for {uid}")
#
#         db.collection("AutoReport").add(
#             {
#                 "reason": "Potentially in violation of rules",
#                 "reportedName": name,
#                 "reportedUID": uid,
#                 "reportedUser": db.collection("users").document(uid),
#                 "reviewed": False,
#                 "time": firestore.SERVER_TIMESTAMP,
#                 "reportedContent": scannable_content
#             }
#         )
#
#         sendSlackMessage(name, scannable_content, uid)
#
#     if more_new != []:
#         try:
#             nm.update_user_current(uid)
#         except Exception as e:
#             logging.exception(e)

now_string = datetime.now().strftime('%Y%m%d')

available_discounts = {
    'evolved1w': {"latestDiscountIos": "https://apps.apple.com/redeem?ctx=offercodes&id=6478389867&code=EVOLVED30OFF1W241125",
                  "latestDiscountAndroid": "chy-evolved-1w-240411:chy-evolved-20off-1w-241120",
                  "latestDiscountAmount": 30, "latestDiscountDayString": now_string,
                  "latestDiscountType": "evolvedWeekly", "discountEligibleNow": firestore.firestore.DELETE_FIELD},
    'evolved1m': {"latestDiscountIos": "https://apps.apple.com/redeem?ctx=offercodes&id=6478389867&code=EVOLVED30OFF1M241125",
                  "latestDiscountAndroid": "chy-evolved-1m-240411:chy-evolved-20off-1m-241120",
                  "latestDiscountAmount": 30, "latestDiscountDayString": now_string,
                  "latestDiscountType": "evolvedMonthly", "discountEligibleNow": firestore.firestore.DELETE_FIELD},
    'gold1w': {"latestDiscountIos": "https://apps.apple.com/redeem?ctx=offercodes&id=6478389867&code=GOLD30OFF1W241125",
               "latestDiscountAndroid": "chy-gold-1w-240411:chy-gold-20off-1w-241119",
                  "latestDiscountAmount": 30, "latestDiscountDayString": now_string,
                  "latestDiscountType": "goldWeekly", "discountEligibleNow": firestore.firestore.DELETE_FIELD},
    'gold1m': {"latestDiscountIos": "https://apps.apple.com/redeem?ctx=offercodes&id=6478389867&code=GOLD30OFF1M241125",
               "latestDiscountAndroid": "chy-gold-1m-240411:chy-gold-20off-1m-241119",
                  "latestDiscountAmount": 30, "latestDiscountDayString": now_string,
                  "latestDiscountType": "goldMonthly", "discountEligibleNow": firestore.firestore.DELETE_FIELD},
    'standard1m': {"latestDiscountIos": "https://apps.apple.com/redeem?ctx=offercodes&id=6478389867&code=STANDARD30OFF1M241125",
                   "latestDiscountAndroid": "chy-standard-1w-240418:chy-standard-25off-1m-241119",
                  "latestDiscountAmount": 30, "latestDiscountDayString": now_string,
                  "latestDiscountType": "standardMonthly", "discountEligibleNow": firestore.firestore.DELETE_FIELD},
}

eligible_discounts = {
    "0ijvlh0x": {"subscription": "Gold", "description": "Unfinished Gold Purchase"},
    "tzr7w1li": {"subscription": "Evolved", "description": "Unfinished Evolved Purchase"},
    "on1hsung": {"subscription": "Standard", "description": "Unfinished Standard Purchase"},
    "9yumbga1": {"subscription": "Evolved", "description": "Evolved Out of Likes Multiple"},
    "vae6ddgb": {"subscription": "GoldEvolved", "description": "Gold Evolved Chat Multiple"},
    "kj13jnqa": {"subscription": "GoldEvolved", "description": "Gold Evolved Evolved Tab Multiple"},
    "ool_comeback_standard": {"subscription": "Standard", "description": "Standard Come Back Out of Likes Multiple"},
    "ool_comeback_gold": {"subscription": "Gold", "description": "Gold Come Back Out of Likes Multiple"}
}

liking_limits_men = {
    "free": 3,
    "standard": 5,
    "gold_access": 7,
    "evolved_access": 10
}

def is_liking_eligible(current_entitlements, liking_limits, likes_done_today):
    if (not "gold_access" in current_entitlements) and (not "evolved_access" in current_entitlements) and (not "paid_standard_1w" in current_entitlements) and (not "paid_standard_lifetime" in current_entitlements):
        if likes_done_today >= liking_limits_men["free"]:
            return "ool_comeback_standard"
    elif (not "gold_access" in current_entitlements) and (not "evolved_access" in current_entitlements):
        if likes_done_today >= liking_limits_men["standard"]:
            return "ool_comeback_gold"
    else:
        return None

def getPopupEligibilityDiscoveryCore(uid, current_subscriptions, iso_time, switch_eligible, language = 'en') -> Any:
    """
           Logic to determine when to show which purchase, info... widget on discovery page to calling user
           :param req: Data includes "isoDate" (string), "currentSubscriptions" (string)
           :return: String determining if and what to show a screen on client side
    """
    db = firestore.client()

    # if uid == "GWAJKKgQ9nhSrhrpRu8cMUL2EaB3":
    #     return '''{"id": "SPECIALINFO-BAN", "title": "We removed an image from your profile", "content": "This is because we detected explicit content. This is not allowed on Chyrpe profiles. If you believe this was a mistake, please reach <NAME_EMAIL>.", "btnTitle": "Understood"}'''


    user_doc = db.collection('users').document(uid)

    user_data = db.collection('users').document(uid).get().to_dict()
    notifications_latest = user_data.get('notificationsLatest', {})


    last_gold_free = notifications_latest.get('goldFree', '********')
    last_gold_free2 = notifications_latest.get('goldFree2', '********')
    last_gold_offer = notifications_latest.get('goldOffer', '********')
    last_notifications = notifications_latest.get('notifications', '********')
    match_start = notifications_latest.get('matchStart', '********')
    standard_lite = notifications_latest.get('standardLite', '********')
    standard_lite2 = notifications_latest.get('standardLite2', '********')
    standard_legacy = notifications_latest.get('standardLegacy', '********')
    findom_latest = notifications_latest.get('findomLatest', None)
    show_standard = user_data.get("showStandardLiteScreen", False)
    purchase_intentions = user_data.get("purchaseIntention", {})
    purchase_int_gold = purchase_intentions.get("gold", False)
    purchase_int_evolved = purchase_intentions.get("evolved", False)
    match_start_signal = user_data.get("matchStart", False)
    boosted_until = user_data.get("boostedUntil", None)
    boosted_confirmation_shown = user_data.get("boostedConfirmationShown", True)
    account_creation_finished = user_data.get("accountCreationFinished", datetime.now())
    discount_eligible_now = user_data.get("discountEligibleNow")
    discount_eligible_day = user_data.get("discountEligibilityDay", "********")
    latest_discount_day = user_data.get("latestDiscountDayString", "********")
    normal_likes_done_today = user_data.get("normalLikesDoneToday", 0)
    latest_day_of_liking = user_data.get("latestDayOfLiking", "********")
    visited_today_count = user_data.get("visitedTodayCount")
    likes_given = user_data.get('likesGiven', 0)
    chosen_language = user_data.get("language")


    now = datetime.now()
    ago_3days = now - timedelta(3)
    ago_7days = now - timedelta(7)

    if latest_day_of_liking == now.strftime('%Y%m%d'):
        db.collection("users").document(uid).update({"visitedTodayCount": firestore.Increment(1)})
    else:
        db.collection("users").document(uid).update({"visitedTodayCount": 1})

    if not (language == chosen_language or language == None):
        db.collection("users").document(uid).update({"language": language})

    # querying other relevant data from users document

    created_time = user_data.get("created_time", '********').strftime('%Y%m%d')
    gender = user_data.get("gender")

    pn_allow = user_data.get('pushNotificationAllow', False)
    first_pn_asked = user_data.get('firstPnAsked', False)
    pn_decision = user_data.get('pushNotificationDecision', False)

    today_string = datetime.now().strftime('%Y%m%d')
    seven_days_ago_string = (datetime.now() - timedelta(7)).strftime('%Y%m%d')

    if (user_data.get("showKinksNotification") == True):
        db.collection('users').document(uid).update(
            {"showKinksNotification": False, 'notificationsLatest.kinkNav': datetime.now().strftime('%Y%m%d')})

        try:
            event = BaseEvent(event_type="Kink: Introduction Popup Shown", user_id=uid)
            amplitude_client.track(event)
        except Exception as e:
            logging.exception(e)

        return json.dumps({"id": "SPECIALINFO-KINKS-NAVIGATE", "title": "Now available:\nA space just for kinks",
                           "content": 'Check out the new "Kinks" option under "Edit Profile"! \n\n Outside that area, please only discuss kink if a woman invites it.',
                           "btnTitle": "Check it out"})

    if not findom_latest and likes_given > 1 and switch_eligible:
        # determine user-specific last times of showing specific popups, screens etc.
        if gender == 'Male':
            db.collection('users').document(uid).update(
                {'notificationsLatest.findomLatest': datetime.now().strftime('%Y%m%d')})

            return json.dumps([{"id": "likes-package-findoms", "titles": [{"title": "About Findom on Chyrpe"}], "descriptions": [
                "We want to put an end to matches with hidden intentions.\n\nAll Findoms on Chyrpe have to disclose, so you can filter them out.\n\nPlease report any undisclosed Findoms you see. It’s against rules."],
                                "buttonLabel": "Next", "initialToggleState": False, "isCircleButton": True,
                                "hideToggle": True}, {"titles": [{"title": "Would you like to see Findoms?"}],
                                                      "descriptions": [
                                                          "If you turn it off, their likes will appear separately too.\n\nYou can change this anytime."],
                                                      "buttonLabel": "Next", "isCircleButton": True,
                                                      "initialToggleState": True}])

    # if discount_eligible_now == None and (latest_discount_day != today_string) and (latest_discount_day < seven_days_ago_string):
    #     if gender == "Male":
    #         discount_eligible_now = is_liking_eligible(current_subscriptions, liking_limits_men, normal_likes_done_today)
    #
    # if (discount_eligible_now != None) and (discount_eligible_day == today_string) and (latest_discount_day < seven_days_ago_string) and (account_creation_finished.strftime("%Y%m%d") < ago_3days.strftime("%Y%m%d")):
    #     discount = eligible_discounts[discount_eligible_now]
    #
    #     if discount["subscription"] == "Standard":
    #         if not (("gold_access" in current_subscriptions) or ("evolved_access" in current_subscriptions) or (
    #                     "paid_standard_1w" in current_subscriptions) or (
    #                          "paid_standard_lifetime" in current_subscriptions)) and gender == "Male":
    #             event = BaseEvent(event_type=f"Given Discount: {discount['subscription']}", user_id=uid, event_properties={"percentage": 30})
    #             amplitude_client.track(event)
    #             event1 = BaseEvent(event_type=f"Given Discount",
    #                                user_id=uid,
    #                                event_properties={"type": discount['subscription'], "duration": "1m",
    #                                                  "percentage": 30})
    #             amplitude_client.track(event1)
    #             db.collection("users").document(uid).update(available_discounts["standard1m"])
    #             return 'standardDiscount'
    #
    #     if discount["subscription"] == "Gold":
    #         if not (("gold_access" in current_subscriptions) or ("evolved_access" in current_subscriptions)) and gender == "Male":
    #
    #             duration = random.choice(["gold1w", "gold1m"])
    #
    #             event = BaseEvent(event_type=f"Given Discount: {discount['subscription']}", user_id=uid, event_properties={"duration": duration, "percentage": 30})
    #             amplitude_client.track(event)
    #             event1 = BaseEvent(event_type=f"Given Discount",
    #                                user_id=uid,
    #                                event_properties={"type": discount['subscription'],
    #                                                  "duration": "1m" if "1m" in duration else "1w", "percentage": 30})
    #             amplitude_client.track(event1)
    #             db.collection("users").document(uid).update(available_discounts[duration])
    #             return 'goldDiscount'
    #
    #     if discount["subscription"] == "Evolved":
    #         if not (("gold_access" in current_subscriptions) or (
    #                 "evolved_access" in current_subscriptions)) and gender == "Male":
    #             duration = random.choice(["evolved1w", "evolved1m"])
    #
    #             event = BaseEvent(event_type=f"Given Discount: {discount['subscription']}", user_id=uid, event_properties={"duration": duration, "percentage": 30})
    #             amplitude_client.track(event)
    #             event1 = BaseEvent(event_type=f"Given Discount",
    #                                user_id=uid,
    #                                event_properties={"type": discount['subscription'],
    #                                                  "duration": "1m" if "1m" in duration else "1w", "percentage": 30})
    #             amplitude_client.track(event1)
    #             db.collection("users").document(uid).update(available_discounts[duration])
    #             return 'evolvedDiscount'
    #
    #     if discount["subscription"] == "GoldEvolved":
    #         if not (("gold_access" in current_subscriptions) or (
    #                 "evolved_access" in current_subscriptions)) and gender == "Male":
    #             duration = random.choice(["gold1m", "gold1w", "evolved1w", "evolved1m"])
    #
    #             event = BaseEvent(event_type=f"Given Discount: {'Evolved' if 'evolved' in duration else 'Gold'}", user_id=uid, event_properties={"duration": duration, "percentage": 30})
    #             amplitude_client.track(event)
    #             event1 = BaseEvent(event_type=f"Given Discount",
    #                               user_id=uid, event_properties={"type": 'Evolved' if 'evolved' in duration else 'Gold', "duration": "1m" if "1m" in duration else "1w", "percentage": 30})
    #             amplitude_client.track(event1)
    #             db.collection("users").document(uid).update(available_discounts[duration])
    #
    #             if duration == "gold1m" or duration == "gold1w":
    #                 return "goldDiscount"
    #
    #             if duration == "evolved1w" or duration == "evolved1m":
    #                 return "evolvedDiscount"
    #


    # logic for showing different popups, screens etc. - subject to change, certain planned and/or past versions may be commented, removed, altered etc.
    if match_start_signal and match_start < seven_days_ago_string:
        user_doc.update({'notificationsLatest.matchStart': datetime.now().strftime('%Y%m%d'),
                         "matchStart": False})
        return 'matchStart'


    # if show_standard and not (("gold_access" in current_subscriptions) or ("evolved_access" in current_subscriptions) or ("paid_standard_1w" in current_subscriptions) or ("paid_standard_lifetime" in current_subscriptions)) and gender == "Male":
    #     user_doc.update({'notificationsLatest.standardLite': datetime.now().strftime('%Y%m%d'), "showStandardLiteScreen": firestore.DELETE_FIELD})
    #     event = BaseEvent(event_type="Standard Lite: Show", user_id=uid)
    #     amplitude_client.track(event)
    #     return 'standardLite'
    #
    # if standard_lite == '********' and not (("gold_access" in current_subscriptions) or ("evolved_access" in current_subscriptions) or ("paid_standard_1w" in current_subscriptions) or ("paid_standard_lifetime" in current_subscriptions)) and gender == "Male":
    #     user_doc.update({'notificationsLatest.standardLite': datetime.now().strftime('%Y%m%d'), "showStandardLiteScreen": firestore.DELETE_FIELD})
    #     event = BaseEvent(event_type="Standard Lite: Show", user_id=uid)
    #     amplitude_client.track(event)
    #     return 'standardLite'

    # if account_creation_finished < ago_3days and not (("gold_access" in current_subscriptions) or ("evolved_access" in current_subscriptions) or ("paid_standard_1w" in current_subscriptions) or ("paid_standard_lifetime" in current_subscriptions)) and gender == "Male":
    #     if standard_lite == '********':
    #         user_doc.update({'notificationsLatest.standardLite': datetime.now().strftime('%Y%m%d'),"standardDecisionMade": firestore.DELETE_FIELD})
    #         event = BaseEvent(event_type="Standard Lite: Show", user_id=uid)
    #         amplitude_client.track(event)
    #         return 'standardLite'
    #
    # if account_creation_finished < ago_7days and not (("gold_access" in current_subscriptions) or ("evolved_access" in current_subscriptions) or ("paid_standard_1w" in current_subscriptions) or ("paid_standard_lifetime" in current_subscriptions)) and gender == "Male":
    #     if standard_lite2 == '********':
    #         user_doc.update({'notificationsLatest.standardLite2': datetime.now().strftime('%Y%m%d'), "standardDecisionMade": firestore.DELETE_FIELD})
    #         event = BaseEvent(event_type="Standard Lite: Show", user_id=uid)
    #         amplitude_client.track(event)
    #         return 'standardLiteDiscount'

    if boosted_until != None:
        if boosted_until.replace(tzinfo=timezone.utc) < datetime.now().replace(tzinfo=timezone.utc):
            if not boosted_confirmation_shown:
                user_doc.update({'boostedConfirmationShown': True})

                likes_query = db.collection("likes").where(filter=FieldFilter("timestamp", "<", boosted_until)).where(filter=FieldFilter("timestamp", ">", (boosted_until - timedelta(hours=2)))).where(filter=FieldFilter("likedUser", "==", db.collection('users').document(uid))).get()

                if len(likes_query) > 0:
                    return ''
                else:
                    return "boostConfirm"

    # if (purchase_int_gold != False) or (purchase_int_evolved != False) and last_gold_offer < seven_days_ago_string:
    #     if ("gold_access" in current_subscriptions) or ("evolved_access" in current_subscriptions):
    #         return ''
    #     elif purchase_int_gold.strftime('%Y%m%d') == today_string or purchase_int_evolved.strftime('%Y%m%d') == today_string:
    #         user_doc.update({"offerEligibleDayString": today_string, "notificationsLatest.goldOffer": today_string, "appleSubOfferLink": "https://apps.apple.com/redeem?ctx=offercodes&id=6478389867&code=GOLD1", "androidSubOfferString": "chy-gold-1m-240411-d"})
    #         return 'goldOffer'


    return ''

    # if created_time < '20241012' and ('standard_access' in current_subscriptions or gender == "Female") and match_start == '********':
    #     db.collection('users').document(uid).update({'notificationsLatest.matchStart': datetime.now().strftime('%Y%m%d')})
    #     return "matchStart"


from datetime import timezone
# GET POPUP ELIGIBILITY DISCOVERY: Logic to determine when to show which purchase, info... widget on discovery page to calling user
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.MB_512, enforce_app_check=True)
def getPopupEligibilityDiscovery(req: https_fn.CallableRequest) -> Any:
    uid = req.auth.uid
    iso_time = req.data['isoDate']
    current_subscriptions = req.data['currentSubscriptions']
    print(iso_time)
    print(current_subscriptions)

    print(f'booleanSwitchPopupCompatible: {req.data.get("booleanSwitchPopupCompatible")}')

    return getPopupEligibilityDiscoveryCore(uid, current_subscriptions, iso_time)


@https_fn.on_call(region="europe-west1", concurrency=100, memory=options.MemoryOption.GB_1, enforce_app_check=True)
def discoveryUpdaterCf(req: https_fn.CallableRequest) -> Any:
    uid = req.auth.uid
    current_subscriptions = req.data.get("currentSubscriptions")
    iso_time = req.data.get('isoDate')
    language = req.data.get('language')

    from datetime import timezone
    now_utc = datetime.now(timezone.utc)
    today = datetime.now()

    # Create a datetime object for midnight today in UTC
    last_monday = today - timedelta(days=today.weekday())

    popup = getPopupEligibilityDiscoveryCore(uid, current_subscriptions, iso_time, req.data.get("booleanSwitchPopupCompatible", False), language)

    print(f'booleanSwitchPopupCompatible: {req.data.get("booleanSwitchPopupCompatible")}')

    return {"dayString": today.strftime('%Y%m%d'), "mondayString": last_monday.strftime('%Y%m%d'), "popupString": popup}

# SAVE AGE: Verifies a user is 18 & saves this data in their users document
import random
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.MB_512, enforce_app_check=True)
def saveAge(req: https_fn.CallableRequest) -> Any:
    """
       Verifies a user is 18 & saves this data in their users document
       :param req: Data includes "dd" (string), "mm" (string), "yy" (string)
       :return: Dictionary with validity & underage status
    """
    if req.auth == None:
        return "User not authenticated"

    def calculateAge(birthDate):
        today = datetime.today()
        age = today.year - birthDate.year - ((today.month, today.day) < (birthDate.month, birthDate.day))
        return age

    def enroll_pricing_experiment(uid, value):
        import requests
        import json

        url = f"https://api.revenuecat.com/v1/subscribers/{uid}/attributes"

        payload = json.dumps({
            "attributes": {
                "lower_prices_250109": {
                    "value": str(value)
                }
            }
        })
        headers = {
            'Authorization': access_secret_version("Revenuecat_Key"),
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)


    uid = req.auth.uid
    day = int(req.data["dd"])
    month = int(req.data["mm"])
    year = int(req.data["yyyy"])
    new_style_verification = bool(req.data.get("newStyleVerification"))
    new_style_tutorial = bool(req.data.get("newStyleTutorial"))

    try:
        birth_date = datetime(year, month, day)
    except Exception as e:
        return { 'valid': False, 'underAge': False }

    age = calculateAge(birth_date)

    if age < 18:
        try:
            event = BaseEvent(event_type="Sign Up: Entered Birthday U18", user_id=uid)
            amplitude_client.track(event)
        except:
            print("Amplitude failed")

        return {'valid': True, 'underAge': True}


    db = firestore.client()
    user_doc_ref = db.collection("users").document(uid)
    user_doc = user_doc_ref.get()
    user_doc_data = user_doc.to_dict()
    user_gender = user_doc_data.get("gender")

    user_b_group = random.choice([True, False])
    if user_gender == 'Female':
        user_five_group = random.choice([1, 2, 3, 4, 5])
    else:
        user_five_group = -1

    user_lower_prices_group = random.choice([True, False])

    try:
        enroll_pricing_experiment(uid, user_lower_prices_group)
    except Exception as e:
        logging.exception(e)


    # month and day are stored as separate attributes for querying purposes in the update function
    user_doc_ref.update({
      'birthday': birth_date,
      'age': age,
      'day_born': day,
      'kinksOwnVisible': True if user_gender == "Male" else False,
      'kinkInterested': True if user_gender == "Male" else False,
      'month_born': month,
        'fiveTestGroup': user_five_group,
        'bGroup': user_b_group,
        'purchasesTestingNewDesign1': True,
        'lowerPrices_250109': user_lower_prices_group,
        'newStyleVerification': new_style_verification,
        'showTutorial': new_style_tutorial
    })

    user_public_prof_ref = user_doc_data.get("publicProfile")
    user_public_prof_ref.update({"age": age})

    user_email = user_doc_data.get("email")
    user_phone = user_doc_data.get("phone_number")

    rtdb_db = rtdb.reference('users')
    rtdb_user_ref = rtdb_db.child(uid)
    rtdb_user_ref.update({
        'details/age': age,
        'contact/email': user_email,
        'contact/phoneNumber': user_phone
    })

    try:
        identify_obj = Identify()
        identify_obj.set("Age", age)
        amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

        identify_obj2 = Identify()
        identify_obj2.set("Gender", user_gender)
        amplitude_client.identify(identify_obj2, EventOptions(user_id=uid))

        identify_obj3 = Identify()
        identify_obj3.set("bGroup", user_b_group)
        amplitude_client.identify(identify_obj3, EventOptions(user_id=uid))

        identify_obj4 = Identify()
        identify_obj4.set("fiveGroup", user_five_group)
        amplitude_client.identify(identify_obj4, EventOptions(user_id=uid))

        identify_obj5 = Identify()
        identify_obj5.set("lowerPrices_250109", user_lower_prices_group)
        amplitude_client.identify(identify_obj5, EventOptions(user_id=uid))

        event = BaseEvent(event_type="Sign Up: Entered Birthday Success", user_id=uid, event_properties={"Age": age})
        amplitude_client.track(event)

        event = BaseEvent(event_type="Assigned B Group Choice", user_id=uid, event_properties={"bGroup": user_b_group})
        amplitude_client.track(event)

        event = BaseEvent(event_type="Assigned 5 Group Choice", user_id=uid, event_properties={"fiveGroup": user_five_group})
        amplitude_client.track(event)

        event = BaseEvent(event_type="Assigned Lower Pricing Choice", user_id=uid,
                          event_properties={"lowerPricesGroup": user_lower_prices_group})
        amplitude_client.track(event)

        if user_b_group:
            event = BaseEvent(event_type="Assigned B Group", user_id=uid)
            amplitude_client.track(event)

        else:
            event = BaseEvent(event_type="Assigned A Group", user_id=uid)
            amplitude_client.track(event)


    except:
        print("Amplitude failed")

    return {'valid': True, 'underAge': False}

# SIGNEDIN NOOP: Runs before every sign up/sign up and automatically excludes blocked users
from google.cloud.firestore_v1 import aggregation
@identity_fn.before_user_signed_in(memory=options.MemoryOption.MB_512)
def signedin_noop(event: identity_fn.AuthBlockingEvent) -> identity_fn.BeforeSignInResponse | None:
    """
       Runs before every sign up/sign up and automatically excludes blocked users
       :param req: Due to trigger of cloud function, no practical relevance
       :return: None
    """
    user_phone_number = event.data.phone_number

    db = firestore.client()

    blockedUsersCollection = db.collection("blockedUsers")

    results = []

    # this has to work regardless of authentication provider (phone or else[which leads to email])

    if user_phone_number != None:

    # queries a user's phone number against the block data base
        query = blockedUsersCollection\
            .where(filter=FieldFilter("phoneNumber", "==", user_phone_number))\
            .where(filter=FieldFilter("blockedUntil", ">", datetime.now()))

        results = query.get()

    else:

    # queries a user's email against the block data base

        user_email = event.data.email

        if user_email != None:

            query = blockedUsersCollection \
                .where(filter=FieldFilter("email", "==", user_email)) \
                .where(filter=FieldFilter("blockedUntil", ">", datetime.now()))

            results = query.get()

    if len(results) > 0:
        logging.exception(f"{user_email} {user_phone_number}")
        raise https_fn.HttpsError(
            code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT, message="You have been blocked")
    else:
        return

@https_fn.on_call(region="europe-west1", memory=options.MemoryOption.GB_4, cpu=4, concurrency=80, min_instances=1, timeout_sec=540, enforce_app_check=True)
def saveSignUpData(req: https_fn.CallableRequest) -> Any:
    if req.auth == None:
        return '{"id": "rejected", "title": "Not authenticated", "content": "We could not identify your account. If this problem persists, please <NAME_EMAIL>"}'

    global db_conn_new

    if db_conn_new == None:
        db_conn_new = connect_with_connector_persist()

    @with_retry()
    def update_user_extras_with_retry(pool, uid, user_params_update):

        nm.update_user_extras_dynamic(pool, uid, user_params_update)


    # parse input data
    input_data = req.data
    raw_attribute = input_data.get("screenName")
    hidden = input_data.get("hidden", False)
    multi_selections = input_data.get("multiSelections")
    single_selection = input_data.get("singleSelection")

    prompt_number = 0

    # special treatment for prompts
    if "prompts" in raw_attribute:
        attribute = "prompts"
        prompt_number = int(raw_attribute[-1])
    else:
        attribute = raw_attribute

    # get correct config for the passed in data
    config = udh.db_user_config[attribute]

    # getting the user doc & references
    db = firestore.client()
    uid = req.auth.uid
    user_reference = db.collection("users").document(uid)

    user_doc = user_reference.get()
    user_doc_data = user_doc.to_dict()

    user_mpe_prompts_ids: list[str] = user_doc_data.get("mpeImprovements", {}).get("prompts", [])
    user_mpe_attributes_ids: list[str] = user_doc_data.get("mpeImprovements", {}).get("attributes", [])

    print(user_mpe_attributes_ids)

    for i, a in enumerate(user_mpe_attributes_ids):
        if a == "specific_gender":
            user_mpe_attributes_ids[i] = "specificGender"
        if a == "specific_role":
            user_mpe_attributes_ids[i] = "specificRole"

    user_mpe_prompts_ids_new: list[str] = [p for p in user_mpe_prompts_ids]
    user_mpe_attributes_ids_new: list[str] = [a for a in user_mpe_attributes_ids]
    user_mpe_image_ids: list[str] = user_doc_data.get("mpeImprovements", {}).get("images", [])

    print(user_mpe_attributes_ids_new)
    print(f"Gender {user_doc_data.get('gender') == 'Male'}")
    print(f"validate {config.validate}")
    print(single_selection)



    if config.validate:
        print(single_selection)
        if user_doc_data.get("gender") == "Male":
            valid = udh.moderate_content_leaner(single_selection)
            print(f"single selection: {single_selection} valid: {valid}")
            if not valid:
                return '{"id": "rejected", "title": "Your prompt may contain prohibited content. Revise it and try again.", "content": "Prohibited content includes sexual and explicit topics. These are reserved for conversation. Reach <NAME_EMAIL> if there was a mistake."}'
        elif (user_doc_data.get("gender") == "Female") and (not (user_doc_data.get("findom", False) == True)):
            try:
                findom = detect_findom_content(single_selection)
                if findom:
                    logging.info({f"Marking {uid} as findom"})
                    db.collection("findomEnable").add({"uid": uid})
            except Exception as e:
                logging.exception(e)



    user_sign_up_finished = user_doc_data.get("signUpFinished", False)
    user_waiting = user_doc_data.get("onWaitingList", False)
    user_in_verification = user_doc_data.get("verificationPendingSignUp", False)

    amplitude_sign_up_eligible = ((user_sign_up_finished == False) and (user_waiting == False) and (user_in_verification == False))
    postgres_eligible = (user_sign_up_finished == True)

    user_public_profile_reference = user_doc_data.get("publicProfile")

    print(attribute)

    if config.type == udh.ConfigType.SINGLE:
        udh.update_user_profile_dynamic(user_reference, user_public_profile_reference, attribute, single_selection, hidden)
        udh.send_sign_up_event_amplitude(config, single_selection, uid, amplitude_sign_up_eligible)

        try:
            if attribute == 'publicRole':
                udh.update_user_profile_dynamic(user_reference, user_public_profile_reference, 'position',
                                                single_selection,
                                                hidden)
        except Exception as e:
            logging.error(e)

        if postgres_eligible:
            try:
                user_doc_new = user_reference.get().to_dict()
                user_params_update = nm.getUpdateParameters(user_doc_new)

                update_user_extras_with_retry(db_conn_new, uid, user_params_update)
            except Exception as e:
                logging.exception(e)

        # automatically acquit users who are currently mbeBlocked for this attribute, but subject to these important conditions
        if attribute in user_mpe_attributes_ids_new:
            user_mpe_attributes_ids_new.remove(attribute)
        if user_mpe_attributes_ids != user_mpe_attributes_ids_new:
            user_update_dict = {"mpeImprovements.attributes": user_mpe_attributes_ids_new}
            mpe_blockers, mpe_blockers_content = mpe_helper.checkBlockers(user_doc, current_user_mpe_attributes=[])

            user_reference.update(user_update_dict)
            if not mpe_blockers.total:
                mpe_helper.process_profile_acceptance(uid)
            else:
                mpe_helper.process_profile_rejection(uid, mpe_blockers, mpe_blockers_content)


        return ''

    elif config.type == udh.ConfigType.MULTI:
        udh.update_user_profile_dynamic(user_reference, user_public_profile_reference, attribute, multi_selections, hidden)
        udh.send_sign_up_event_amplitude(config, multi_selections, uid, amplitude_sign_up_eligible)

        if postgres_eligible:
            try:
                user_doc_new = user_reference.get().to_dict()
                user_params_update = nm.getUpdateParameters(user_doc_new)

                update_user_extras_with_retry(db_conn_new, uid, user_params_update)
            except Exception as e:
                logging.exception(e)

        return ''

    elif config.type == udh.ConfigType.NUM:
        udh.update_user_profile_dynamic(user_reference, user_public_profile_reference, attribute, int(single_selection), hidden)
        udh.send_sign_up_event_amplitude(config, int(single_selection), uid, amplitude_sign_up_eligible)

        if postgres_eligible:
            try:
                user_doc_new = user_reference.get().to_dict()
                user_params_update = nm.getUpdateParameters(user_doc_new)

                update_user_extras_with_retry(db_conn_new, uid, user_params_update)
            except Exception as e:
                logging.exception(e)

        return ''

    elif config.type == udh.ConfigType.SPECIAL:

        if attribute and attribute == 'findomChoice':
            user_reference.update({
                'professionalsReq': not single_selection,
                'notificationsLatest.findomLatest': datetime.now().strftime('%Y%m%d')
            })

            udh.send_findom_event_amplitude(uid, single_selection)

            return ''

        if attribute == "prompts":
            udh.update_prompts(user_public_profile_reference, single_selection, prompt_number)
            udh.send_sign_up_event_amplitude(config, single_selection, uid, amplitude_sign_up_eligible)

            # automatically acquit users who are currently mbeBlocked for this attribute, but subject to these important conditions
            if raw_attribute in user_mpe_prompts_ids_new:
                user_mpe_prompts_ids_new.remove(raw_attribute)
            if user_mpe_prompts_ids != user_mpe_prompts_ids_new:
                user_update_dict = {"mpeImprovements.prompts": user_mpe_prompts_ids_new}
                mpe_blockers, mpe_blockers_content = mpe_helper.checkBlockers(user_doc, current_user_mpe_prompts=user_mpe_prompts_ids_new)

                user_reference.update(user_update_dict)
                if not mpe_blockers.total:
                    mpe_helper.process_profile_acceptance(uid)
                else:
                    mpe_helper.process_profile_rejection(uid, mpe_blockers, mpe_blockers_content)


            return ''

        elif attribute == "employer" or attribute == "job":
            combo_job_employer = udh.combine_job_employer(user_public_profile_reference, attribute, single_selection)
            udh.update_user_profile_dynamic(user_reference, user_public_profile_reference, "jobCombo", combo_job_employer,
                                        hidden)
            udh.update_user_profile_dynamic(user_reference, user_public_profile_reference, attribute, single_selection,
                                        hidden)

            udh.send_sign_up_event_amplitude(config, single_selection, uid, amplitude_sign_up_eligible)

            # automatically acquit users who are currently mbeBlocked for this attribute, but subject to these important conditions
            if attribute in user_mpe_attributes_ids_new:
                user_mpe_attributes_ids_new.remove(attribute)
            if user_mpe_attributes_ids != user_mpe_attributes_ids_new:
                user_update_dict = {"mpeImprovements.attributes": user_mpe_attributes_ids_new}
                mpe_blockers, mpe_blockers_content = mpe_helper.checkBlockers(user_doc,
                                                                              current_user_mpe_attributes=[])

                user_reference.update(user_update_dict)
                if not mpe_blockers.total:
                    mpe_helper.process_profile_acceptance(uid)
                else:
                    mpe_helper.process_profile_rejection(uid, mpe_blockers, mpe_blockers_content)

            return ''
        else:

            udh.update_user_profile_dynamic(user_reference, user_public_profile_reference, attribute, single_selection,
                                        hidden)

            udh.send_sign_up_event_amplitude(config, single_selection, uid, amplitude_sign_up_eligible)


            # automatically acquit users who are currently mbeBlocked for this attribute, but subject to these important conditions
            if attribute in user_mpe_attributes_ids_new:
                user_mpe_attributes_ids_new.remove(attribute)
                print(user_mpe_attributes_ids_new)
                for i, a in enumerate(user_mpe_attributes_ids_new):
                    if a == "specificGender":
                        user_mpe_attributes_ids_new[i] = "specific_gender"
                    if a == "specificRole":
                        user_mpe_attributes_ids_new[i] = "specific_role"
                print(user_mpe_attributes_ids_new)
                print(len(user_mpe_attributes_ids) != len(user_mpe_attributes_ids_new))

            if len(user_mpe_attributes_ids) != len(user_mpe_attributes_ids_new):
                user_update_dict = {"mpeImprovements.attributes": user_mpe_attributes_ids_new}

                mpe_blockers, mpe_blockers_content = mpe_helper.checkBlockers(user_doc, current_user_mpe_attributes=[])

                user_reference.update(user_update_dict)
                if not mpe_blockers.total:
                    mpe_helper.process_profile_acceptance(uid)
                else:
                    mpe_helper.process_profile_rejection(uid, mpe_blockers, mpe_blockers_content)


            return ''

    return ''


# UPDATE USER AGES: Once a day, updates the age of all users whose birthday it is
import calendar
import time
@scheduler_fn.on_schedule(
    schedule='1 0 * * *',
    timezone=scheduler_fn.Timezone("GMT"),
    timeout_sec=600,
    region="europe-west1",
    memory=options.MemoryOption.MB_512
)
def updateUserAges(event: scheduler_fn.ScheduledEvent) -> None:
    """
       Once a day, updates the age of all users whose birthday it is
       :param event: Due to scheduled trigger of cloud function, no practical relevance
       :return: None
    """
    db = firestore.client()
    user_collection = db.collection("users")

    global db_conn_new

    if db_conn_new == None:
        db_conn_new = connect_with_connector_persist()

    def calculateAge(birthDate):
        today = datetime.today()
        age = today.year - birthDate.year - ((today.month, today.day) < (birthDate.month, birthDate.day))
        return age

    # extracting month and day separately for querying purposes
    day_today = datetime.today().day
    month_today = datetime.today().month

    # made into a function to cater for 29th of February-borns
    def updating_function (day, month):
        query = user_collection \
            .where(filter=FieldFilter("day_born", "==", day)) \
            .where(filter=FieldFilter("month_born", "==", month))

        birthday_users = query.get()

        @with_retry()
        def update_age_with_retry(pool):
            if not (len(birthday_users) > 0):
                return None


            for b_user_doc in birthday_users:
                birthday = b_user_doc.get("birthday")
                old_age = b_user_doc.get("age")
                uid = b_user_doc.get("uid")

                new_age = calculateAge(birthday)

                b_user_ref = b_user_doc.reference
                b_user_profile_ref = b_user_doc.get("publicProfile")
                b_user_ref.update({"age": new_age})
                b_user_profile_ref.update({"age": new_age})

                b_user_uid = b_user_doc.get('uid')

                with pool.connect() as db_conn:

                    try:

                        try:
                            # update user in Postgres

                            stmt = sqlalchemy.text(
                                """SELECT update_user(p_user_id => :user_id, p_age => :age)""")

                            # Execute with the parameters
                            result = db_conn.execute(stmt, {
                                'user_id': b_user_uid,
                                'age': new_age}).fetchall()

                            db_conn.commit()

                        except Exception as e:
                            logging.exception(e)
                            db.collection('SQLExceptions').add(
                                {'function': 'updateAge -> user update', 'user': b_user_uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

                    except:
                        print("Fail Update Age")

                    try:
                        identify_obj = Identify()
                        identify_obj.set("Age", new_age)
                        amplitude_client.identify(identify_obj, EventOptions(user_id=uid))
                    except:
                        print("Amplitude failure")
                        continue

                time.sleep(1)

        update_age_with_retry(db_conn_new)

    updating_function(day_today, month_today)

    if (day_today == 1) and (month_today == 3) and (calendar.isleap(datetime.today().year) is False):
        updating_function(29, 2)

@scheduler_fn.on_schedule(
    schedule='*/30 * * * *',
    timezone=scheduler_fn.Timezone("GMT"),
    region="europe-west1",
    memory=options.MemoryOption.MB_512
)
def updateAmplitudeForConfigTests(event: Event[DocumentSnapshot]) -> None:
    from google.cloud import bigquery

    formatted_date = datetime.now().strftime("%Y%m%d")

    client = bigquery.Client()

    # Perform a query.
    QUERY = (
        f"""
        SELECT DISTINCT 
        device.vendor_id,
    FROM 
        `greenrocks-9abc3.analytics_429055103.events_intraday_{formatted_date}`, 
        UNNEST(user_properties) AS user_property
    WHERE 
        user_property.key = "firebase_exp_5"
        AND user_property.value.string_value = '1'
        AND event_timestamp > UNIX_MICROS(CURRENT_TIMESTAMP() - interval 30 minute)
        """)
    query_job = client.query(QUERY)  # API request
    rows = query_job.result()  # Waits for query to finish

    for row in rows:
        try:
        # Access the actual ID
            device_id = row[0]  # or row['advertising_id']

            identify_obj = Identify()
            identify_obj.set("Higher Like Limits Test 241221 Eligible", True)
            amplitude_client.identify(identify_obj, EventOptions(device_id=device_id))

            logging.info(f"Added eligibility info for {device_id}")
        except Exception as e:
            logging.exception(e)


from firebase_admin import auth
# UPON ADMIN BLOCKING USER: Revokes a user's auth login tokens as soon as added to blockedUsers collection by admin
@on_document_created(document="blockedUsers/{docId}", region="europe-west1", memory=options.MemoryOption.MB_512)
def uponAdminBlockingUser(event: Event[DocumentSnapshot]) -> None:
    """
       Revokes a user's auth login tokens as soon as added to blockedUsers collection by admin
       :param event: Includes blocked user's data
       :return: None
    """
    new_value = event.data.to_dict()

    uid = new_value.get("uid")

    # log user out completely
    # Revoke tokens on the backend.
    auth.revoke_refresh_tokens(uid)
    user = auth.get_user(uid)

    # Convert to seconds as the auth_time in the token claims is in seconds.
    revocation_second = user.tokens_valid_after_timestamp / 1000
    print('Tokens revoked at: {0}'.format(revocation_second))

    # set profile to paused & delete current matching suggestions
    db = firestore.client()
    users_collection = db.collection("users")
    users_doc = users_collection.document(uid)

    users_doc.update({"paused": True, "matchingSuggestions": firestore.DELETE_FIELD, "signUpFinished": False, "verificationPendingSignUp": False})

@scheduler_fn.on_schedule(
    schedule='0 6 * * *',
    timezone=scheduler_fn.Timezone("GMT"),
    region="europe-west1",
    memory=options.MemoryOption.GB_1
)
def unblockUsersScheduled():
    """
    Once a day at 6am GMT unblocks users who have a blockedUntil date on the current day
    """
    db = firestore.client()
    blocked_users_collection = db.collection('blockedUsers')
    users_collection = db.collection("users")

    # get the current day as a midnight datetime
    now = datetime.now()
    midnight = now.replace(hour=0, minute=0, second=0, microsecond=0)

    # get midnight the next day
    midnight_tomorrow = midnight + timedelta(days=1)

    logging.info(f"Calling function unblockUsersScheduled for day starting on {midnight.isoformat()}")

    try:
        # get all documents with blockedUntil values in the 24 hour period of the day
        query = blocked_users_collection.where(
            filter=FieldFilter("blockedUntil", ">", midnight)
        ).where(
            filter=FieldFilter("blockedUntil", "<", midnight_tomorrow)
        )

        results = query.get()

        # list to collect document IDs to delete after changes to user document
        doc_ids: list[str] = []

        for doc in results:
            # add id to doc_ids
            doc_ids.append(doc.id)

            # get UID for blocked user
            uid = doc.to_dict().get("uid")
            if uid is None:
                logging.warning(f"No UID found in blocked user document {doc.id}")
                continue
            
            # get user document and reverse fields that prevented matching
            users_doc = users_collection.document(uid)
            users_doc.update({
                "signUpFinished": True,
                "verificationPendingSignUp": True
            })

        # delete blocked users - done in separate loop to avoid undefined behaviour
        # while updating user documents
        if len(doc_ids) > 0:
            logging.info(f"Deleting blocked user documents for {len(doc_ids)} users")
            for doc_id in doc_ids:
                blocked_users_collection.document(doc_id).update({"unblocked": True})
        else:
            logging.info("No blocks removed for today")
    except Exception as e:
        logging.exception(f"Error accessing Firestore in unblockUsersScheduled: {e}")

# UPON REPORT: Ensures all likes reporting and reported users are unmatched upon a report being created
@on_document_created(document="reports/{docId}", region="europe-west1", memory=options.MemoryOption.MB_512)
def uponReport(event: Event[DocumentSnapshot]) -> None:
    """
       Ensures all likes reporting and reported users are unmatched upon a report being created
       :param event: Includes report document's data
       :return: None
    """
    new_value = event.data.to_dict()

    # get reported and reporting users
    reported_user = new_value.get('reportedUID')
    reporting_user = new_value.get('reportingUID')

    if reported_user is None or reporting_user is None:
        return

    # determine if there is any like between them
    db = firestore.client()
    likes_collection = db.collection('likes')

    likes_found = find_all_likes_of_two_users(reporting_user, reported_user, likes_collection)

    if len(likes_found) > 0:
        update_data = {"unmatched": True, "mutual": False}
        for like in likes_found:
            like.reference.update(update_data)
            filter_doc = db.collection('likeFilters').document(like.id).get()
            if filter_doc.exists:
                filter_doc.reference.update(update_data)

    try:

        if new_value.get('reason', '') == 'Unreported findom':
            findom_reports = db.collection('users').document(reported_user).get().to_dict().get('findomReports', 0)
            findom_reports += 1
            if findom_reports > 2:
                db.collection("findomEnable").add({"uid": reported_user})
                event.data.reference.update({'reason': 'Unreported findom (auto-handled)'})
            db.collection('users').document(reported_user).update(
                {'findomReports': firestore.firestore.Increment(1)})

    except Exception as e:

        logging.exception(e)

# UPDATE REFERRALS: Once a day, publishes to pub/sub data on eligible-to-be-rewarded referred users, to be ingested
# in referral project for attribution to referrer

# The structure is like this to transfer a strictly defined, limited, least necessary amount of data between two distinct
# cloud projects with differing levels of security and data protection

import json
def publish_json_data(project_id, topic_name, data):
  """Publishes JSON data to a Pub/Sub topic.

  Args:
    project_id: GCP project ID of the Pub/Sub topic.
    topic_name: Name of the Pub/Sub topic.
    data: A dictionary containing the JSON data to be published.
  """

  publisher = pubsub_v1.PublisherClient()
  topic_path = publisher.topic_path(project_id, topic_name)
  data_bytes = json.dumps(data).encode('utf-8')
  future = publisher.publish(topic_path, data=data_bytes)
  future.result()  # Wait for the message to be published
  print("successfully published topic")

from google.cloud import pubsub_v1
import phonenumbers
from phonenumbers import PhoneNumberFormat, parse
import hashlib
from collections import defaultdict
@scheduler_fn.on_schedule(
    schedule='0 4 * * *',
    timezone=scheduler_fn.Timezone("GMT"),
    region="europe-west2",
    memory=options.MemoryOption.GB_1

)
def updateReferrals():
    """
       Once a day, publishes to pub/sub data on eligible-to-be-rewarded referred users, to be ingested in referral project for attribution to referrer
       :param: None
       :return: None
    """

    # functiton to standardize phone numbers to international format using phonenumbers lib
    def standardize_phone_number(phone_number):
        try:
            # Parse the phone number
            parsed_number = phonenumbers.parse(phone_number, None)
            # Ensure the number is valid
            if not phonenumbers.is_valid_number(parsed_number):
                print("Not valid phone number")
                return None
            # Format the number to E.164 international format
            standardized_number = phonenumbers.format_number(parsed_number, PhoneNumberFormat.E164)
            return standardized_number

        except phonenumbers.phonenumberutil.NumberParseException:
            return None

    # hashes phone number - this is because we want to detect fraud eternally without storing phone numbers beyond the account's lifespan
    def hash_phone_number(phone_number):
        # Convert the phone number to bytes (UTF-8 encoding)
        phone_number_bytes = phone_number.encode('utf-8')
        # Hash the bytes using SHA-256
        hashed_phone_number = hashlib.sha256(phone_number_bytes).hexdigest()
        return hashed_phone_number


    db = firestore.client()
    users_collection = db.collection('users')
    permanent_referrals_collection = db.collection("permanentReferrals")

    referrer_dict = defaultdict(int)

    today = datetime.today()
    five_days_ago = datetime.now() - timedelta(5)
    seven_days_ago = datetime.now() - timedelta(7)

    # gets all eligible accounts for referral
    eligible_referral_query = (users_collection.where(filter=FieldFilter("refCode", "!=", "default"))
                               .where(filter=FieldFilter("gender", "==", "Female"))
                               .where(filter=FieldFilter("signUpFinished", "==", True))
                               .where(filter=FieldFilter("accountCreationFinished", "<", five_days_ago))
                               .where(filter=FieldFilter("accountCreationFinished", ">", seven_days_ago))).get()

    # for every eligible account, ensure using hashes of authentication provider (email or phone) that is not duplicate
    # then, add to export list & update user document to signify that it has been attributed
    for w in eligible_referral_query:
        w_doc = w.to_dict()
        w_phone_number = w_doc.get("phone_number")
        w_email = w_doc.get("email")

        if w_phone_number != None:
            w_ref_code = w_doc.get("refCode")
            w_already_attributed = w_doc.get("referralAttributed")

            if w_already_attributed != True:
                try:
                    w_standard_phone_number = standardize_phone_number(w_phone_number)
                    w_phone_hash = hash_phone_number(w_standard_phone_number)

                    w_perm_doc = permanent_referrals_collection.document(w_phone_hash).get()

                    if w_perm_doc.exists:
                        w_perm_doc.reference.update({"alternative_uids": firestore.ArrayUnion([{"id": w.id, "date_added": today, "ref_code": w_ref_code}])})
                    else:
                        w_perm_doc.reference.set({"phone_number_hash": w_phone_hash, "ref_code": w_ref_code, "original_uid": w.id, "timestamp_added": firestore.SERVER_TIMESTAMP, "date_added": today})
                        referrer_dict[w_ref_code] += 1

                    w.reference.update({"referralAttributed": True})

                except Exception as e:
                    logging.error("An error occurred: %s", e)
                    continue
        else:
            try:
                w_email_hash = hash_phone_number(w_email)

                w_perm_doc = permanent_referrals_collection.document(w_email_hash).get()

                if w_perm_doc.exists:
                    w_perm_doc.reference.update({"alternative_uids": firestore.ArrayUnion(
                        [{"id": w.id, "date_added": today, "ref_code": w_ref_code}])})
                else:
                    w_perm_doc.reference.set(
                        {"phone_number_hash": w_email_hash, "ref_code": w_ref_code, "original_uid": w.id,
                         "timestamp_added": firestore.SERVER_TIMESTAMP, "date_added": today, "is_email_based": True})
                    referrer_dict[w_ref_code] += 1

                w.reference.update({"referralAttributed": True})

            except Exception as e:
                logging.error("An error occurred: %s", e)
                continue

    export_dict = dict(referrer_dict)
    publish_json_data('chyrpe-referral', 'daily_user_referral-sub', export_dict)


@on_document_created(document="verificationBan/{docId}", region="europe-west1", memory=options.MemoryOption.GB_2)
def verificationBanUser(event: Event[DocumentSnapshot]) -> None:
    new_value = event.data.to_dict()

    # get reported and reporting users
    uid = new_value.get('uid')

    db = firestore.client()

    user = db.collection("users").document(uid).get().to_dict()

    db.collection("blockedUsers").add({
        "blockedUntil": datetime.now() + timedelta(days=36500),
        "email": user.get("email"),
        "phoneNumber": user.get("phone_number"),
        "uid": uid
    })

    logging.info("Securing User Data")

    secureUserDataCore(uid)

    logging.info("Deleting User Account")

    deleteAdminCore(uid)

from common_like import get_like_filter_data


@https_fn.on_call(
    region="europe-west1",
    concurrency=20,
    memory=options.MemoryOption.GB_2,
)
def applyFiltersToLikes(req: https_fn.CallableRequest) -> Any:
    if req.auth.uid == None:
        return "User not authenticated"

    uid = req.auth.uid
    db = firestore.client()
    user_doc = db.collection("users").document(uid)
    user_data = user_doc.get().to_dict()

    likes_collection = (
        db.collection("likes")
        .where(filter=FieldFilter("likedUser", "==", user_doc))
        .where(filter=FieldFilter("mutual", "==", False))
        .where(filter=FieldFilter("unmatched", "==", False))
        .order_by("timestamp", direction=firestore.Query.DESCENDING)
    ).limit(50).get()

    batch = db.batch()
    write_count = 0
    while likes_collection:
        for l in likes_collection:
            
            start = time.time()
            logging.info(f"Processing like {l.id} for user {uid}")

            like_data = l.to_dict()
            liking_user = like_data["likingUser"].get()
            liking_user_data = liking_user.to_dict()

            filter_data = get_like_filter_data(user_data, liking_user_data)
            filter_data.update(
                {
                    "like": l.reference,
                    "likedUser": user_doc,
                    "mutual": False,
                    "unmatched": False,
                    "timestamp": like_data.get("timestamp"),
                }
            )

            if filter_data:
                filter_doc = db.collection("likeFilters").document(l.id)
                batch.set(filter_doc, filter_data)

            write_count += 1
            if write_count > 4:
                batch.commit()
                batch = db.batch()
                write_count = 0

            end = time.time()
            logging.info(f"Processed like {l.id} for user {uid} in {end - start} seconds")

        last_doc = likes_collection[-1]

        likes_collection = (
        db.collection("likes")
        .where(filter=FieldFilter("likedUser", "==", user_doc))
        .where(filter=FieldFilter("mutual", "==", False))
        .where(filter=FieldFilter("unmatched", "==", False))
        .order_by("timestamp", direction=firestore.Query.DESCENDING)
        .start_after(last_doc)
    ).limit(50).get()
             
    batch.commit()

    user_doc.update({
        "lastEvolvedTabLikesTabLikesFilteration": firestore.SERVER_TIMESTAMP
    })


def detect_findom_content(scannable_content):
    findom = False

    from pydantic import BaseModel
    from openai import AzureOpenAI, BadRequestError

    client = AzureOpenAI(
        azure_endpoint="https://chyrpegptp.openai.azure.com/",
        api_key=access_secret_version("OpenAI_Key"),
        api_version="2024-10-21"
    )

    class LanguageEvaluation(BaseModel):
        input_text: str
        evaluation_result: int

    try:
        # Call the GPT model with the schema
        completion = client.beta.chat.completions.parse(
            model="gpt-4o-mini",  # replace with the actual model deployment name
            messages=[
                {"role": "system",
                 "content": """Act as a findom & professional detector that strictly and only outputs: 1 for true it is a findom/professional. or: 0 for false it is not a findom/professional. or: 2 for unsure / edgecase. A findom is somebody that wants to financially dominate another, we need to detect these people. A professional is someone trying to sell a service/good or a professional dominatrix, etc. Look for signs in this piece of text that this person is a findom/professional. Just because someone calls herself domme, dominatrix or dominant does not mean she is a professional, she is an edge case then. If she uses the prefix "pro" or "professional" in conjunction with any of the terms domme, dominatrix, dominant, she is to be classes as professional. Calling herself a findom (or any derivative of it) is a clear sign to class her as professional."""},
                {"role": "user", "content": scannable_content}
            ],
            response_format=LanguageEvaluation,  # Using the schema here
        )

        # Parse and print the result
        evaluation = completion.choices[0].message.to_dict().get("parsed").get("evaluation_result")

        logging.info(evaluation)

        if evaluation == 0 or evaluation == 2:
            findom = False
        else:
            findom = True

    except Exception as e:
        logging.error(e)
        findom = False

    return findom
