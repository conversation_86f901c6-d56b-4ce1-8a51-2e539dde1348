from dataclasses import dataclass
from typing import Dict
from enum import Enum
import json

# Imports in alphabetical order

from firebase_admin import initialize_app
from amplitude import Amplitude, BaseEvent, Identify, EventOptions
from firebase_functions.firestore_fn import (
  DocumentSnapshot,
)
from google.cloud import secretmanager, firestore
from firebase_admin import firestore
from google.cloud.firestore_v1 import FieldFilter, Or
import google.cloud.logging
from google.cloud.sql.connector import Connector
import logging
import pg8000
import requests
from requests.adapters import HTTPAdapter, Retry
from openai import BadRequestError
import sqlalchemy

# initializing log client
log_client = google.cloud.logging.Client()
log_client.setup_logging()

# defining common methods in global scope
def access_secret_version(secret_name):
    '''Access a secret from the project's secret manager'''
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/greenrocks-9abc3/secrets/{secret_name}/versions/latest"
    response = client.access_secret_version(name=name)
    return response.payload.data.decode('UTF-8')

def create_retry_session() -> requests.Session:
    session = requests.Session()
    retries = Retry(total=5, backoff_factor=0.1, status_forcelist=[429, 500, 502, 503, 504])
    session.mount('https://', HTTPAdapter(max_retries=retries))
    return session

# defining common variables
amplitude_client = Amplitude("65dbac83dec4e06df231ca34a83e4757")

from dataclasses import dataclass
from typing import Dict
from enum import Enum
import json

class ConfigType(Enum):
    NUM = "num"
    SINGLE = "single"
    MULTI = "multi"
    SPECIAL = "special"

@dataclass
class UserConfig:
    type: ConfigType
    validate: bool
    postgres: bool
    amplitude_title: str
    amplitude_signup: bool

db_user_config: Dict[str, UserConfig] = {
    "height": UserConfig(type=ConfigType.NUM, validate=False, postgres=True, amplitude_title="Height", amplitude_signup=True),
    "publicRole": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Role",  amplitude_signup=False),
    "experienceLevel": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Experience Level",  amplitude_signup=True),
    "relationPreferences": UserConfig(type=ConfigType.MULTI, validate=False, postgres=True, amplitude_title="Relationship Preferences", amplitude_signup=True),
    "ethnicity": UserConfig(type=ConfigType.MULTI, validate=False, postgres=True, amplitude_title="Ethnicity", amplitude_signup=True),
    "children": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Children", amplitude_signup=True),
    "familyPlans": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Family Plans", amplitude_signup=True),
    "job": UserConfig(type=ConfigType.SPECIAL, validate=True, postgres=False, amplitude_title="Job", amplitude_signup=True),
    "employer": UserConfig(type=ConfigType.SPECIAL, validate=True, postgres=False, amplitude_title="Employer", amplitude_signup=True),
    "education": UserConfig(type=ConfigType.SPECIAL, validate=True, postgres=False, amplitude_title="School", amplitude_signup=True),
    "eduLevel": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Education Level", amplitude_signup=True),
    "religion": UserConfig(type=ConfigType.MULTI, validate=False, postgres=True, amplitude_title="Religion", amplitude_signup=True),
    "politics": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Politics", amplitude_signup=True),
    "drinking": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Drinking",amplitude_signup=True),
    "smoking": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=True, amplitude_title="Smoking", amplitude_signup=True),
    "workout": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=False, amplitude_title="Workout", amplitude_signup=True),
    "specificRole": UserConfig(type=ConfigType.SPECIAL, validate=True, postgres=False, amplitude_title="Specific Role", amplitude_signup=True),
    "specificGender": UserConfig(type=ConfigType.SPECIAL, validate=True, postgres=False, amplitude_title="Specific Gender", amplitude_signup=True),
    "personality": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=False, amplitude_title="Personality", amplitude_signup=True),
    "monoPolyRelationship": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=False, amplitude_title="Mono/Poly Relationship", amplitude_signup=True),
    "diet": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=False, amplitude_title="Diet", amplitude_signup=True),
    "zodiac": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=False, amplitude_title="Zodiac", amplitude_signup=True),
    "languages": UserConfig(type=ConfigType.MULTI, validate=False, postgres=False, amplitude_title="Languages",amplitude_signup=True),
    "pets": UserConfig(type=ConfigType.SINGLE, validate=False, postgres=False, amplitude_title="Pets", amplitude_signup=True),
    "hobbies": UserConfig(type=ConfigType.MULTI, validate=False, postgres=False, amplitude_title="Hobbies", amplitude_signup=True),
    "findomChoice": UserConfig(type=ConfigType.SPECIAL, validate=False, postgres=False, amplitude_title="Findom Choice", amplitude_signup=True),
    "kinks": UserConfig(type=ConfigType.MULTI, validate=False, postgres=False, amplitude_title="Kinks", amplitude_signup=True),
    "bio": UserConfig(type=ConfigType.SPECIAL, validate=True, postgres=False, amplitude_title="Bio", amplitude_signup=False),
    "prompts": UserConfig(type=ConfigType.SPECIAL, validate=True, postgres=False, amplitude_title="Prompts", amplitude_signup=False),
}

def update_user_profile_dynamic(user_ref, public_ref, attribute, selection, hidden):
    user_ref.update({attribute: selection, f"{attribute}Hidden": hidden})
    if hidden:
        public_ref.update({attribute: firestore.firestore.DELETE_FIELD})
    else:
        public_ref.update({attribute: selection})

    return ''

def update_prompts(public_ref, selection, number):
    public_profile_data = public_ref.get().to_dict()

    user_prompts = public_profile_data.get("prompts", [])

    prompts_data = json.loads(selection)

    if len(user_prompts) > number:
        user_prompts[number] = prompts_data
    else:
        user_prompts.append(prompts_data)

    public_ref.update({"prompts": user_prompts})

def combine_job_employer(public_ref, attribute, selection):
    public_profile_data = public_ref.get().to_dict()

    user_employer = public_profile_data.get("employer", '')
    user_job = public_profile_data.get("job", '')

    if attribute == "employer":
        user_employer = selection
    else:
        user_job = selection

    if (user_job != '') and (user_employer != ''):
        return f'{user_job} at {user_employer}'
    elif user_job != '':
        return user_job
    elif user_employer != '':
        return user_employer
    else:
        return ''


def send_findom_event_amplitude(uid, choice):

    identify_obj1 = Identify()
    identify_obj1.set("Findom Choice", choice)

    event1 = BaseEvent(event_type=f"Sign Up: Entered Findom Choice", user_id=uid, event_properties={
        "Findom Choice": choice
    }, user_properties={
        "Findom Choice": choice
    })

    amplitude_client.track(event1)

    return

def send_sign_up_event_amplitude(config: UserConfig, value, uid, not_signed_up):

    event_name = config.amplitude_title
    event_sendable = config.amplitude_signup

    if not_signed_up and event_sendable:

        if config.type != ConfigType.SPECIAL:
            identify_obj1 = Identify()
            identify_obj1.set(event_name, value)
            event1 = BaseEvent(event_type=f"Sign Up: Entered {event_name}", user_id=uid, event_properties={
                event_name: value
            })
        else:
            event1 = BaseEvent(event_type=f"Sign Up: Entered {event_name}", user_id=uid)

        amplitude_client.track(event1)

    elif event_sendable:

        if config.type != ConfigType.SPECIAL:
            identify_obj1 = Identify()
            identify_obj1.set(event_name, value)
            event1 = BaseEvent(event_type=f"Edit Profile: Entered {event_name}", user_id=uid, event_properties={
                event_name: value
            })
        else:
            event1 = BaseEvent(event_type=f"Edit Profile: Entered {event_name}", user_id=uid)

        amplitude_client.track(event1)

def moderate_content_leaner(scannable_content):
    valid = True

    from pydantic import BaseModel
    from openai import AzureOpenAI, BadRequestError

    client = AzureOpenAI(
        azure_endpoint="https://chyrpegptp.openai.azure.com/",
        api_key=access_secret_version("OpenAI_Key"),
        api_version="2024-10-21"
    )

    class LanguageEvaluation(BaseModel):
        input_text: str
        evaluation_result: int

    try:
        # Call the GPT model with the schema
        completion = client.beta.chat.completions.parse(
            model="gpt-4o-mini",  # replace with the actual model deployment name
            messages=[
                {"role": "system", "content": """You are a content classification assistant. Your task is to classify a given piece of text (a user’s bio) into one of three categories based on explicitness. You must strictly follow these rules:

- Output must be exactly one digit: 0, 1, or 2.
- Do not include any additional text or explanation beyond the digit.
- Use the following guidelines to determine the correct digit:
    0 = Not explicit (safe, no questionable content).
    1 = Explicit (clearly sexual, violent, or otherwise disallowed content).
    2 = Borderline or unclear (hints of explicitness, ambiguity, or if you are unsure).

If the bio is obviously explicit, output 1.
If it is obviously not explicit, output 0.
If it is ambiguous or borderline, output 2.

Be especially strict about finding kinky / BDSM / femdom content. Terms like 'mommy', 'dominant woman', 'submissive' if not used in a clearly harmless, non-kinky context, should be flagged as explicit.

Immediate qualifiers for 1 are terms: dom, sub, femdom, submissive, dominant, chastity, kink, kinky, slave, master, mistress, degradation, humiliation, sissy, sissification, brat, brat-tamer, collar, collared, leash, leashing, punishment, punished, pain slut, domme, switch, bondage, sadist, masochist, sadomasochism, S&M, TPE, CNC, orgasm denial, findom, pegging, bootlicker, discipline, protocol, degradation, petplay, good boy, mistress, dominatrix, paypig
More immediate qualifiers for 1 are the emojis: 🔒, 🔐, 🥚, 🐾, 🐕, 🐶, 🐱, 👠, 🖤, 👑, 🔗, ⛓️, 🍑, 🍆, 😈, 👅, 🧼

Classify the following bio into the categories above and output only the appropriate digit:"""},
                {"role": "user", "content": scannable_content}
            ],
            response_format=LanguageEvaluation,  # Using the schema here
        )

        # Parse and print the result
        evaluation = completion.choices[0].message.to_dict().get("parsed").get("evaluation_result")

        print(f"evaluation of {scannable_content}, evaluation: {evaluation}")

        if evaluation == 0 or evaluation == 2:
            valid = True
        else:
            valid = False

    except Exception as e:
        if "ContentFilterFinishReasonError" in str(e) or "400" in str(e):
            valid = False
        else:
            valid = True

        logging.exception(e)

    return valid