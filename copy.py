import os
import shutil


files_to_copy = {
    "like.py": [
        "user",
        "matching",
    ],
    "utils.py": [
        "user",
        "matching",
    ],
    "translations.py": [

        "purchase",
        "admin",
        "analytics",
        "forum",
        "matching",
        "user",
        "verification",
        "waitinglist",

    ],
}

def copy_file(file, target_dir):
    file_path = os.path.join("common", file)
    shutil.copy(file_path, target_dir)

    # Add a comment to the top of the file
    with open(os.path.join(target_dir, file), "r") as f:
        lines = f.readlines()
    with open(os.path.join(target_dir, file), "w") as f:
        f.write("# DONT MODIFY (Copied code from ../common/" + file + ")\n")
        f.writelines(lines)
    os.rename(os.path.join(target_dir, file), os.path.join(target_dir, f"common_{file}"))

if __name__ == "__main__":
    for file, target_dirs in files_to_copy.items():
        print(f"Copying {file} to {target_dirs}")
        for target_dir in target_dirs:
            copy_file(file, target_dir)
