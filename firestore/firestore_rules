rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    match /countdown/{document} {
      allow create: if false;
      allow read: if request.auth != null;
      allow write: if false;
      allow delete: if false;
    }
    
    match /VerificationsSettings/{document} {
      allow create: if false;
      allow read: if request.auth != null;
      allow write: if false;
      allow delete: if false;
    }
    
    match /dynamicAppRatingNo/{document} {
      allow create: if request.auth != null;
      allow read: if false;
      allow update: if false;
      allow delete: if false;
    }
    
    match /likeLimitsConfig/{document} {
      allow create: if false;
      allow read: if true;
      allow update: if false;
      allow delete: if false;
    }
    
    match /openRegions/{document} {
      allow create: if false;
      allow read: if request.auth != null;
      allow write: if false;
      allow delete: if false;
    }
    
    match /forums/{document} {
      allow create: if false;
      allow read: if request.auth != null;
      allow write: if false;
      allow delete: if false;
    }
    
    match /forumMessageReports/{document} {
      allow create: if request.auth != null;
      allow read: if false;
      allow write: if false;
      allow delete: if false;
    }

    match /forums/{parent}/forumMessages/{document} {
      allow create: if request.auth != null;
      allow read: if request.auth != null;
      allow update: if false;
      allow delete: if false;
    }
    
    match /kinks/{document} {
      allow create: if false;
      allow read: if request.auth != null;
      allow write: if false;
      allow delete: if false;
    }
    
    match /waitingListFaq/{document} {
      allow create: if false;
      allow read: if request.auth != null;
      allow write: if false;
      allow delete: if false;
    }
    
    match /globalWlAvailability/{document} {
      allow create: if false;
      allow read: if request.auth != null;
      allow write: if false;
      allow delete: if false;
    }
    
    match /wlAvailability/{document} {
      allow create: if false;
      allow read: if request.auth != null;
      allow write: if false;
      allow delete: if false;
    }

    match /aKey/{document} {
      allow create: if false;
      allow read: if request.auth != null;
      allow write: if false;
      allow delete: if false;
    }
    
    match /broadcastMessages/{document} {
      allow create: if false;
      allow read: if request.auth != null;
      allow write: if false;
      allow delete: if false;
    }

    match /ff_user_push_notifications/{document} {
      allow create: if false;
      allow read: if false;
      allow write: if false;
      allow delete: if false;
    }

    match /users/{parent}/images/{document} {
      allow create: if request.auth.uid == parent;
      allow get: if request.auth != null;
      allow list: if request.auth == parent;
      allow write: if request.auth.uid == parent;
      allow delete: if false;
    }

    match /{path=**}/images/{document} {
      allow get: if request.auth != null;
      allow list: if request.auth == parent;
    }


    match /likes/{document} {
      allow create: if request.auth != null && request.resource.data.mutual == false;
      allow read: if (
  ('involedUIDs' in resource.data.keys() && request.auth.uid in resource.data.involedUIDs) ||
  ('involvedUsers' in resource.data.keys() && 
    /databases/$(database)/documents/users/$(request.auth.uid) in resource.data.involvedUsers) ||
  ('recentMessageNotSeenBy' in resource.data.keys() && 
    /databases/$(database)/documents/users/$(request.auth.uid) in resource.data.recentMessageNotSeenBy) ||
  ('likedUser' in resource.data.keys() && 
    /databases/$(database)/documents/users/$(request.auth.uid) == resource.data.likedUser) ||
  ('likingUser' in resource.data.keys() && 
    /databases/$(database)/documents/users/$(request.auth.uid) == resource.data.likingUser) ||
  (
    'involvedUsersProfileRefOnly' in resource.data.keys() && 
    get(/databases/$(database)/documents/users/$(request.auth.uid)).data.publicProfile in resource.data.involvedUsersProfileRefOnly
  )
);
      allow write: if request.auth.uid in resource.data.involedUIDs;
      allow delete: if request.auth.uid in resource.data.involedUIDs;
    }

    match /matchesForDeletion/{document} {
      allow create: if request.auth != null;
      allow read: if false;
      allow write: if false;
      allow delete: if false;
    }

    match /likes/{parent}/messages/{document} {
      allow create: if /databases/$(database)/documents/users/$(request.auth.uid) in get(/databases/$(database)/documents/likes/$(parent)).data.involvedUsers;
      allow read: if /databases/$(database)/documents/users/$(request.auth.uid) in get(/databases/$(database)/documents/likes/$(parent)).data.involvedUsers;
      allow write: if false;
      allow delete: if false;
    }

    match /notifications/{document} {
      allow create: if request.auth != null;
      allow read: if /databases/$(database)/documents/users/$(request.auth.uid) in resource.data.receivers;
      allow write: if false;
      allow delete: if false;
    }

    match /optionsForSelectors/{document} {
      allow create: if false;
      allow read: if request.auth != null;
      allow write: if false;
      allow delete: if false;
    }

    match /users/{parent}/publicProfile/{document} {
      allow create: if request.auth.uid == parent;
      allow get: if request.auth != null;
      allow list: if request.auth.uid == parent;
      allow write: if request.auth.uid == parent;
      allow delete: if false;
    }

    match /{path=**}/publicProfile/{document} {
      allow get: if request.auth != null;
      allow list: if request.auth.uid == parent;
    }


    match /reports/{document} {
      allow create: if request.auth != null;
      allow read: if false;
      allow write: if false;
      allow delete: if false;
    }

    match /users/{parent}/searchSettings/{document} {
      allow create: if false;
      allow read: if request.auth.uid == parent;
      allow write: if request.auth.uid == parent;
      allow delete: if false;
    }

    match /users/{parent}/suggestedMatches/{document} {
      allow create: if false;
      allow read: if request.auth.uid == parent;
      allow write: if request.auth.uid == parent;
      allow delete: if false;
    }

    match /users/{document} {
      allow create: if request.auth.uid == document;
      allow read: if request.auth.uid == document;
      allow update: if request.auth.uid == document && (!request.resource.data.diff(resource.data).affectedKeys()
        .hasAny(['evolvedSubscriber', 'plusSubscriber', 'goldSubscriber', 'uid', 'signUpFinished', 'verificationFailed', 'verificationFailReason', 'onWaitingList', 'waitingListInterval', 'chyrpeStandardSubscriber', 'verified', 'normalLikeDailyLimit', 'evolvedLikesWeeklyLimit']));
      allow delete: if false;
    }

    match /verifications/{document} {
      allow create: if request.auth != null;
      allow read: if resource.data.userToVerify == /databases/$(database)/documents/users/$(request.auth.uid);
      allow write: if resource.data.userToVerify == /databases/$(database)/documents/users/$(request.auth.uid);
      allow delete: if false;
    }

}
}