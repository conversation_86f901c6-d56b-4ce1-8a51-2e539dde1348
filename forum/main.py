# Imports in alphabetical order
from firebase_admin import initialize_app
from firebase_functions import https_fn, options
from google.cloud import firestore
from firebase_admin import firestore
import google.cloud.logging
import logging
from typing import Any
from common_translations import get_translated_text
# initializing log client
log_client = google.cloud.logging.Client()
log_client.setup_logging()

# defining common methods in global scope
def sendPushNotification(uid, title, text, push_notifications_collection):
    push_notifications_collection.add({"notification_title": title, "notification_text": text,
                                       "user_refs": f'/users/{uid}',
                                       "timestamp": firestore.SERVER_TIMESTAMP})

### Actual cloud functions

initialize_app()

# ACTIVATE PUSH NOTIFICATIONS FOR FORUM: Securely adds user to array of users who receive push notifications about new messages in a given forum
@https_fn.on_call(region="europe-west2", enforce_app_check=True)
def activatePushNotificationsForForum(req: https_fn.CallableRequest) -> Any:
    """
            Securely adds user to array of users who receive push notifications about new messages in a given forum
            :param req: Callable request incl. user auth information - extra param: forumID
            :return: None
    """

    if req.auth.uid == None:
        return "User not authenticated"
    uid = req.auth.uid

    forum_id = req.data['forumID']

    db = firestore.client()
    forums_collection = db.collection('forums')

    # adding metadata to forum ID doc
    forums_collection.document(forum_id).update(
        {'pushNotificationRecipientUIDs': firestore.ArrayUnion([uid])})

# DEACTIVATE PUSH NOTIFICATIONS FOR FORUM: Securely removes user from array of users who receive push notifications about new messages in a given forum
@https_fn.on_call(region="europe-west2", enforce_app_check=True)
def deactivatePushNotificationsForForum(req: https_fn.CallableRequest) -> Any:
    """
                Securely removes user from array of users who receive push notifications about new messages in a given forum
                :param req: Callable request incl. user auth information - extra param: forumID
                :return: None
    """
    if req.auth.uid == None:
        return "User not authenticated"
    uid = req.auth.uid

    forum_id = req.data['forumID']

    db = firestore.client()
    forums_collection = db.collection('forums')

    # adding metadata to forum ID doc
    forums_collection.document(forum_id).update(
        {'pushNotificationRecipientUIDs': firestore.ArrayRemove([uid])})

# SEND FORUM MESSAGE: Securely sends push notifications to users who have opted to receive them for a given forum
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.MB_512, enforce_app_check=True)
def sendForumMessage(req: https_fn.CallableRequest) -> Any:
    """
                Securely sends push notifications to users who have opted to receive them for a given forum
                :param req: Callable request incl. user auth information - extra param: forumID, message (string)
                :return: None
    """
    if req.auth.uid == None:
        return "User not authenticated"
    uid = req.auth.uid

    forum_id = req.data['forumID']
    message = req.data['message']

    # shortens message for preview if it is long
    short_message = f'{message[:25]}...' if len(message) >= 25 else message

    db = firestore.client()
    forums_collection = db.collection('forums')
    user = db.collection("users").document(uid).get().to_dict()
    user_lang = user.get("language", "en")

    # adding metadata to forum ID doc
    forums_collection.document(forum_id).update({'latestMessageSentTime': firestore.SERVER_TIMESTAMP, 'latestMessageSentPreview': short_message})

    # notifying all members who want to be notified
    forums_dict = forums_collection.document(forum_id).get().to_dict()
    forum_name = forums_dict.get("name")
    push_notification_recipients = forums_dict.get("pushNotificationRecipientUIDs", [])

    push_notifications_collection = db.collection("ff_user_push_notifications")

    for p in push_notification_recipients:
        sendPushNotification(p, f'{get_translated_text("New message in", user_lang)} {forum_name} {get_translated_text("on", user_lang)} Chyrpe', f'{get_translated_text("Open the app to check it out", user_lang)} {forum_name} {get_translated_text("on", user_lang)} Chyrpe', push_notifications_collection)

