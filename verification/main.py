# Imports in alphabetical order
import functools
import firebase_functions.firestore_fn
from amplitude import Amplitude, BaseEvent, Identify, EventOptions
from datetime import datetime, timedelta
from firebase_admin import initialize_app
from firebase_functions import https_fn, identity_fn, options, scheduler_fn
from firebase_functions.firestore_fn import (
    on_document_created,
    on_document_deleted,
    on_document_updated,
    on_document_written,
    Event,
    Change,
    DocumentSnapshot,
)
import google.auth
from google.cloud import secretmanager, firestore
from google.cloud.firestore import DocumentReference
from firebase_admin import firestore
from firebase_admin import db as rtdb
from google.cloud.firestore_v1 import FieldFilter, Or, And
import google.cloud.logging
from google.cloud.sql.connector import Connector, IPTypes
import logging
import pg8000
import random
import requests
from requests.adapters import HTTPAdapter, Retry
import sqlalchemy
from typing import Any, Dict
import nmatching as nm
import mpe_helper as mpe_helper
import mpe_user_watcher
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import time
from common_translations import get_translated_text

# initializing log client
log_client = google.cloud.logging.Client()
log_client.setup_logging()

### Actual cloud functions

initialize_app()

def is_dev_environment():
    """
    Check if running in dev environment using default credentials
    """
    _, project_id = google.auth.default()
    return project_id == "greenrocks-dev"


dev_environment = is_dev_environment()
# defining common methods in global scope
amplitude_client = Amplitude("********************************") if not dev_environment else Amplitude("********************************")

db_conn_new = None
def connect_with_connector_persist():
    # initialize Cloud SQL Python Connector object
    connector = Connector(refresh_strategy="LAZY")

    def getconn() -> pg8000.dbapi.Connection:
        conn: pg8000.dbapi.Connection = connector.connect(
            "greenrocks-9abc3:europe-west2:chyrpe-prod1",
            "pg8000",
            user="<EMAIL>",
            db="chyrpe_main_db",
            enable_iam_auth=True,
            ip_type=IPTypes.PUBLIC
        )
        return conn

    # The Cloud SQL Python Connector can be used with SQLAlchemy
    # using the 'creator' argument to 'create_engine'
    pool = sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        # [START_EXCLUDE]
        # Pool size is the maximum number of permanent connections to keep.
        pool_size=10,
        # Temporarily exceeds the set pool_size if no connections are available.
        max_overflow=2,
        # The total number of concurrent connections for your application will be
        # a total of pool_size and max_overflow.
        # 'pool_timeout' is the maximum number of seconds to wait when retrieving a
        # new connection from the pool. After the specified amount of time, an
        # exception will be thrown.
        pool_timeout=30,  # 30 seconds
        # 'pool_recycle' is the maximum number of seconds a connection can persist.
        # Connections that live longer than the specified amount of time will be
        # re-established
        pool_recycle=1800,  # 30 minutes
        # [END_EXCLUDE]
    )
    return pool

def send_ampli_event(name, uid, params=None):
    """Sends an event to Amplitude with optional parameters"""

    try:

        event = BaseEvent(event_type=name, user_id=uid,
                          event_properties=params)
        amplitude_client.track(event)

    except Exception as e:
        logging.exception(e)


def set_ampli_user_property(property, value, uid):
    """Sets a new user property for given user in Amplitude"""

    try:

        identify_obj = Identify()
        identify_obj.set(property, value)
        amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

    except Exception as e:
        logging.exception(e)


def track_and_notify_verification_successful(uid, manual):
    set_ampli_user_property('Verified', True, uid)
    send_ampli_event('Verification: Successful', uid, {'Manual': manual})
    db = firestore.client()
    user_data = db.collection("users").document(uid).get().to_dict()
    language = user_data.get('language', 'en')

    sendPushNotification(uid, "Chyrpe",
                         get_translated_text("Congratulations, you've been verified successfully!", language))

    return


def track_and_notify_verification_failed(uid, manual, reason, outcome, language):
    if manual:
        set_ampli_user_property('Manual Verification', True, uid)

    send_ampli_event('Verification: Failed', uid,
                     {'Manual': manual, 'Reason': reason, 'Outcome': outcome})
    


    sendPushNotification(uid, get_translated_text("Your verification was reviewed", language), get_translated_text("Please open the Chyrpe app to see details.", language))

    return

def track_manual_review_verification_requested(uid, reason=None):

    if reason is not None:
        send_ampli_event('User Requests Manual Review', uid, {'Reason': reason})
        return

    send_ampli_event('User Requests Manual Review', uid)

    return



def access_secret_version(secret_name):
    '''Access a secret from the project's secret manager'''
    _, project_id = default()
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
    response = client.access_secret_version(name=name)
    return response.payload.data.decode('UTF-8')


def connect_with_connector(connector: Connector) -> sqlalchemy.engine.base.Engine:
    """
    Initializes a connection pool for a Cloud SQL instance of Postgres.

    Uses the Cloud SQL Python Connector package.
    """

    def getconn() -> pg8000.dbapi.Connection:
        conn: pg8000.dbapi.Connection = connector.connect(
            "greenrocks-9abc3:europe-west2:chyrpe-prod1",
            "pg8000",
            user="<EMAIL>",
            db="chyrpe_main_db",
            enable_iam_auth=True,
        )
        return conn

    # The Cloud SQL Python Connector can be used with SQLAlchemy
    # using the 'creator' argument to 'create_engine'
    pool = sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        pool_size=2,
        max_overflow=2,
        # ...
    )
    return pool


def create_retry_session() -> requests.Session:
    session = requests.Session()
    retries = Retry(total=5, backoff_factor=0.1, status_forcelist=[429, 500, 502, 503, 504])
    session.mount('https://', HTTPAdapter(max_retries=retries))
    return session


def detect_findom_content(scannable_content):
    findom = False

    from pydantic import BaseModel
    from openai import AzureOpenAI, BadRequestError

    client = AzureOpenAI(
        azure_endpoint="https://chyrpegptp.openai.azure.com/",
        api_key=access_secret_version("OpenAI_Key"),
        api_version="2024-10-21"
    )

    class LanguageEvaluation(BaseModel):
        input_text: str
        evaluation_result: int

    try:
        # Call the GPT model with the schema
        completion = client.beta.chat.completions.parse(
            model="gpt-4o-mini",  # replace with the actual model deployment name
            messages=[
                {"role": "system",
                 "content": """Act as a findom & professional detector that strictly and only outputs: 1 for true it is a findom/professional. or: 0 for false it is not a findom/professional. or: 2 for unsure / edgecase. A findom is somebody that wants to financially dominate another, we need to detect these people. A professional is someone trying to sell a service/good or a professional dominatrix, etc. Look for signs in this piece of text that this person is a findom/professional. Just because someone calls herself domme, dominatrix or dominant does not mean she is a professional, she is an edge case then. If she uses the prefix "pro" or "professional" in conjunction with any of the terms domme, dominatrix, dominant, she is to be classes as professional. Calling herself a findom (or any derivative of it) is a clear sign to class her as professional."""},
                {"role": "user", "content": scannable_content}
            ],
            response_format=LanguageEvaluation,  # Using the schema here
        )

        # Parse and print the result
        evaluation = completion.choices[0].message.to_dict().get("parsed", {}).get("evaluation_result")

        logging.info(evaluation)

        if evaluation == 0 or evaluation == 2:
            findom = False
        else:
            findom = True

    except Exception as e:
        logging.error(e)
        findom = False

    return findom


def grant_promotional_access_rc(entitlement_name: str, time_to_end: datetime, user_id: str, secret: str,
                                s: requests.Session) -> bool:
    timestamp_end = int(time_to_end.timestamp() * 1000)

    url = f"https://api.revenuecat.com/v1/subscribers/{user_id}/entitlements/{entitlement_name}/promotional"

    payload = json.dumps({
        "end_time_ms": timestamp_end
    })
    headers = {
        'Authorization': f'Bearer {secret}',
        'Content-Type': 'application/json'
    }

    response = s.post(url, headers=headers, data=payload)

    if response.status_code in [200, 201]:
        return True
    else:
        return False


def moderate_content(scannable_content):
    valid = True

    from pydantic import BaseModel
    from openai import AzureOpenAI, BadRequestError

    client = AzureOpenAI(
        azure_endpoint="https://chyrpegptp.openai.azure.com/",
        api_key=access_secret_version("OpenAI_Key"),
        api_version="2024-10-21"
    )

    class LanguageEvaluation(BaseModel):
        input_text: str
        evaluation_result: int

    try:
        # Call the GPT model with the schema
        completion = client.beta.chat.completions.parse(
            model="gpt-4o-mini",  # replace with the actual model deployment name
            messages=[
                {"role": "system", "content": "Evaluate the provided text for inappropriate language."},
                {"role": "user", "content": scannable_content}
            ],
            response_format=LanguageEvaluation,  # Using the schema here
        )

        # Parse and print the result
        evaluation = completion.choices[0].message.to_dict().get("parsed", {}).get("evaluation_result")

        if evaluation == 0:
            valid = True
        else:
            valid = False

    except Exception as e:
        logging.exception(e)
        valid = False

    return valid


def sendPushNotification(uid, title, text, push_notifications_collection=None):
    if push_notifications_collection == None:
        push_notifications_collection = firestore.client().collection('ff_user_push_notifications')

    push_notifications_collection.add({"notification_title": title, "notification_text": text,
                                       "user_refs": f'/users/{uid}',
                                       "timestamp": firestore.SERVER_TIMESTAMP})


def log_retry_attempt(retry_state):
    """Logs retry attempts before each retry."""
    logging.info(f"Retrying... Attempt {retry_state.attempt_number}")


def with_retry(max_attempts=10, min_wait=2, max_wait=20, exception_types=(Exception)):
    """Decorator to add retry logic with exponential backoff."""
    return retry(
        retry=retry_if_exception_type(exception_types),
        wait=wait_exponential(multiplier=1, min=min_wait, max=max_wait),  # Initial wait 2s, doubles each time, max 10s
        stop=stop_after_attempt(max_attempts),  # Max 5 retry attempts
        before=log_retry_attempt,
        reraise=True
    )


def profileSetUpMen(uid, pool) -> Any:
    """
    Series of steps and database update to make when a man is verified & ready to start matching
    :param uid: User's uid
    :return: None
    """

    # initialize firestore
    db = firestore.client()

    # set user doc ref and get user doc, extract data
    user_doc_ref = db.collection("users").document(uid)
    user_doc = user_doc_ref.get()
    user_doc_data = user_doc.to_dict()
    user_data = user_doc_data

    user_publicProfile = user_doc_data.get("publicProfile")

    # extract data from user doc, to be used in process
    gender = user_doc_data.get("gender")
    location = user_doc_data.get("location", False)
    location_city = user_doc_data.get("city", False)
    location_region = user_doc_data.get("wlRegion", False)
    user_local_matching = user_doc_data.get("localMatching", False)
    user_ref_code = user_data.get("refCode", "")
    chyrpeMatchRef = user_doc_data.get("chyrpeMatchRef", False)
    like_ref = chyrpeMatchRef

    # adds the Chyrpe Chat match if user is in the right state (location set) and Chyrpe Chat not set yet
    if gender == "Male" and location != False and location_city != False and location_region != False and chyrpeMatchRef == False:
        chyrpe_user = db.collection("users").document('fehBchlcouUcVwb66DJL03ZQJPH3')
        update_time, like_ref = db.collection('likes').add({'likingUser': chyrpe_user,
                                                            'likedUser': user_doc_ref,
                                                            'evolvedLike': False,
                                                            'speakAllow': True,
                                                            'chatting': True,
                                                            'unmatched': False,
                                                            'mutual': True,
                                                            'recentMessageText': get_translated_text("chyrpe_welcome_title", user_doc_data.get("language", "en")),
                                                            'recentMessageSeenBy': [
                                                                chyrpe_user, user_doc_ref
                                                            ],
                                                            'recentMessageSender': chyrpe_user,
                                                            'recentMessageDate': firestore.SERVER_TIMESTAMP,
                                                            'involvedUsersProfileRefOnly':
                                                                [user_doc.get('publicProfile'),
                                                                 chyrpe_user.collection(
                                                                     'publicProfile').document(
                                                                     '80KmP3ztM6AJVozLcujS')],
                                                            'involvedUsers':
                                                                [chyrpe_user, user_doc_ref],
                                                            'involedUIDs': [
                                                                'fehBchlcouUcVwb66DJL03ZQJPH3', uid]
                                                            })

        update_time_mes, message_ref = like_ref.collection('messages').add({
            'body': get_translated_text("chyrpe_welcome", user_doc_data.get("language", "en")),
            'time':
                firestore.SERVER_TIMESTAMP,
            'sender':
                chyrpe_user,
            'isSysMessage':
                False})

    if gender == "Male" and location != False and location_city != False and location_region != False:
        user_doc_ref.update(
            {'signUpFinished': True, 'freeGoldShown': True, 'freeGold2Shown': True,
             'chyrpeStandardSubscriber': True, "chyrpeMatchRef": like_ref,
             "accountCreationFinished": firestore.SERVER_TIMESTAMP, "lowerHeightReq": 0,
             "upperHeightReq": 250, "pnMa": True,
             "pnMe": True, "distanceReq": 500.1, "globalWlAdmitted": True})

        try:

            u_location = user_doc_data.get('location')
            u_lat = u_location.latitude
            u_long = u_location.longitude

            here_key = access_secret_version("Here_Key")

            url = f"https://revgeocode.search.hereapi.com/v1/revgeocode?at={u_lat},{u_long}&lang=en-US&apiKey={here_key}"
            payload = {}
            headers = {}
            response = requests.request("GET", url, headers=headers, data=payload)

            try:

                country_name = response.json().get("items", {})[0].get("address").get("countryName")
                state = response.json().get("items", {})[0].get("address").get("state")
                city = response.json().get("items", {})[0].get("address").get("city")

            except:

                country_name = "Unknown"
                state = "Unknown"
                city = "Unknown"

            identify_obj1 = Identify()
            identify_obj1.set("Country_EN", country_name)
            amplitude_client.identify(identify_obj1, EventOptions(user_id=uid))
            identify_obj2 = Identify()
            identify_obj2.set("State_EN", state)
            amplitude_client.identify(identify_obj2, EventOptions(user_id=uid))
            identify_obj3 = Identify()
            identify_obj3.set("City_EN", city)
            amplitude_client.identify(identify_obj3, EventOptions(user_id=uid))
            identify_obj = Identify()
            identify_obj.set("Global Wl Admitted", True)
            amplitude_client.identify(identify_obj, EventOptions(user_id=uid))
            identify_obj5 = Identify()
            identify_obj5.set("Sign Up Finished", True)
            amplitude_client.identify(identify_obj5, EventOptions(user_id=uid))

            if user_local_matching:
                identify_obj = Identify()
                identify_obj.set("Regional Wl Admitted", True)
                amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

            user_doc_ref.update({'city': city, 'country': country_name, 'state': state})

            event1 = BaseEvent(event_type="Started Matching", user_id=uid)
            amplitude_client.track(event1)

            event2 = BaseEvent(event_type="Man Started Matching", user_id=uid)
            amplitude_client.track(event2)

            user_publicProfile.update({'city': city})

        except Exception as e:
            logging.exception(e)

            logging.info("Amplitude failed")

        # add him to Postgres
        try:
            try:

                @with_retry()
                def admitUser(pool):
                    user_long = user_data.get('location').longitude
                    user_lat = user_data.get('location').latitude

                    with pool.connect() as db_conn:
                        stmt = sqlalchemy.text(

                            """SELECT add_user(:user_id, :age, :date_of_birth, :gender, :role, :gender_preference, :longitude, :latitude, :upper_age_band, :lower_age_band,
                            :proximity_preference, :elo_score, :elo_score_raw, :match_status, :incognito,
                            :started_matching, :latest_suggestions, :findom_interest)""")

                        # Execute with the parameters

                        result = db_conn.execute(stmt, {
                            'user_id': user_data.get('uid'),
                            'age': user_data.get('age'),
                            'date_of_birth': user_data.get('birthday').strftime("%Y-%m-%d"),
                            'gender': user_data.get('gender'),
                            'role': user_data.get('position').capitalize(),
                            'elo_score': 0.5,
                            'started_matching': datetime.now().strftime("%Y-%m-%d"),
                            'latest_suggestions': datetime.now().strftime("%Y-%m-%d"),
                            'longitude': round(user_long, 2),
                            'latitude': round(user_lat, 2),
                            'upper_age_band': user_data.get('upperAgeReq'),
                            'lower_age_band': user_data.get('lowerAgeReq'),
                            'proximity_preference': 100000000,  # in m, not in km
                            'gender_preference': 'Female' if user_data.get(
                                'genderReq') == 'Women' else (
                                'Male' if user_data.get('genderReq') == 'Men' else 'Everyone'),
                            'match_status': 'RegionalMatch' if user_local_matching else 'GlobalMatch',
                            'incognito': user_data.get('incognito', False),
                            'findom_interest': get_findom_interest_status(user_data.get("kinks", []), user_data.get("professionalsReq", False)),
                            'elo_score_raw': 1000}).fetchall()

                        db_conn.commit()

                if not is_dev_environment():
                    admitUser(pool)


            except Exception as e:

                logging.exception(e)
                db.collection('SQLExceptions').add(
                    {'function': 'profileSetUpMen', 'user': user_data.get('uid'),
                     'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

            except:
                logging.exception(f"Fail Profile Setup Women: {uid}")

        except:
            logging.exception("SQL Error")

        if not is_dev_environment():
            try:
                nm.update_user_current(uid)
            except Exception as e:
                logging.exception(e)

    # if the user has a valid referral code
    if user_ref_code != '' and user_ref_code != 'default':
        try:
            import referral_helper as rh

            rh.attributeReferredUser(uid)
        except Exception as e:
            logging.error(e)

    return


def profileSetUpWomen(uid, pool) -> Any:
    """
        Series of steps and database update to make when a woman is verified & ready to start matching
        :param uid: User's uid
        :return: None
    """
    # initialize firestore
    db = firestore.client()

    # set user doc ref and get user doc, extract data
    user_doc_ref = db.collection("users").document(uid)
    user_doc = user_doc_ref.get()
    user_doc_data = user_doc.to_dict()
    user_data = user_doc_data

    user_publicProfile = user_doc_data.get("publicProfile")

    gender = user_doc_data.get("gender")
    location = user_doc_data.get("location", False)
    location_city = user_doc_data.get("city", False)
    location_region = user_doc_data.get("wlRegion", False)
    chyrpeMatchRef = user_doc_data.get("chyrpeMatchRef", False)
    like_ref = chyrpeMatchRef

    findom = False

    try:
        user_public_profile_data = user_publicProfile.get().to_dict()
        user_bio = user_public_profile_data.get("bio", "")
        prompts = user_public_profile_data.get("prompts", [])
        promptsEdited = ". ".join(prompt.get('answer', '') for prompt in prompts if 'answer' in prompt)
        scannable_content = ", ".join([user_bio, promptsEdited])

        print(scannable_content)

        findom = detect_findom_content(scannable_content)

    except Exception as e:
        logging.exception(e)


    # adds the Chyrpe Chat match if user is in the right state (location set) and Chyrpe Chat not set yet
    if gender == "Female" and location != False and location_city != False and location_region != False:
        chyrpe_user = db.collection("users").document('fehBchlcouUcVwb66DJL03ZQJPH3')
        if chyrpeMatchRef == False:
            update_time, like_ref = db.collection('likes').add({'likingUser': chyrpe_user,
                                                                'likedUser': user_doc_ref,
                                                                'evolvedLike': False,
                                                                'speakAllow': True,
                                                                'chatting': True,
                                                                'unmatched': False,
                                                                'mutual': True,
                                                                'recentMessageText': get_translated_text("chyrpe_welcome_title", user_doc_data.get("language", "en")),
                                                                'recentMessageSeenBy': [
                                                                    chyrpe_user, user_doc_ref
                                                                ],
                                                                'recentMessageSender': chyrpe_user,
                                                                'recentMessageDate': firestore.SERVER_TIMESTAMP,
                                                                'involvedUsersProfileRefOnly':
                                                                    [user_doc.get('publicProfile'),
                                                                     chyrpe_user.collection(
                                                                         'publicProfile').document(
                                                                         '80KmP3ztM6AJVozLcujS')],
                                                                'involvedUsers':
                                                                    [chyrpe_user, user_doc_ref],
                                                                'involedUIDs': [
                                                                    'fehBchlcouUcVwb66DJL03ZQJPH3', uid]
                                                                })
            update_time_mes, message_ref = like_ref.collection('messages').add({
                'body': get_translated_text("chyrpe_welcome", user_doc_data.get("language", "en")),
                'time':
                    firestore.SERVER_TIMESTAMP,
                'sender':
                    chyrpe_user,
                'isSysMessage':
                    False})

    if gender == "Female" and location != False and location_city != False and location_region != False:
        # query whether her region is available and she should get into global or local matching
        region_collection = db.collection("openRegions")
        her_region_query = region_collection.where("name", "==", location_region)
        her_region = her_region_query.get()

        # some regions are open as a preview only to women, also note that
        region_collection_pre = db.collection("openRegionsPre")
        her_pre_region_query = region_collection_pre.where("name", "==", location_region)
        her_pre_region = her_pre_region_query.get()

        waiting_list_deferred_admission_collection = db.collection("wlDeferredAdmission")

        if len(her_region) > 0:
            user_doc_ref.update(
                {'signUpFinished': True, 'freeGoldShown': True, 'freeGold2Shown': True,
                 'chyrpeStandardSubscriber': True, "chyrpeMatchRef": like_ref,
                 "accountCreationFinished": firestore.SERVER_TIMESTAMP, "globalMatching": False,
                 "localMatching": True, "lowerHeightReq": 0, "upperHeightReq": 250, "pnMa": True,
                 "pnMe": True, "distanceReq": 100, "localWlAdmitted": True, "globalWlAdmitted": True, "findom": findom})
            get_deferred_list_query = waiting_list_deferred_admission_collection.where(
                filter=FieldFilter("region", "==", location_region))
            deferred_admission_docs = get_deferred_list_query.get()

            # admit men into regional matching (deferred)
            if len(deferred_admission_docs) > 0:
                deferred_admission_doc = deferred_admission_docs[0]
                deferred_admission_doc.reference.update(
                    {"remainingAdmits": firestore.Increment(10)})
            else:
                waiting_list_deferred_admission_collection.add(
                    {"region": location_region, "remainingAdmits": 10})

        elif len(her_pre_region) > 0:
            user_doc_ref.update(
                {'signUpFinished': True, 'freeGoldShown': True, 'freeGold2Shown': True,
                 'chyrpeStandardSubscriber': True, "chyrpeMatchRef": like_ref,
                 "accountCreationFinished": firestore.SERVER_TIMESTAMP, "globalMatching": False,
                 "localMatching": True, "lowerHeightReq": 0, "upperHeightReq": 250, "pnMa": True,
                 "pnMe": True, "distanceReq": 500.1, "localWlAdmitted": True, "globalWlAdmitted": True,
                 "findom": findom})

        else:
            # put her into global matching if regional is not available + admit men globally
            user_doc_ref.update({'signUpFinished': True, 'chyrpeStandardSubscriber': True,
                                 "chyrpeMatchRef": like_ref,
                                 "accountCreationFinished": firestore.SERVER_TIMESTAMP,
                                 "globalMatching": True,
                                 "localMatching": False, "lowerHeightReq": 0,
                                 "upperHeightReq": 250, "pnMa": True, "pnMe": True, "distanceReq": 500.1,
                                 "findom": findom})

            get_deferred_list_query = waiting_list_deferred_admission_collection.where(
                filter=FieldFilter("region", "==", "GLOBAL_WL"))
            deferred_admission_docs = get_deferred_list_query.get()

            try:
                user_data.get("publicProfile").update({"cityHidden": False})
            except Exception as e:
                logging.exception(e)

            if len(deferred_admission_docs) > 0:
                deferred_admission_doc = deferred_admission_docs[0]
                deferred_admission_doc.reference.update(
                    {"remainingAdmits": firestore.Increment(10)})  # for test
            else:
                waiting_list_deferred_admission_collection.add(
                    {"region": "GLOBAL_WL", "remainingAdmits": 10})

        try:

            u_location = user_doc_data.get('location')
            u_lat = u_location.latitude
            u_long = u_location.longitude

            here_key = access_secret_version("Here_Key")
            url = f"https://revgeocode.search.hereapi.com/v1/revgeocode?at={u_lat},{u_long}&lang=en-US&apiKey={here_key}"
            payload = {}
            headers = {}

            response = requests.request("GET", url, headers=headers, data=payload)

            try:
                country_name = response.json().get("items", {})[0].get("address").get("countryName")
                state = response.json().get("items", {})[0].get("address").get("state")
                city = response.json().get("items", {})[0].get("address").get("city")
            except:
                country_name = "Unknown"
                state = "Unknown"
                city = "Unknown"

            identify_obj1 = Identify()
            identify_obj1.set("Country_EN", country_name)
            amplitude_client.identify(identify_obj1, EventOptions(user_id=uid))

            identify_obj2 = Identify()
            identify_obj2.set("State_EN", state)
            amplitude_client.identify(identify_obj2, EventOptions(user_id=uid))

            identify_obj3 = Identify()
            identify_obj3.set("City_EN", city)
            amplitude_client.identify(identify_obj3, EventOptions(user_id=uid))

            identify_obj = Identify()
            identify_obj.set("Global Wl Admitted", True)
            amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

            identify_obj5 = Identify()
            identify_obj5.set("Sign Up Finished", True)
            amplitude_client.identify(identify_obj5, EventOptions(user_id=uid))

            if len(her_region) > 0 or len(her_pre_region) > 0:
                identify_obj = Identify()
                identify_obj.set("Regional Wl Admitted", True)
                amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

            user_doc_ref.update({'city': city, 'country': country_name, 'state': state})
            user_publicProfile.update({'city': city, "findom": findom})

            event1 = BaseEvent(event_type="Started Matching", user_id=uid)
            amplitude_client.track(event1)

            event2 = BaseEvent(event_type="Woman Started Matching", user_id=uid)
            amplitude_client.track(event2)
        except:
            logging.info("Amplitude failed")

        # add her to Postgres
        try:
            try:

                @with_retry()
                def admitUserW(pool):
                    user_long = user_data.get('location').longitude
                    user_lat = user_data.get('location').latitude

                    with pool.connect() as db_conn:
                        # query database
                        stmt = sqlalchemy.text(
                            """SELECT add_user(:user_id, :age, :date_of_birth, :gender, :role, :gender_preference, :longitude, :latitude, :upper_age_band, :lower_age_band,
                            :proximity_preference, :elo_score, :elo_score_raw, :match_status, :incognito,
                            :started_matching, :latest_suggestions)""")

                        result = db_conn.execute(stmt, {
                            'user_id': user_data.get('uid'),
                            'age': user_data.get('age'),
                            'date_of_birth': user_data.get('birthday').strftime("%Y-%m-%d"),
                            'gender': user_data.get('gender'),
                            'role': user_data.get('position').capitalize(),
                            'elo_score': 0.5,
                            'started_matching': datetime.now().strftime("%Y-%m-%d"),
                            'latest_suggestions': datetime.now().strftime("%Y-%m-%d"),
                            'longitude': round(user_long, 2),
                            'latitude': round(user_lat, 2),
                            'upper_age_band': user_data.get('upperAgeReq'),
                            'lower_age_band': user_data.get('lowerAgeReq'),
                            'proximity_preference': 100000000,  # in m, not in km
                            'gender_preference': 'Female' if user_data.get(
                                'genderReq') == 'Women' else (
                                'Male' if user_data.get('genderReq') == 'Men' else 'Everyone'),
                            'match_status': 'RegionalMatch' if (
                                    len(her_region) > 0 or len(her_pre_region) > 0) else 'GlobalMatch',
                            'incognito': user_data.get('incognito', False),
                            'elo_score_raw': 1000}).fetchall()

                        db_conn.commit()

                if not is_dev_environment():
                    admitUserW(pool)

            except Exception as e:
                logging.exception(e)
                db.collection('SQLExceptions').add(
                    {'function': 'profileSetUpWomen', 'user': user_data.get('uid'),
                     'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

            except:
                logging.exception(f"Fail Profile Setup Women: {uid}")
        except:
            print("SQL Error")

        if not is_dev_environment():
            try:
                nm.update_user_current(uid)

                if findom:
                    db.collection("findomEnable").add({"uid": uid})

            except Exception as e:
                logging.exception(e)

    return





# GENDER CONFIRM: Allows users to change their display gender after verification
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.MB_512, enforce_app_check=(not is_dev_environment()))
def genderConfirm(req: https_fn.CallableRequest) -> Any:
    """
    Allows users to change their display gender after verification
    :param req: Callable request incl. user auth information - extra param: confirmedGender (string)
    :return: None
    """
    if req.auth == None:
        return "Not authenticated"

    uid = req.auth.uid

    db = firestore.client()

    global db_conn_new

    if db_conn_new == None:
        db_conn_new = connect_with_connector_persist()

    user_collection = db.collection("users")
    user_doc_ref = user_collection.document(uid)
    user_doc = user_doc_ref.get()

    confirmed_gender = req.data.get("confirmedGender")

    public_profile_ref = user_doc.get("publicProfile")

    # update public profile & profile according to confirmed gender
    public_profile_ref.update({"alternativeG": True, "gender": confirmed_gender, "displayG": confirmed_gender})
    user_doc_ref.update({"alternativeG": True, "gender": "Male", "genderConfirm": False})

    send_ampli_event('Profile: Confirm Gender After Verification', uid, {'Moderator': False})

    profileSetUpMen(uid, db_conn_new)


import re
from google.cloud import vision
import urllib.parse
from google.cloud import storage


def get_image_bytes(image_url):
    import io

    # Fetch the image from the URL
    response = requests.get(image_url)
    response.raise_for_status()  # Raise HTTPError for bad responses (4xx and 5xx)

    # Open the image content with Pillow
    image = Image.open(io.BytesIO(response.content))

    print(image)

    if image.width * image.height > 16000 * 16000:  # Prevent huge images
        raise ValueError("Image dimensions too large")

    # Convert the image to JPEG bytes
    jpeg_bytes = io.BytesIO()
    image.convert("RGB").save(jpeg_bytes, format="JPEG")

    print(jpeg_bytes)

    # Reset the buffer pointer to the beginning
    jpeg_bytes.seek(0)

    return jpeg_bytes.getvalue()


def convert_firebase_url_to_gs(firebase_url):
    """Converts the Firebase Token URL of an image to its Bucket location URL"""
    # Step 1: Parse the URL
    parsed_url = urllib.parse.urlparse(firebase_url)

    # Step 2: Extract the bucket name from the path
    bucket_name = parsed_url.path.split('b/')[1].split('/')[0]  # Extract bucket name after 'b/'

    # Step 3: Extract the encoded file path and decode it
    encoded_file_path = parsed_url.path.split('/o/')[1]
    decoded_file_path = urllib.parse.unquote(encoded_file_path)

    # Step 4: Construct the gs:// URL
    gs_url = f"gs://{bucket_name}/{decoded_file_path}"
    return bucket_name, gs_url


safe_search_concepts = {
    'verification': {
        'adult': [vision.Likelihood.UNLIKELY, vision.Likelihood.VERY_UNLIKELY],
        'medical': [vision.Likelihood.UNLIKELY, vision.Likelihood.VERY_UNLIKELY],
        'violence': [vision.Likelihood.UNLIKELY, vision.Likelihood.VERY_UNLIKELY, vision.Likelihood.POSSIBLE],
        'racy': [vision.Likelihood.UNLIKELY, vision.Likelihood.VERY_UNLIKELY, vision.Likelihood.POSSIBLE,
                 vision.Likelihood.LIKELY]
    },
    'always_check': {
        'adult': [vision.Likelihood.UNLIKELY, vision.Likelihood.VERY_UNLIKELY, vision.Likelihood.POSSIBLE],
        'medical': [vision.Likelihood.UNLIKELY, vision.Likelihood.VERY_UNLIKELY, vision.Likelihood.POSSIBLE,
                    vision.Likelihood.LIKELY],
        'violence': [vision.Likelihood.UNLIKELY, vision.Likelihood.VERY_UNLIKELY, vision.Likelihood.POSSIBLE,
                     vision.Likelihood.LIKELY],
        'racy': [vision.Likelihood.UNLIKELY, vision.Likelihood.VERY_UNLIKELY, vision.Likelihood.POSSIBLE,
                 vision.Likelihood.LIKELY, vision.Likelihood.VERY_LIKELY]
    }
}


def safe_search(path):
    """
    Calls Google Cloud Vision API to detect explicit content and face presence in images.
    Returns a dict with:
    - adult: "VERY_LIKELY" if adult content detected
    - has_face: True if at least one face is detected
    """
    client = vision.ImageAnnotatorClient()

    image = vision.Image()
    image.source.image_uri = path

    # Define the features we want to detect
    features = [
        vision.Feature(type=vision.Feature.Type.SAFE_SEARCH_DETECTION),
        vision.Feature(type=vision.Feature.Type.FACE_DETECTION)
    ]

    # Create and send the request
    request = vision.AnnotateImageRequest(image=image, features=features)
    response = client.annotate_image(request=request)

    # Check for errors
    if response.error.message:
        logging.error(f"Error in safe_search: {response.error.message}")
        return {"adult": "VERY_UNLIKELY", "has_face": True}  # Conservative approach on error

    # Process SafeSearch results
    safe_search = response.safe_search_annotation
    adult_likelihood = vision.Likelihood(safe_search.adult).name

    # Process face detection results - True if at least one face detected
    has_face = len(response.face_annotations) > 0

    return {
        "adult": adult_likelihood,
        "has_face": has_face
    }

# REQUEST MANUAL VERI REVIEW: Allows users who failed automatic verification to request a review from team
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.GB_1, enforce_app_check=True)
def requestManualVeriReview(req: https_fn.CallableRequest) -> Any:
    """
    Allows users who failed automatic verification to request a review from team
    :param req: Callable request incl. user auth information - No extra params
    :return: None
    """
    if req.auth == None:
        return "Not authenticated"

    uid = req.auth.uid

    db = firestore.client()

    user_collection = db.collection("users")
    user_doc_ref = user_collection.document(uid)
    user_doc_data = user_doc_ref.get().to_dict()
    verifications_collection = db.collection("verifications")

    verifications_collection.add({"userToVerify": user_doc_ref,
                                  "genderOfUserToVerify":
                                      user_doc_data.get("gender"),
                                  "handled": False,
                                  "timeCreated": firestore.SERVER_TIMESTAMP,
                                  "name": user_doc_data.get("name"),
                                  "uid": uid,
                                  "verificationPhoto": user_doc_data.get("verification_picture"),
                                  "manualReviewOfAuto": True})

    user_doc_ref.update({"verificationGoingOn": True, "manualVerification": True, "autoVerificationStarted": False,
                         "autoVeriReclass": True,  "verificationCustomTitle": "Manual Review in Progress",
         "verificationCustomSubtitle": "This can take up to 48 hours, but is mostly quicker.",})

    track_manual_review_verification_requested(uid)

@https_fn.on_call(region="europe-west1", memory=options.MemoryOption.MB_512, enforce_app_check=(not is_dev_environment()))
def requestManualVeriReviewGeneral(req: https_fn.CallableRequest) -> Any:
    """
    Allows users who failed automatic verification to request a review from team
    :param req: Callable request incl. user auth information - No extra params
    :return: None
    """
    if req.auth == None:
        return "Not authenticated"

    uid = req.auth.uid

    db = firestore.client()

    user_collection = db.collection("users")
    user_doc_ref = user_collection.document(uid)

    user_doc_data = user_doc_ref.get().to_dict()

    manual_verification_reason = user_doc_data.get("manualVerificationReason")

    user_doc_ref.update({"verificationGoingOn": True, "manualVerification": True, "autoVerificationStarted": False,
                         "autoVeriReclass": True, "manualVerificationReason": manual_verification_reason if manual_verification_reason else "User Request",
                         "verificationCustomTitle": "Manual Review in Progress",
                         "verificationCustomSubtitle": "A human member of our team is now reviewing your verification. This can take up to 48 hours, but is mostly quicker.",
                         })

    track_manual_review_verification_requested(uid)


@https_fn.on_call(region="europe-west1", memory=options.MemoryOption.MB_512, enforce_app_check=(not is_dev_environment()))
def requestManualVeriGeneral(req: https_fn.CallableRequest) -> Any:
    """
    Allows users who failed automatic verification to request a review from team
    :param req: Callable request incl. user auth information - No extra params
    :return: None
    """
    if req.auth == None:
        return "Not authenticated"

    uid = req.auth.uid

    db = firestore.client()

    user_collection = db.collection("users")
    user_doc_ref = user_collection.document(uid)

    user_doc_data = user_doc_ref.get().to_dict()

    manual_verification_reason = user_doc_data.get("manualVerificationReason")

    user_doc_ref.update({"verificationGoingOn": True, "manualVerification": True, "autoVerificationStarted": False,
                         "autoVeriReclass": False,
                         "manualVerificationReason": manual_verification_reason if manual_verification_reason else "User Request",
                         "verificationPhoto": req.data.get("verificationPhotoUrl"),
                         "verificationCustomTitle": "Manual Review in Progress",
                         "verificationCustomSubtitle": "A human member of our team is now reviewing your verification. This can take up to 48 hours, but is mostly quicker.",
                         "verificationV3": True
                         })

    db.collection("users").document(uid).collection("meta").document("verificationRequests").set({
        "manual": firestore.firestore.ArrayUnion([{
            "timestamp": datetime.now().strftime('%Y%m%d %H:%M:%S'),
            "imageUrl": req.data.get("verificationPhotoUrl"),
        }])
    }, merge=True)

    track_manual_review_verification_requested(uid)


@https_fn.on_call(region="europe-west1", memory=options.MemoryOption.MB_512, enforce_app_check=True)
def resetManualVeri(req: https_fn.CallableRequest) -> Any:
    """
    Allows users who failed automatic verification to request a review from team
    :param req: Callable request incl. user auth information - No extra params
    :return: None
    """
    if req.auth == None:
        return "Not authenticated"

    uid = req.auth.uid

    db = firestore.client()

    user_collection = db.collection("users")
    user_doc_ref = user_collection.document(uid)

    user_doc_ref.update({"verificationGoingOn": False, "verificationFailed": False})



import re
from urllib.parse import unquote


# UPON VERIFICATION HANDLED: Runs when a manual verification request has been handled
@on_document_updated(document="verifications/{documentId}", region="europe-west1", timeout_sec=540,
                     memory=options.MemoryOption.GB_8,cpu=2,concurrency=5) # type: ignore[assignment]
def uponVerificationHandled(event: Event[Change[DocumentSnapshot]]) -> None:
    """
    Runs when a manual verification request has been handled
    :param event: Includes affected verification's information
    :return: None
    """
    new_value: Dict[str, Any] = event.data.after.to_dict() or {} if event.data.after else {}
    old_value = event.data.before.to_dict() or {} if event.data.before else {}

    db = firestore.client()

    global db_conn_new

    if db_conn_new == None:
        db_conn_new = connect_with_connector_persist()

    uid = new_value.get("uid")

    # get user data

    users_collection = db.collection("users")
    push_notifications_collection = db.collection("ff_user_push_notifications")
    verification_photo = new_value["verificationPhoto"]

    users_doc = users_collection.document(uid).get()
    gender = users_doc.get("gender")
    gwl_admitted = users_doc.to_dict().get("globalWlAdmitted", False)
    lwl_admitted = users_doc.to_dict().get("localWlAdmitted", False)


    # provision for backward-compatible handling of new style verification results
    if new_value.get('handledByNewStyle'):
        logging.info(f'{uid} handled by new style')
        return

    # if verification was successful
    if new_value.get("handled") == True and new_value.get("verified") == True:

        user_ref_code = ''

        # makes sure no loop is created by the on_updated trigger
        if old_value.get("handled") != new_value.get("handled") or old_value.get("verified") != new_value.get(
                "verified"):
            try:
                user_doc = users_collection.document(uid).get().to_dict()
                user_ref_code = user_doc.get("refCode", "")
                user_gender = user_doc.get("gender")

                identify_obj = Identify()
                identify_obj.set("Verified", True)
                amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

                event = BaseEvent(event_type="Sign Up: Passed Verification", user_id=uid,
                                  event_properties={"Gender": user_gender})
                amplitude_client.track(event)

            except:
                logging.info("Amplitude failed")

            # if the user has a valid referral code
            if user_ref_code != '' and user_ref_code != 'default':
                try:
                    import referral_helper as rh

                    rh.attributeReferredUser(uid)
                except Exception as e:
                    logging.error(e)

            if gender == "Female":

                profileSetUpWomen(uid, db_conn_new)

            elif gender == "Male":

                def addNewUserWaitingList(uid) -> Any:

                    # initialize firestore

                    db = firestore.client()

                    # set user doc ref and get user doc, extract data and from data region

                    user_doc_ref = db.collection("users").document(uid)

                    user_doc = user_doc_ref.get()

                    user_doc_data = user_doc.to_dict()

                    user_region = user_doc_data['wlRegion']

                    rtdb_db = rtdb.reference('users')

                    rtdb_user_ref = rtdb_db.child(uid)

                    rtdb_user_ref.update({

                        'location/latitude': user_doc_data['location'].latitude,

                        'location/longitude': user_doc_data['location'].longitude,

                        'location/wlRegion': user_doc_data['wlRegion']

                    })

                    # get document for user's location

                    wl_region_ref = db.collection("waitingList").document(user_region)

                    region_collection = db.collection("openRegions")
                    his_region_query = region_collection.where("name", "==", user_region)
                    his_region = his_region_query.get()

                    transaction = db.transaction()

                    @firestore.transactional
                    def update_in_transaction(transaction, wl_region_ref):

                        # get master doc for that region

                        master_wl_region_doc = wl_region_ref.get(transaction=transaction)

                        users = master_wl_region_doc.to_dict().get("users", [])

                        users.append({"reference": user_doc_ref, "sublevel": "100"})

                        transaction.update(wl_region_ref, {"users": users})

                        return len(users)

                    index_of_user = update_in_transaction(transaction, wl_region_ref)

                    def calculate_interval_range(position):

                        """

                        Calculate the interval range for a given position based on specified ranges.


                        Args:

                        - position (int): The position of an item in the array.


                        Returns:

                        - str: The interval range in which the position falls, in the format "start-end".

                        """

                        position = position * 5

                        if position < 500:

                            interval_start = (position // 100) * 100

                            interval_end = interval_start + 100

                        elif position < 2500:

                            interval_start = ((position - 500) // 250) * 250 + 500

                            interval_end = interval_start + 250

                        elif position < 5000:

                            interval_start = ((position - 2500) // 500) * 500 + 2500

                            interval_end = interval_start + 500

                        else:

                            interval_start = ((position - 5000) // 1000) * 1000 + 5000

                            interval_end = interval_start + 1000

                        return f"{1 if interval_start == 0 else interval_start}-{interval_end}"

                    user_interval = calculate_interval_range(index_of_user)

                    user_doc_ref.update(

                        {"onWaitingList": True,
                         "waitingListInterval": "Wait for matching" if len(his_region) == 0 else user_interval,
                         "regionOpen": True if len(his_region) > 0 else False,

                         "trueFirstInterval": user_interval})

                    try:

                        u_location = user_doc_data.get('location')

                        u_lat = u_location.latitude

                        u_long = u_location.longitude

                        here_key = access_secret_version("Here_Key")

                        url = f"https://revgeocode.search.hereapi.com/v1/revgeocode?at={u_lat},{u_long}&lang=en-US&apiKey={here_key}"

                        payload = {}

                        headers = {}

                        response = requests.request("GET", url, headers=headers, data=payload)

                        try:

                            country_name = response.json().get("items", {})[0].get("address").get("countryName")

                            state = response.json().get("items", {})[0].get("address").get("state")

                            city = response.json().get("items", {})[0].get("address").get("city")

                        except:

                            country_name = "Unknown"

                            state = "Unknown"

                            city = "Unknown"

                        identify_obj1 = Identify()

                        identify_obj1.set("Country_EN", country_name)

                        amplitude_client.identify(identify_obj1, EventOptions(user_id=uid))

                        identify_obj2 = Identify()

                        identify_obj2.set("State_EN", state)

                        amplitude_client.identify(identify_obj2, EventOptions(user_id=uid))

                        identify_obj3 = Identify()

                        identify_obj3.set("City_EN", city)

                        amplitude_client.identify(identify_obj3, EventOptions(user_id=uid))

                        identify_obj = Identify()

                        identify_obj.set("Initial Position Regional Wl", user_interval)

                        amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

                        identify_obj4 = Identify()

                        identify_obj4.set("On Regional Waiting List", True)

                        amplitude_client.identify(identify_obj4, EventOptions(user_id=uid))

                        identify_obj5 = Identify()

                        identify_obj5.set("Sign Up Finished", True)

                        amplitude_client.identify(identify_obj5, EventOptions(user_id=uid))

                        event = BaseEvent(event_type="Waiting List: Put on Regional Waiting List", user_id=uid,

                                          event_properties={

                                              "Initial Position Regional Wl": user_interval

                                          })

                        amplitude_client.track(event)

                        user_doc_ref.update({'city': city, 'country': country_name, 'state': state})

                        user_doc_data.get('publicProfile').update({'city': city})

                    except:

                        print("Amplitude failed")

                    return

                if gwl_admitted or lwl_admitted:

                    profileSetUpMen(uid, db_conn_new)

                    rc_secret = access_secret_version("Revenuecat_Key")
                    session = create_retry_session()

                    time_to_end = datetime(2099, 12, 31)

                    grant_promotional_access_rc("standard_access", time_to_end, uid, rc_secret, session)
                    grant_promotional_access_rc("free_standard_lifetime", time_to_end, uid, rc_secret, session)


                else:

                    addNewUserWaitingList(uid)

            db.collection("users").document(uid).update({"verificationCustomTitle": "Congratulations, you are verified",
                     "verificationCustomSubtitle": "Thank you for helping us keep Chyrpe safe & welcoming for all."})

        track_and_notify_verification_successful(uid, True)

    # if verification was not successful
    elif new_value.get("handled") == True and new_value.get("verified") == False:

        # send verification failed event to Amplitude
        try:

            user_doc = users_collection.document(uid).get().to_dict()
            user_gender = user_doc.get("gender")

            event = BaseEvent(event_type="Sign Up: Failed Verification", user_id=uid,
                              event_properties={"Gender": user_gender})
            amplitude_client.track(event)

            db.collection("users").document(uid).update({"verificationCustomTitle": "We couldn't verify you yet",
                                                         "verificationCustomSubtitle": user_doc.get(
                                                             "verificationFailReason", "")})

        except:
            logging.exception("Amplitude failed")

        track_and_notify_verification_failed(uid, True, 'Old Style (Pre 5/25) Verifications', 'Retry Manual', language=user_doc_data.get("language", "en"))



    # store verification photo in Storage bucket
    photoURL = new_value.get("verificationPhoto")

    # Using regular expression to extract the file path from the URL
    matches = re.search(r'o/(.+?)\?alt=media', photoURL)
    if matches:
        from firebase_admin import storage
        file_path = matches.group(1)
        file_path = unquote(file_path)  # Decode URL-encoded string

        # Create a client for the storage
        storage_client = storage
        # Assuming default bucket, specify your bucket name if different
        bucket = storage_client.bucket()

        # Get the file from the bucket
        file = bucket.blob(file_path)

        # Generate a new file name with timestamp
        now = datetime.now().strftime("%Y%m%d%H%M%S")
        new_file_path = f"archivedVerifications/{uid}/{file_path}_{now}"

        # Copy the file to the new location
        new_file = bucket.copy_blob(file, bucket, new_file_path)
        print(f"File archived successfully as {new_file_path}.")

        try:
            # Delete the original file
            file.delete()
            print("Original file deleted successfully.")
        except Exception as e:
            print(f"Failed to delete original file: {e}")
    else:
        print("No file found to process.")

    previous_verifications_collection = db.collection("previousVerificationOutcomes")
    previous_verifications_collection.add(new_value)


import boto3


# GET VERIFICATION SESSION ID: Creates an AWS Rekognition Liveness Session, gets the necessary ID, returns it to client & saves it in user doc
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.GB_1, enforce_app_check=(not is_dev_environment))
def getVerificationSessionID(req: https_fn.CallableRequest) -> Any:
    """
    Creates an AWS Rekognition Liveness Session, gets the necessary ID, returns it to client & saves it in user doc
    :param req: Callable request incl. user auth information - No extra params
    :return: Dictionary with Session ID and AWS region for client
    """
    if req.auth == None:
        return "Not authenticated"

    uid = req.auth.uid

    db = firestore.client()
    # set user doc ref and get user doc, extract data and from data region
    user_doc_ref = db.collection("users").document(uid)
    user_doc = user_doc_ref.get()
    user_doc_data = user_doc.to_dict()

    # defines method to send a slack notification to inform about requested verification
    def send_slack_notification(name, gender, time, user_uid):
        name = name
        gender = gender
        time = time
        user_uid = user_uid

        webhook_url = '*********************************************************************************'
        message = {
            'text': f"New verification request:\n\nName: {name}\nGender: {gender}\nTime: {time}\nUser UID: {user_uid}"
        }

        try:
            response = requests.post(webhook_url, json=message)
            response.raise_for_status()  # Will raise an error for bad responses (4XX, 5XX)
        except requests.exceptions.RequestException as error:
            print(f"Error sending message to Slack: {error}")

    previous_verifications = user_doc_data.get("previousVerifications", [])

    # to prevent DoS or similar attack
    if len(previous_verifications) > 10:
        raise RuntimeError("Rate Limit")

    # creates an AWS Rekognition session & gets necessary session id, then stores it in suer doc & returns
    try:
        aws_key = access_secret_version("AWS_Rekognito_Key")
        aws_secret = access_secret_version("AWS_Rekognito_Secret_Key")

        session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)
        client = session.client('rekognition', region_name='us-east-1')

        def create_session():
            response = client.create_face_liveness_session()

            session_id = response.get("SessionId")
            logging.info('SessionId: ' + session_id)

            return session_id

        session_id = create_session()
        previous_verifications.append(session_id)
        user_doc_ref.update({"previousVerifications": previous_verifications})

        # send Slack notification
        try:
            send_slack_notification(user_doc_data.get("name"), user_doc_data.get("gender"), datetime.now().isoformat(),
                                    user_doc.id)
        except:
            logging.info("Slack Error")

        send_ampli_event('Verification: Requested AWS Start', uid, {'Manual': False})

        return {"session": session_id, "region": "us-east-1"}

    except Exception as e:
        logging.exception(e)
        raise RuntimeError("Internal")


from datetime import datetime
import requests
from io import BytesIO
from PIL import Image
from profanity_check import predict
import json
import re
import urllib.parse


# VERIFY SELF SERVICE: Gets AWS Rekognition Liveness Results, checks for face & gender matches & explicit content, approves verification requests
@https_fn.on_call(region="europe-west2", timeout_sec=540, memory=options.MemoryOption.GB_4, cpu=2, enforce_app_check=True)
def verifySelfService(req: https_fn.CallableRequest) -> Any:
    """
    Gets AWS Rekognition Liveness Results, checks for face & gender matches & explicit content, approves verification requests
    :param req: Callable request incl. user auth information - No extra params
    :return: None
    """
    if req.auth == None:
        return "Not authenticated"

    uid = req.auth.uid

    global db_conn_new

    if db_conn_new == None:
        db_conn_new = connect_with_connector_persist()

    db = firestore.client()

    verifications_collection = db.collection("verifications")

    # get user information
    user_doc_ref = db.collection("users").document(uid)
    user_doc = user_doc_ref.get()
    user_doc_data = user_doc.to_dict()
    user_gender = user_doc_data.get("gender")
    user_name = user_doc_data.get("name", '')
    user_alternative_gender = user_doc_data.get("alternativeG", False)
    gwl_admitted = user_doc_data.get("globalWlAdmitted", False)
    lwl_admitted = user_doc_data.get("localWlAdmitted", False)
    previous_verifications = user_doc_data.get("previousVerifications", [])
    user_region = user_doc_data.get("wlRegion", "")

    try:
        aws_key = access_secret_version("AWS_Rekognito_Key")
        aws_secret = access_secret_version("AWS_Rekognito_Secret_Key")

        session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)
        client = session.client('rekognition', region_name='us-east-1')

        def get_session_results(session_id):
            response = client.get_face_liveness_session_results(SessionId=session_id)

            confidence = response.get("Confidence")
            status = response.get("Status")
            reference_image = response.get("ReferenceImage").get("Bytes")

            return (status, confidence, reference_image)

        import io

        def upload_image_to_firebase(bucket_name, image_bytes, file_name):
            '''Uploads a given image (verification image from AWS) to Firebase Storage'''
            # Create a bucket instance
            from firebase_admin import storage
            bucket = storage.bucket(bucket_name)

            # Create an in-memory file-like object from the bytes
            image_stream = io.BytesIO(image_bytes)

            # Create a new blob in the bucket with the desired filename
            blob = bucket.blob(file_name)

            # Upload the image to Firebase Storage from the in-memory file
            blob.upload_from_file(image_stream,
                                  content_type='image/jpeg')  # Set the correct content type for your image

            metadata = blob.metadata or {}
            token = metadata.get("firebaseStorageDownloadTokens", None)

            if not token:
                import uuid
                token = str(uuid.uuid4())  # Generate a new token if none exists
                metadata["firebaseStorageDownloadTokens"] = token
                blob.metadata = metadata
                blob.patch()  # Save the new metadata

            # Construct the download URL with the token
            download_url = f"https://firebasestorage.googleapis.com/v0/b/{bucket.name}/o/{blob.name.replace('/', '%2F')}?alt=media&token={token}"

            return download_url

        def compare_faces(sourceFile, targetFile):
            """Calls AWS API to compare two faces for match"""
            imageSource = sourceFile
            imageTarget = targetFile

            # setting a similarity threshold of 70
            try:
                response = client.compare_faces(SimilarityThreshold=70,
                                                SourceImage={'Bytes': imageSource},
                                                TargetImage={'Bytes': imageTarget})

                logging.info(response)


            except Exception as e:
                logging.exception(e)
                response = {}

            logging.info(response)

            similarities = []

            # retrieve similarity - if no face found in either image, make it 0
            try:

                for faceMatch in response.get('FaceMatches', []):
                    position = faceMatch['Face']['BoundingBox']
                    similarity = faceMatch['Similarity']
                    similarities.append(similarity)

            except:
                similarities.append(0)

            similarities.sort()

            return similarities

        def detect_face_gender_base64(image_base64):
            """Calls AWS API that detects gender of person in image"""
            response = client.detect_faces(
                Image={'Bytes': image_base64},
                Attributes=['ALL']
            )

            gender = "None"
            confidence = 0

            for face_detail in response.get('FaceDetails', []):
                gender_dict = face_detail['Gender']
                gender = gender_dict['Value']
                confidence = gender_dict['Confidence']

                try:
                    age_range = face_detail['AgeRange']
                    high_age = age_range["High"]
                    low_age = age_range["Low"]
                    medium_age = ((high_age + low_age) / 2)
                    user_doc_ref.update({"mediumAge": medium_age})
                    logging.info(age_range)
                except Exception as e:
                    logging.exception(e)

            return gender, confidence, medium_age

        def get_image_bytes(image_url):
            import io

            # Fetch the image from the URL
            response = requests.get(image_url)
            response.raise_for_status()  # Raise HTTPError for bad responses (4xx and 5xx)

            # Open the image content with Pillow
            image = Image.open(io.BytesIO(response.content))

            print(image)

            if image.width * image.height > 16000 * 16000:  # Prevent huge images
                raise ValueError("Image dimensions too large")

            # Convert the image to JPEG bytes
            jpeg_bytes = io.BytesIO()
            image.convert("RGB").save(jpeg_bytes, format="JPEG")

            print(jpeg_bytes)

            # Reset the buffer pointer to the beginning
            jpeg_bytes.seek(0)

            return jpeg_bytes

        def text_allowed(message: str) -> bool:
            """
            Uses our custom forbidden words list against any given string
            :param message: String to detect forbidden words in
            :return: bool: Whether the string contains forbidden words
            """
            # Decode JSON list of forbidden words
            forbidden_words_list = ["Mommy", "Mummy", "Momy", "Mamy", "Mami", "Mistress", "Mistres", "Misstress",
                                    "Misstres", "Mistrress", "Dommy", "Domy", "Dommie", "Dommymommy", "Dominatrix",
                                    "Domina", "Cock", "Penis", "Pussy", "Balls"]
            forbidden_words = [str(word) for word in forbidden_words_list]

            # Create a regular expression from the forbidden words
            def escape_and_expand(word):
                escaped_word = re.escape(word)
                # Expanding for leet speak variations with minimal flexibility
                replacements = [
                    (r'o', r'[oO0]'),
                    (r'i', r'[iI1!]'),
                    (r's', r'[sS5$]'),
                    (r'a', r'[aA4@]'),
                    (r'e', r'[eE3]')
                ]
                for char, replacement in replacements:
                    escaped_word = re.sub(char, replacement, escaped_word)
                # Allowing non-word characters between the letters
                return r'\W*'.join(list(escaped_word))

            # Create the combined regex pattern
            pattern = '|'.join(map(escape_and_expand, forbidden_words))
            regex = re.compile(pattern, re.IGNORECASE)

            # Check if the message contains any forbidden words
            return regex.search(message)

        def moderate_string(input_string):
            """Uses Profanity Check module to detect common explicit/forbidden language in profiles"""
            result = predict([input_string]).tolist()[0]
            return result == 1

        def resize_image(image_bytes, max_size_mb=3):
            """Resizes profile images to be small enough for AWS APIs"""
            img = Image.open(image_bytes)

            print(img)

            # Calculate current size in MB
            current_size_mb = image_bytes.getbuffer().nbytes / (1024 * 1024)

            print(current_size_mb)

            if current_size_mb <= max_size_mb:
                return image_bytes

            # Calculate the scaling factor
            scale_factor = (max_size_mb / current_size_mb) ** 0.5

            # Calculate new dimensions
            new_width = int(img.width * scale_factor)
            new_height = int(img.height * scale_factor)

            # Resize the image
            resized_img = img.resize((new_width, new_height), Image.LANCZOS)

            print(resized_img)

            # Save the resized image to a BytesIO object
            output = BytesIO()
            resized_img.save(output, format=img.format, quality=85)
            resized_bytes = output.getvalue()

            # If still too large, reduce quality
            while len(resized_bytes) > max_size_mb * 1024 * 1024 and resized_img.format == 'JPEG':
                quality = int(quality * 0.9)
                output = BytesIO()
                resized_img.save(output, format='JPEG', quality=quality)
                resized_bytes = output.getvalue()

            return resized_bytes

        previous_verifications = user_doc_data.get("previousVerifications", [])

        if len(previous_verifications) == 0:
            logging.exception("no verification sessions generated")
            return

        session_id = previous_verifications[-1]

        try:
            status, confidence, reference_image = get_session_results(session_id)
        except Exception as e:
            logging.exception(e)

        # store the photo returned from AWS in the project cloud bucket and user document

        if not is_dev_environment():
            try:

                now = f"archivedVerifications/{uid}/{datetime.now().strftime('%Y%m%d%h%m%s')}"
                bucket_name_string = 'greenrocks-9abc3.appspot.com'

                veri_picture = upload_image_to_firebase(bucket_name_string, reference_image, now)
                user_doc_ref.update({"verificationPhoto": veri_picture})

            except Exception as e:
                logging.exception(e)
                user_doc_ref.update(
                    {"verificationGoingOn": False, "autoVerificationStarted": True, "verificationFailed": True,
                     "autoVeriFailedFace": True})
                return

        scammer_face = False

        # finding similarity to Verena
        try:
            image_bytes = get_image_bytes("https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/archivedVerifications%2Fw3BIUGmETWZO8Wt4dYFBUxs0vSe2%2F20250428Apr041745872719?alt=media&token=18528e82-1887-4dac-acee-ff581cafbc26")

            face_similarities = compare_faces(reference_image, image_bytes.read())

            if len(face_similarities) > 0:
                face_similarity = face_similarities[-1]
            else:
                face_similarity = 0

            if face_similarity > 80:
                scammer_face = True

        except Exception as e:
            logging.exception(e)

        # if face liveness confidence < 60
        if confidence < 60 or (user_region in ["Nigeria", "Philippines"]) or ("Allison" in user_name) or (
                "angel" in user_name.lower()) or ("queen" in user_name.lower()) or ("miss" in user_name.lower()) or scammer_face:

            if user_region == "Nigeria":

                logging.info("Nigeria Provision")

                user_doc_ref.update(
                    {"verificationGoingOn": False, "manualVerification": True, "verificationFailed": True,
                     "verificationFailReason": "Please upload your government-issued photo ID below to verify.",
                     "verificationCustomTitle": "We couldn't verify you yet",
                     "verificationCustomSubtitle": "Please upload your government-issued ID (e.g., passport).",
                     "autoVerificationStarted": False,
                     "autoVeriReclass": True})

                track_and_notify_verification_failed(uid, False, 'High Risk', 'ID Manual', language=user_doc_data.get("language", "en"))
                return

            if len(previous_verifications) > 3:
                verifications_collection.add({"userToVerify": user_doc_ref,
                                              "genderOfUserToVerify":
                                                  user_doc_data.get("gender"),
                                              "handled": False,
                                              "timeCreated": firestore.SERVER_TIMESTAMP,
                                              "name": user_doc_data.get("name"),
                                              "uid": uid,
                                              "verificationPhoto": user_doc_data.get("verification_picture"),
                                              "manualReviewOfAuto": True, "manualReviewReason": "face_realness_<60"})
                user_doc_ref.update(
                    {"verificationGoingOn": True, "manualVerification": True, "autoVerificationStarted": False,
                     "autoVeriReclass": True,  "verificationCustomTitle": "Manual Review in Progress",
                     "verificationCustomSubtitle": "Your profile has been selected for additional human review. This can take up to 48 hours, but is mostly quicker.",
                    })

                logging.info("Put into manual verification after face not real in 3 verifications")

                track_and_notify_verification_failed(uid, False, 'Face Liveness' if confidence < 60 else 'High Risk', 'Review Manual', language=user_doc_data.get("language", "en"))


            else:
                user_doc_ref.update(
                    {"verificationGoingOn": False, "autoVerificationStarted": True, "verificationFailed": True,
                     "autoVeriFailedFace": True,  "verificationCustomTitle": "We couldn't verify you yet, please try again.",
                     "verificationFailReason":  "Please ensure you are in a well-lit room and that your face is clearly visible and matches the face on the pictures on your profile.",
                     "verificationCustomSubtitle": "Please ensure you are in a well-lit room and that your face is clearly visible and matches the face on the pictures on your profile.",
                    })

                logging.info("Try again after face not real")

                track_and_notify_verification_failed(uid, False, 'Face Liveness' if confidence < 60 else 'High Risk', 'Retry Auto', language=user_doc_data.get("language", "en"))

            return

        # get bio and prompts from user document
        user_public_doc_ref = user_doc_data.get("publicProfile")
        user_public_doc = user_public_doc_ref.get()
        user_public_data = user_public_doc.to_dict()

        bio = user_public_data.get("bio", "")
        prompts = user_public_data.get("prompts", [])
        promptsEdited = ". ".join(prompt.get('answer', '') for prompt in prompts if 'answer' in prompt)
        images = user_public_doc.get("nPictures")

        risky_images = []
        face_similarities_full = []
        risky_bio = False
        gender_okay = False
        bucket_name = 'default'

        for i in images:
            image_doc = i.get()
            image_url = image_doc.get("url")
            bucket_name, image_uri = convert_firebase_url_to_gs(image_url)
            image_classification = checkExplicitnessCore(image_doc, uid)

            if image_classification == True:
                logging.info("Risky images found")
                risky_images.append(i)
                break

        if len(risky_images) > 0:
            logging.info("risky images")
            verifications_collection.add({"userToVerify": user_doc_ref,
                                          "genderOfUserToVerify":
                                              user_doc_data.get("gender"),
                                          "handled": False,
                                          "timeCreated": firestore.SERVER_TIMESTAMP,
                                          "name": user_doc_data.get("name"),
                                          "uid": uid,
                                          "verificationPhoto": user_doc_data.get("verification_picture"),
                                          "manualReviewOfAuto": True, "manualReviewReason": "risky_images"})

            user_doc_ref.update(
                {"verificationGoingOn": True, "manualVerification": True, "autoVerificationStarted": False,
                 "autoVeriReclass": True,  "verificationCustomTitle": "Manual Review in Progress",
                     "verificationCustomSubtitle": "Your profile has been selected for additional human review. This can take up to 48 hours, but is mostly quicker.",
                    })

            track_and_notify_verification_failed(uid, False, 'Images', 'Review Manual', language=user_doc_data.get("language", "en"))

            return

        for i in images:
            image_doc = i.get()
            image_url = image_doc.get("url")
            bucket_name, image_uri = convert_firebase_url_to_gs(image_url)

            try:
                image_bytes = get_image_bytes(image_url)
            except Exception as e:
                logging.exception(e)

                verifications_collection.add({"userToVerify": user_doc_ref,
                                              "genderOfUserToVerify":
                                                  user_doc_data.get("gender"),
                                              "handled": False,
                                              "timeCreated": firestore.SERVER_TIMESTAMP,
                                              "name": user_doc_data.get("name"),
                                              "uid": uid,
                                              "verificationPhoto": user_doc_data.get("verification_picture"),
                                              "manualReviewOfAuto": True, "manualReviewReason": "faulty_images"})


                if len(previous_verifications) >= 2:
                    user_doc_ref.update(
                        {"verificationGoingOn": True, "manualVerification": True, "autoVerificationStarted": False,
                         "autoVeriReclass": True, "verificationCustomTitle": "Manual Review in Progress",
                         "verificationCustomSubtitle": "Your profile has been selected for additional human review. This can take up to 48 hours, but is mostly quicker.",
                         })

                user_doc_ref.update(
                    {"verificationGoingOn": True, "manualVerification": True, "autoVerificationStarted": False,
                     "autoVeriReclass": True,  "verificationCustomTitle": "Manual Review in Progress",
                     "verificationCustomSubtitle": "Your profile has been selected for additional human review. This can take up to 48 hours, but is mostly quicker.",
                    })

                track_and_notify_verification_failed(uid, False, 'Images', 'Review Manual', language=user_doc_data.get("language", "en"))

                return

            print(image_bytes)

            print(image_bytes.getvalue())

            image_bytes.seek(0)

            face_similarities = compare_faces(reference_image, image_bytes.read())

            print(face_similarities)

            if len(face_similarities) > 0:
                face_similarity = face_similarities[-1]
            else:
                face_similarity = 0
            face_similarities_full.append(face_similarity)
            if face_similarity > 50:
                break

        face_similarities_full.sort()

        if face_similarities_full[-1] < 50:
            user_doc_ref.update(
                {"verificationGoingOn": False, "autoVerificationStarted": True, "verificationFailed": True,
                 "autoVeriFailedFace": True,  "verificationCustomTitle": "We couldn't verify you yet, please try again.",
                     "verificationCustomSubtitle": "Please ensure you are in a well-lit room and that your face is clearly visible and matches the face on the pictures on your profile.",
                    })

            track_and_notify_verification_failed(uid,
                              False, 'Face Match', 'Retry Auto', language=user_doc_data.get("language", "en"))
            return

        scannable_content = " ".join([bio, promptsEdited])

        if moderate_content(scannable_content) == False:
            risky_bio = True

        if risky_bio:
            user_doc_ref.update(
                {"verificationGoingOn": False, "autoVerificationStarted": True, "verificationFailed": True,
                 "autoVeriFailedBio": True})

            track_and_notify_verification_failed(uid, False, 'Bio/Prompts', 'Retry Auto', language=user_doc_data.get("language", "en"))

            return

        gender, gender_confidence, medium_age = detect_face_gender_base64(reference_image)

        if (medium_age < 18 and user_region == 'United Kingdom'):
            logging.info("potentially minor")

            user_doc_ref.update(
                {"verificationGoingOn": False, "manualVerification": True, "verificationFailed": True,
                 "verificationFailReason": "We are unsure about your age. Please upload your government-issued photo ID below.",
                 "verificationCustomTitle": "We couldn't verify your age",
                 "verificationCustomSubtitle": "Please upload your government-issued ID (e.g., passport) for age verification",
                 "autoVerificationStarted": False,
                 "autoVeriReclass": True})

            track_and_notify_verification_failed(uid, False, 'Age', 'ID Manual', language=user_doc_data.get("language", "en"))

            return

        if (gender_confidence > 60 and gender == user_gender) or user_alternative_gender:
            if user_gender == "Female":
                profileSetUpWomen(uid, db_conn_new)


            elif user_gender == "Male":

                # relevant for old app versions, deprecated
                def addNewUserWaitingList(uid) -> Any:
                    # initialize firestore
                    db = firestore.client()
                    # set user doc ref and get user doc, extract data and from data region
                    user_doc_ref = db.collection("users").document(uid)
                    user_doc = user_doc_ref.get()
                    user_doc_data = user_doc.to_dict()
                    user_region = user_doc_data['wlRegion']


                    # get document for user's location
                    wl_region_ref = db.collection("waitingList").document(user_region)
                    transaction = db.transaction()

                    @firestore.transactional
                    def update_in_transaction(transaction, wl_region_ref):
                        # get master doc for that region
                        master_wl_region_doc = wl_region_ref.get(transaction=transaction)

                        users = master_wl_region_doc.to_dict().get("users", [])

                        users.append({"reference": user_doc_ref, "sublevel": "100"})

                        transaction.update(wl_region_ref, {"users": users})

                        return len(users)

                    index_of_user = update_in_transaction(transaction, wl_region_ref)

                    def calculate_interval_range(position):
                        """
                        Calculate the interval range for a given position based on specified ranges.

                        Args:
                        - position (int): The position of an item in the array.

                        Returns:
                        - str: The interval range in which the position falls, in the format "start-end".
                        """
                        position = position * 5
                        if position < 500:
                            interval_start = (position // 100) * 100
                            interval_end = interval_start + 100
                        elif position < 2500:
                            interval_start = ((position - 500) // 250) * 250 + 500
                            interval_end = interval_start + 250
                        elif position < 5000:
                            interval_start = ((position - 2500) // 500) * 500 + 2500
                            interval_end = interval_start + 500
                        else:
                            interval_start = ((position - 5000) // 1000) * 1000 + 5000
                            interval_end = interval_start + 1000

                        return f"{1 if interval_start == 0 else interval_start}-{interval_end}"

                    user_interval = calculate_interval_range(index_of_user)

                    region_collection = db.collection("openRegions")
                    his_region_query = region_collection.where("name", "==", user_region)
                    his_region = his_region_query.get()

                    user_doc_ref.update(
                        {"onWaitingList": True,
                         "waitingListInterval": "Wait for matching" if len(his_region) == 0 else user_interval,
                         "regionOpen": True if len(his_region) > 0 else False,
                         "trueFirstInterval": user_interval})

                    try:
                        u_location = user_doc_data.get('location')
                        u_lat = u_location.latitude
                        u_long = u_location.longitude

                        here_key = access_secret_version("Here_Key")

                        url = f"https://revgeocode.search.hereapi.com/v1/revgeocode?at={u_lat},{u_long}&lang=en-US&apiKey={here_key}"

                        payload = {}
                        headers = {}

                        response = requests.request("GET", url, headers=headers, data=payload)

                        try:
                            country_name = response.json().get("items", {})[0].get("address").get("countryName")
                            state = response.json().get("items", {})[0].get("address").get("state")
                            city = response.json().get("items", {})[0].get("address").get("city")
                        except:
                            country_name = "Unknown"
                            state = "Unknown"
                            city = "Unknown"

                        identify_obj1 = Identify()
                        identify_obj1.set("Country_EN", country_name)
                        amplitude_client.identify(identify_obj1, EventOptions(user_id=uid))

                        identify_obj2 = Identify()
                        identify_obj2.set("State_EN", state)
                        amplitude_client.identify(identify_obj2, EventOptions(user_id=uid))

                        identify_obj3 = Identify()
                        identify_obj3.set("City_EN", city)
                        amplitude_client.identify(identify_obj3, EventOptions(user_id=uid))

                        identify_obj = Identify()
                        identify_obj.set("Initial Position Regional Wl", user_interval)
                        amplitude_client.identify(identify_obj, EventOptions(user_id=uid))

                        identify_obj4 = Identify()
                        identify_obj4.set("On Regional Waiting List", True)
                        amplitude_client.identify(identify_obj4, EventOptions(user_id=uid))

                        identify_obj5 = Identify()
                        identify_obj5.set("Sign Up Finished", True)
                        amplitude_client.identify(identify_obj5, EventOptions(user_id=uid))

                        event = BaseEvent(event_type="Waiting List: Put on Regional Waiting List", user_id=uid,
                                          event_properties={
                                              "Initial Position Regional Wl": user_interval
                                          })

                        amplitude_client.track(event)

                        user_doc_ref.update({'city': city, 'country': country_name, 'state': state})
                        user_doc_data.get('publicProfile').update({'city': city})
                    except:
                        print("Amplitude failed")

                    return

                if gwl_admitted or lwl_admitted:
                    profileSetUpMen(uid, db_conn_new)
                    rc_secret = access_secret_version("Revenuecat_Key")
                    session = create_retry_session()
                    time_to_end = datetime(2099, 12, 31)

                    grant_promotional_access_rc("standard_access", time_to_end, uid, rc_secret, session)
                    grant_promotional_access_rc("free_standard_lifetime", time_to_end, uid, rc_secret, session)

                else:
                    addNewUserWaitingList(uid)

            track_and_notify_verification_successful(uid, False)
            user_doc_ref.update(
                {"verificationGoingOn": False, "verified": True})

            return

        if not gender_okay:
            verifications_collection.add({"userToVerify": user_doc_ref,
                                          "genderOfUserToVerify":
                                              user_doc_data.get("gender"),
                                          "handled": False,
                                          "timeCreated": firestore.SERVER_TIMESTAMP,
                                          "name": user_doc_data.get("name"),
                                          "uid": uid,
                                          "verificationPhoto": user_doc_data.get("verification_picture"),
                                          "manualReviewOfAuto": True, "manualReviewReason": "gender mismatch"})

            user_doc_ref.update(
                {"verificationGoingOn": True, "manualVerification": True, "autoVerificationStarted": False,
                 "autoVeriReclass": True,  "verificationCustomTitle": "Manual Review in Progress",
                     "verificationCustomSubtitle": "Your profile has been selected for additional human review. This can take up to 48 hours, but is mostly quicker.",
                    })

            track_and_notify_verification_failed(uid,False,'Gender Match', 'Review Manual', language=user_doc_data.get("language", "en"))

            return

    except Exception as e:
        logging.exception(e)
        verifications_collection.add({"userToVerify": user_doc_ref,
                                      "genderOfUserToVerify":
                                          user_doc_data.get("gender"),
                                      "handled": False,
                                      "timeCreated": firestore.SERVER_TIMESTAMP,
                                      "name": user_doc_data.get("name"),
                                      "uid": uid,
                                      "verificationPhoto": user_doc_data.get("verification_picture"),
                                      "manualReviewOfAuto": True, "manualReviewReason": "technical issue"})

        user_doc_ref.update(
            {"verificationGoingOn": True, "manualVerification": True, "autoVerificationStarted": False,
             "autoVeriReclass": True, "verificationCustomTitle": "Manual Review in Progress",
             "verificationCustomSubtitle": "Your profile has been selected for additional human review. This can take up to 48 hours, but is mostly quicker.",
             })


@https_fn.on_call(region="europe-west1", timeout_sec=540, memory=options.MemoryOption.GB_4, cpu=2, enforce_app_check=(not is_dev_environment))
def verifySelfServiceAdvanced(req: https_fn.CallableRequest) -> Any:
    """
    Gets AWS Rekognition Liveness Results, checks for face & gender matches & explicit content, approves verification requests
    :param req: Callable request incl. user auth information - No extra params
    :return: None
    """
    if req.auth == None:
        return "Not authenticated"

    uid = req.auth.uid

    db = firestore.client()

    global db_conn_new

    if db_conn_new == None:
        db_conn_new = connect_with_connector_persist()

    # get user information
    user_doc_ref = db.collection("users").document(uid)
    user_doc = user_doc_ref.get()
    user_doc_data = user_doc.to_dict()
    user_gender = user_doc_data.get("gender")
    user_name = user_doc_data.get("name", '')
    user_alternative_gender = user_doc_data.get("alternativeG", False)
    gwl_admitted = user_doc_data.get("globalWlAdmitted", False)
    lwl_admitted = user_doc_data.get("localWlAdmitted", False)
    previous_verifications = user_doc_data.get("previousVerifications", [])
    user_region = user_doc_data.get("wlRegion", "")

    try:
        aws_key = access_secret_version("AWS_Rekognito_Key")
        aws_secret = access_secret_version("AWS_Rekognito_Secret_Key")

        session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)
        client = session.client('rekognition', region_name='us-east-1')

        if len(previous_verifications) == 0:
            logging.exception("no verification sessions generated")
            return

        session_id = previous_verifications[-1]

        try:
            image_bytes, image_url, image_certainty = aws_rekognition_query_results(client, session_id, uid)
        except Exception as e:
            logging.exception(e)

        # get public profile and full current nPictures array
        public_profile = db.collection('users').document(uid).collection("publicProfile").limit(1).get()[
            0].to_dict()
        n_pictures_raw = public_profile.get("nPictures")
        n_pictures = [n.id for n in n_pictures_raw]  # gets the ids as needed for comp functions

        # show loading screen in between
        compliance_images = get_current_compliance_images(uid)

        # get face and explicitness

        face_images, explicit_images = has_face_explicitness(compliance_images)

        # decision rule: approve user automatically or disapproving them with cause
        if face_images and not explicit_images:
            approve_image_verification(uid)
        else:


            if len(previous_verifications) > 3:


                user_doc_ref.update(
                    {"verificationGoingOn": True, "manualVerification": True, "autoVerificationStarted": False,
                     "autoVeriReclass": False, "imageVeriHumanReviewInProgress": True, "manualVerificationReason": "Images",
                     "verificationCustomTitle": "Manual Review in Progress",
                     "verificationCustomSubtitle": "Your profile has been selected for additional human review. This can take up to 48 hours, but is mostly quicker.",
                     })

                track_and_notify_verification_failed(uid, False, 'Images', 'Review Manual', language=user_doc_data.get("language", "en"))



            else:

                disapprove_image_verifiation(uid, n_pictures, explicit_images, face_images, force_update_decriptions=True)

                logging.info("risky images")

                user_doc_ref.update(
                    {"verificationGoingOn": False, "manualVerification": False, "autoVerificationStarted": True,
                     "autoVeriReclass": False, "verificationFailed": True, "autoVeriFailedFace": True,
                      })

                track_and_notify_verification_failed(uid, False, 'Images', 'Retry Auto', language=user_doc_data.get("language", "en"))



            return

        # determine user's passing of all other criteria except image rules
        image_liveness = image_certainty >= 70
        risky = is_high_risk_region_name(user_region, user_name)
        face_match = check_face_comparison(client, image_bytes, uid)
        gender_match, age_match = check_gender_age(client, image_bytes, user_gender)

        scammer_face = False

        # finding similarity to Verena
        try:

            scammer_images = ["https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/archivedVerifications%2Fw3BIUGmETWZO8Wt4dYFBUxs0vSe2%2F20250428Apr041745872719?alt=media&token=18528e82-1887-4dac-acee-ff581cafbc26", "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/archivedVerifications%2FjrokHuvj1keYISSvk5JhvNnruK33%2F20250427Apr041745792829?alt=media&token=8a3681a4-54e4-4e34-ae1b-16f0bef9ca88",
                              "https://firebasestorage.googleapis.com/v0/b/greenrocks-s4p/o/1748022207.364194_8mSihjMxIm.jpeg?alt=media&token=042906d1-f6a2-4894-b031-dd5fb0f81704",
                              "https://firebasestorage.googleapis.com/v0/b/greenrocks-s4p/o/1746857437.199464_40Kp3Oe4De.jpeg?alt=media&token=6802dffe-40f7-490d-9517-9af3d292d0e9", "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/archivedVerifications%2FEQulLWq4NsWH5P2n3GNT526ljtA2%2F20250430Apr041746040741?alt=media&token=b73ed1cf-3570-45ec-93bf-83c1aacf9e53"]

            for s in scammer_images:
                image_bytes = get_image_bytes(
                    s
                    )

                scammer_similarity = check_face_comparison(client, image_bytes, uid)

                if scammer_similarity:
                    scammer_face = True

        except Exception as e:
            logging.exception(e)

        # todo: put potential minors into new id verification loop
        if (not age_match and user_region == 'United Kingdom'):
            logging.info("potentially minor")

            user_doc_ref.update(
                {"verificationGoingOn": False, "manualVerification": True, "verificationFailed": True, "idVerification": True,
                 "verificationFailReason": "We are unsure about your age. Please upload your government-issued photo ID below.",
                 "autoVerificationStarted": False, "verificationCustomTitle": "Please upload an image of your photo ID",
                     "verificationCustomSubtitle": "We are unsure about your age. Please upload an image of your government-issued photo ID (e.g., passport).",
                 "autoVeriReclass": True, "manualVerificationReason": "Age"})

            track_and_notify_verification_failed(uid, False, 'Age', 'ID Manual', language=user_doc_data.get("language", "en"))

            return

        # put risky users directly into manual verification
        if risky:
            user_doc_ref.update(
                {"verificationGoingOn": True, "manualVerification": True, "autoVerificationStarted": False,
                 "autoVeriReclass": True, "manualVerificationReason": "High Risk", "verificationCustomTitle": "Manual Review in Progress",
                     "verificationCustomSubtitle": "Your profile has been selected for additional human review. This can take up to 48 hours, but is mostly quicker.",
                     })

            track_and_notify_verification_failed(uid, False, 'High Risk', 'Review Manual', language=user_doc_data.get("language", "en"))

            return

        # for liveness, gender or face match concerns, allow up to 3 retries, then put into manual automatically
        if (not image_liveness) or (not (gender_match or user_alternative_gender)) or (not face_match) or scammer_face:

            if len(previous_verifications) > 2:

                update_dict = {"verificationGoingOn": True, "manualVerification": True, "autoVerificationStarted": False,
                     "autoVeriReclass": True, "manualVerificationReason": "Gender Match" if not gender_match else "Face Match" if not face_match else "Image Liveness","verificationCustomTitle": "Manual Review in Progress",
                     "verificationCustomSubtitle": "Your profile has been selected for additional human review. This can take up to 48 hours, but is mostly quicker.",
                     }

                if scammer_face:
                    update_dict['scammerFace'] = scammer_face

                user_doc_ref.update(
                    update_dict
                    )

                logging.info("Put into manual verification after face not real in 3 verifications")

                track_and_notify_verification_failed(uid, False,
                                                     'Face Liveness' if not image_liveness else 'Face Match' if not face_match else 'Gender Match', 'Review Manual', language=user_doc_data.get("language", "en"))


            else:
                user_doc_ref.update(
                    {"verificationGoingOn": False, "autoVerificationStarted": True, "verificationFailed": True,
                     "autoVeriFailedFace": True, "verificationCustomTitle": "We could not verify you yet",
                     "verificationCustomSubtitle": "Please try again and ensure you are in a well-lit room, your face is clearly visible and you have at least one clear face picture of yourself on your profile.",
                     })

                logging.info("Try again after face not real")

                track_and_notify_verification_failed(uid, False,
                                                     'Face Liveness' if not image_liveness else 'Face Match' if not face_match else 'Gender Match', 'Retry Auto', language=user_doc_data.get("language", "en"))

            return

        track_and_notify_verification_successful(uid, False)

        if user_gender == "Female":
            profileSetUpWomen(uid, db_conn_new)


        elif user_gender == "Male":

            # relevant for old app versions, deprecated

            profileSetUpMen(uid, db_conn_new)
            rc_secret = access_secret_version("Revenuecat_Key")
            session = create_retry_session()
            time_to_end = datetime(2099, 12, 31)

            grant_promotional_access_rc("standard_access", time_to_end, uid, rc_secret, session)
            grant_promotional_access_rc("free_standard_lifetime", time_to_end, uid, rc_secret, session)


        current_compliance_images = get_current_compliance_images(uid)
        face_images, own_face_images, explicit_images = has_ownface_face_explicitness(current_compliance_images)

        user_doc_ref.update(
            {"verificationGoingOn": False, "verified": True, "verificationCustomTitle": "Congratulations, you are verified",
                     "verificationCustomSubtitle": "Thank you for helping us keep Chyrpe safe & welcoming for all.",
                     "advancedVerificationEligible": len(own_face_images) >= 3
                     })
        
        user_doc_data.get('publicProfile').update({'verified': True})

        return

    except Exception as e:
        logging.exception(e)

        user_doc_ref.update(
            {"verificationGoingOn": True, "manualVerification": True, "autoVerificationStarted": False,
             "autoVeriReclass": False, "imageVeriHumanReviewInProgress": True, "manualVerificationReason": "User Request",
             "verificationCustomTitle": "Manual Review in Progress",
             "verificationCustomSubtitle": "Your profile has been selected for additional human review. This can take up to 48 hours, but is mostly quicker.",
             })

        track_and_notify_verification_failed( uid, False, 'General/Technical', 'Review Manual', language=user_doc_data.get("language", "en"))

        return

def checkExplicitnessCore(image, uid):
    """
    Checks explicitness of a given image. Accepts an image document.

    :return: bool indicating if the image is marked as explicit
    """
    image_url = image.get("url")

    explicit = False

    # convert image URL to storage address
    from pydantic import BaseModel
    from openai import AzureOpenAI, BadRequestError

    client = AzureOpenAI(
        azure_endpoint="https://chyrpegptp.openai.azure.com/",
        api_key=access_secret_version("OpenAI_Key"),
        api_version="2024-10-21"
    )

    class LanguageEvaluation(BaseModel):
        input_text: str
        evaluation_result: int

    try:
        # Call the GPT model with the schema
        completion = client.beta.chat.completions.parse(
            model="gpt-4o-mini",  # replace with the actual model deployment name
            messages=[
                {
                    "role": "system",
                    "content": """Evaluate if this image contains explicit content. Strictly return one number only. 1 if explicit, 0 if not."""
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {"url": image_url}
                        }]}],
            response_format=LanguageEvaluation,  # Using the schema here
        )

        # Parse and print the result
        evaluation = completion.choices[0].message.to_dict().get("parsed").get("evaluation_result")

        logging.info(evaluation)

        if evaluation == 0 or evaluation == 2:
            explicit = False
        else:
            explicit = True

    except Exception as e:
        logging.error(e)
        explicit = True

    return explicit


def moderateFaceCore(verification_image, image_url, uid, image_id):
    """
    Compares if an image contains a different face from the verified user's.

    :return: bool indicating if the image contains a different face
    """
    aws_key = access_secret_version("AWS_Rekognito_Key")
    aws_secret = access_secret_version("AWS_Rekognito_Secret_Key")

    session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)
    client = session.client('rekognition', region_name='eu-west-1')

    def compare_faces(sourceFile, targetFile):
        """Calls AWS API to compare two faces for match"""
        imageSource = sourceFile
        imageTarget = targetFile

        # setting a similarity threshold of 70
        try:
            response = client.compare_faces(SimilarityThreshold=70,
                                            SourceImage={'Bytes': imageSource},
                                            TargetImage={'Bytes': imageTarget})

            print(response)

        except Exception as e:
            logging.exception(e)
            response = {}

        similarities = []

        # retrieve similarity - if no face found in either image, make it 0
        try:

            for faceMatch in response.get('FaceMatches', []):
                position = faceMatch['Face']['BoundingBox']
                similarity = faceMatch['Similarity']
                similarities.append(similarity)

            for faceUnmatch in response.get('UnmatchedFaces', []):
                similarities.append(0)

        except:
            similarities.append(0)

        similarities.sort()

        return similarities

    face_similarities = compare_faces(get_image_bytes(verification_image), get_image_bytes(image_url))

    if len(face_similarities) > 0:
        face_similarity = face_similarities[-1]
    else:
        logging.warning(f"No faces recognised in photo of {uid}, photo id {image_id}")
        # If no faces recognised no need to monitor
        return False

    if face_similarity < 60:
        logging.warning(f"Face similarity too low for photo of {uid}, photo id {image_id}")

    return face_similarity < 60


@with_retry()
def changeUserBlockStatusSql(uid: str, block: bool):
    """
    Sets the `mpeBlock` column in the User table
    """
    db = firestore.client()
    try:
        connector = Connector(refresh_strategy="lazy")
        pool = connect_with_connector(connector)

        block_text = "TRUE" if block else "FALSE"

        with pool.connect() as db_conn:
            stmt = sqlalchemy.text(f"""
                UPDATE "User" 
                SET mpeBlock = :block_text
                WHERE user_id = :user_id;"""
                                   )

            db_conn.execute(stmt, {
                'block_text': block_text,
                'user_id': uid
            })
            db_conn.commit()

        pool.dispose()
        connector.close()

    except Exception as e:
        logging.exception(e)
        db.collection('SQLExceptions').add({
            'function': 'changeUserBlockStatusSql',
            'user': uid,
            'error': str(e),
            'time': firestore.SERVER_TIMESTAMP
        })


@on_document_written(region="europe-west1", memory=options.MemoryOption.GB_2,
                     document="users/{userId}/images/{imageDoc}")
def imageCheck(event: Event[Change[DocumentSnapshot]]) -> None:
    """Triggered cloud function to run once an images document is changed in any way. This is the entry point for automated detection."""

    if not event.data.after:
        logging.info("Image deleted")
        return

    new_value = event.data.after.to_dict() if event.data.after != None else {}
    old_value = event.data.before.to_dict() if event.data.before != None else {}

    if old_value.get('url') == new_value.get('url'):
        logging.info(f"No image actually changed")
        return

    db = firestore.client()
    image_url = new_value.get("url", '')

    uid = event.params["userId"]
    image_id = event.params["imageDoc"]

    mpe_quota_collection = db.collection("mpeQuota")

    # Get the current UTC date and time
    yesterday_date = datetime.now()

    # Format the date as "YYYYMMDD"
    formatted_date = yesterday_date.strftime("%Y%m%d")

    # Get remaining mpe block quota for the day
    mpe_quota_doc = mpe_quota_collection.document("quota").get()
    mpe_today_doc = mpe_quota_collection.document(formatted_date).get()

    quota = mpe_quota_doc.get("quota")
    blocks_made = mpe_today_doc.get("blocksCount") if mpe_today_doc.exists else 0

    remaining_quota: int = quota - blocks_made

    current_user = mpe_user_watcher.FirestoreUserWatcher(uid)

    if not current_user.get_latest_data().get("signUpFinished"):
        logging.info("Sign Up not yet finished")
        current_user.stop_listener()
        return

    if current_user.get_latest_data().get("verificationV3"):
        logging.info("verificationV3")
        current_user.stop_listener()
        return

    # Fetch latest synced data
    data = current_user.get_latest_data()

    image_scan = mpe_helper.imageScan(uid, image_id)
    has_face = mpe_helper.faceOnProfile(uid, current_user.get_latest_snapshot())

    if current_user.wait_for_update(timeout=1):  # Waits up to 5 seconds
        logging.info("✅ Update received!")
    else:
        logging.info("⚠️ No update received within timeout.")

    logging.info(f"Has face: {has_face}")

    if image_scan.explicit:
        logging.info(f"safe add block {image_id}")
        mpe_helper.safeAddImageMpeBlock(uid, image_id)
    else:
        mpe_helper.safeRemoveImageMpeBlock(uid, current_user.get_latest_snapshot(), image_id)

    if current_user.get_latest_data()["mpeNoFace"] != (not has_face):
        db.collection("users").document(uid).update({"mpeNoFace": not has_face})

    if current_user.wait_for_update(timeout=1):  # Waits up to 5 seconds
        logging.info("✅ Update received!")
    else:
        logging.info("⚠️ No update received within timeout.")

    # Fetch latest synced data
    data = current_user.get_latest_data()
    logging.info("Latest synced data:", data)

    logging.info(f"Latest snapshot: {current_user.get_latest_snapshot().to_dict()}")

    blockers, blockers_content = mpe_helper.checkBlockers(current_user.get_latest_snapshot())

    logging.info(blockers_content)
    logging.info(blockers)

    if (blockers.total) and (not blockers.prompts) and (not blockers.attributes) and (not blockers.images) and (
            blockers.no_face):
        if remaining_quota <= 0:
            blockers.total = False

    if blockers.total:
        logging.info("reject")
        mpe_helper.process_profile_rejection(uid, blockers, blockers_content)
    elif not blockers.total and current_user.get_latest_data()["mpeBlock"] == True:
        logging.info("accept")
        mpe_helper.process_profile_acceptance(uid)
    else:
        logging.info("already alright")

    current_user.stop_listener()

    return


@on_document_created(region="europe-west1", memory=options.MemoryOption.GB_4,
                     document="moderatorApproveProfiles/{docId}", concurrency=10)
def moderatorApproveProfile(event: Event[Change[DocumentSnapshot]]) -> None:
    db = firestore.client()
    new_value = event.data.to_dict()
    uid = new_value.get("uid")
    own_face_images = new_value.get("ownFaceImages", [])
    if not uid:
        logging.error("No uid provided in moderatorApproveProfile")
        return
    db.collection("users").document(uid).update({
        "mpeReviewInProgress": False,
        "mpeBlock": False,
        "mpeNoFace": False,
        "mpeImprovements.images": [],
        "mpeImprovements.prompts": [],
        "mpeImprovements.attributes": []
    })

    user_reference = db.collection("users").document(uid)

    public_profile = user_reference.collection("publicProfile").limit(1).get()[0].to_dict()
    n_pictures = public_profile.get("nPictures", [])

    for n in n_pictures:
        
        image_reference_update = {'explicit': False}
        image_compliance_update = {f'currentImages.{n.id}.explicit': False}

        if own_face_images:
            image_reference_update['ownFace'] = n.id in own_face_images
            image_compliance_update[f'currentImages.{n.id}.ownFace'] = n.id in own_face_images

        user_reference.collection("images").document(n.id).update(image_reference_update)
        user_reference.collection('meta').document('imageCompliance').update(image_compliance_update)

    changeUserBlockStatusSql(uid, False)


@on_document_created(region="europe-west1", memory=options.MemoryOption.GB_4,
                     document="moderatorRejectProfiles/{docId}", concurrency=10)
def moderatorRejectProfile(event: Event[Change[DocumentSnapshot]]) -> Any:
    db = firestore.client()
    new_value = event.data.to_dict()
    uid = new_value.get("uid")
    if not uid:
        logging.error("No uid provided in moderatorRejectProfile")
        return
    marked_image_ids: list[str] = new_value.get("markedImageIds", [])
    marked_prompt_ids: list[str] = new_value.get("markedPromptIds", [])
    marked_attribute_ids: list[str] = new_value.get("markedAttributeIds", [])
    no_face: bool = new_value.get("noFace", False)

    if len(marked_image_ids) == 0 and len(marked_prompt_ids) == 0 and len(marked_attribute_ids) == 0 and not no_face:
        return

    try:
        user_doc = db.collection("users").document(uid).get()
        public_profile = user_doc.get("publicProfile").get().to_dict()

        restricted_content_list = []

        for a in marked_attribute_ids:
            restricted_content_list.append(public_profile.get(a))
        for p in marked_prompt_ids:
            p_number = int(p[-1])
            print(marked_prompt_ids)
            restricted_content_list.append(public_profile.get("prompts")[p_number]["answer"])
        db.collection("moderatorRejectProfiles").document(event.params["docId"]).update(
            {"restrictedContent": restricted_content_list, "timestamp": firestore.SERVER_TIMESTAMP})
    except Exception as e:
        logging.exception(e)

    # NOTE: this function should return all fields for `mpeDisplay`
    # pass as parameters `displayTitle`, `displayBody` & `displayBtn`
    display_title = new_value.get("displayTitle")
    display_body = new_value.get("displayBody")
    display_btn = new_value.get("displayBtn")

    if not display_title:
        logging.error("No display reason provided in moderatorRejectProfile")
        return

    db.collection("users").document(uid).update({
        "mpeReviewInProgress": False,
        "mpeBlock": True,
        "mpeNoFace": no_face,
        "mpeImprovements.images": marked_image_ids,
        "mpeImprovements.prompts": marked_prompt_ids,
        "mpeImprovements.attributes": marked_attribute_ids,
        "mpeDisplay.title": display_title,
        "mpeDisplay.body": display_body,
        "mpeDisplay.infoBtn": display_btn,
    })

    if not is_dev_environment():
        changeUserBlockStatusSql(uid, True)

    user_reference = db.collection("users").document(uid)

    for m in marked_image_ids:
        user_reference.collection("images").document(m).update({'explicit': True})
        user_reference.collection('meta').document('imageCompliance').update({f'currentImages.{m}.explicit': True})


@on_document_written(region="europe-west1", memory=options.MemoryOption.GB_2,
                     document="users/{userId}/publicProfile/{publicProfileId}")
def removeFlagForDeletedPicture(event: Event[Change[DocumentSnapshot]]) -> None:
    """
    Handles changes to a user's public profile, particularly image changes.
    Implements auto-acquittal logic by:
    1. Checking if flagged images were deleted/changed
    2. Verifying face presence in remaining images
    3. Checking if any other blocking conditions remain (prompts, attributes)
    4. Removing block if all conditions are satisfied
    
    Note: This function handles document creation, updates, and deletions
    """
    db = firestore.client()
    uid = event.params["userId"]

    # Handle document deletion
    if not event.data.after:
        logging.info(f"Public profile deleted for user {uid}")
        return

    # Get the new and old pictures, handling document creation case
    new_n_pictures = event.data.after.to_dict().get('nPictures', []) if event.data.after else []
    old_n_pictures = event.data.before.to_dict().get('nPictures', []) if event.data.before else []

    # If no pictures in either state, nothing to process
    if not new_n_pictures and not old_n_pictures:
        logging.info(f"No pictures to process for user {uid}")
        return

    new_picture_ids = [n.id for n in new_n_pictures]
    old_picture_ids = [o.id for o in old_n_pictures]

    if not len(new_picture_ids) < len(old_picture_ids):
        logging.info(f"No images deleted for user {uid}")
        return

    current_user = mpe_user_watcher.FirestoreUserWatcher(uid)

    if not current_user.get_latest_data().get("signUpFinished"):
        logging.info("Sign Up not yet finished")
        current_user.stop_listener()
        return

    if current_user.get_latest_data().get("verificationV3"):
        logging.info("verificationV3")
        current_user.stop_listener()
        return

    deleted_images = [i for i in old_picture_ids if i not in new_picture_ids]

    if len(deleted_images) > 0:
        mpe_helper.safeRemoveImageMpeBlock(uid, current_user.get_latest_snapshot(), deleted_images[0])

    if current_user.wait_for_update(timeout=1):  # Waits up to 5 seconds
        logging.info("✅ Update received!")
    else:
        logging.info("⚠️ No update received within timeout.")

    # Fetch latest synced data
    data = current_user.get_latest_data()
    logging.info("Latest synced data:", data)

    has_face = mpe_helper.faceOnProfile(uid, current_user.get_latest_snapshot())

    if current_user.wait_for_update(timeout=1):  # Waits up to 5 seconds
        logging.info("✅ Update received!")
    else:
        logging.info("⚠️ No update received within timeout.")

    logging.info(f"Has face: {has_face}")

    if current_user.get_latest_data()["mpeNoFace"] != (not has_face):
        db.collection("users").document(uid).update({"mpeNoFace": not has_face})

    if current_user.wait_for_update(timeout=1):  # Waits up to 5 seconds
        logging.info("✅ Update received!")
    else:
        logging.info("⚠️ No update received within timeout.")

    # Fetch latest synced data
    data = current_user.get_latest_data()

    blockers, blockers_content = mpe_helper.checkBlockers(current_user.get_latest_snapshot())

    logging.info(blockers_content)
    logging.info(blockers)

    if blockers.total:
        logging.info("reject")
        mpe_helper.process_profile_rejection(uid, blockers, blockers_content)
    elif not blockers.total and current_user.get_latest_data()["mpeBlock"] == True:
        logging.info("accept")
        mpe_helper.process_profile_acceptance(uid)
    else:
        logging.info("already alright")

    current_user.stop_listener()

    return


@https_fn.on_call(region="europe-west1", memory=options.MemoryOption.MB_512, enforce_app_check=True)
def requestMandatoryProfileEditingReview(req: https_fn.CallableRequest) -> Any:
    """Placeholder cloud function. This is the endpoint the frontend will call in the future to request another manual review for their profile."""
    db = firestore.client()
    uid = req.auth.uid

    comment = req.data.get("comment", "")

    db.collection("users").document(uid).update(
        {"mpeReviewInProgress": True, "mpeUserComment": comment, "mpeDisplay.title": "Your profile is in manual review",
         "mpeDisplay.body": "You can still make changes to your profile and restart matching immediately."})


def fetchUsers(limit, severity):
    """Gets users with open old mpeImprovements (prompts, attributes only)"""
    db = firestore.client()

    users_ref = db.collection("users")

    filters = [
        {"field": "mpeImprovements.prompts", "operator": "!=", "value": []},
        {"field": "mpeImprovements.attributes", "operator": "!=", "value": []}
    ]

    all_results = []
    seen_uids = set()

    for f in filters:
        query = users_ref.where(filter=FieldFilter("mpeBlock", "==", False)).where(
            filter=FieldFilter(f["field"], f["operator"], f["value"])).where(
            filter=FieldFilter("mpeEvaluationResultHighest", "==", severity)).order_by("uid").limit(limit).get()

        for doc in query:
            data = doc
            if data.id not in seen_uids:
                seen_uids.add(data.id)
                all_results.append(data)

    return all_results


def scanImagesAdvanced(uid, image_id) -> dict:
    """
    Advanced image scanning using Google Cloud Vision API.
    Returns a dict with scan results including:
    - strictExplicitness: True if adult content detected
    - noFace: True if no face detected
    """
    db = firestore.client()

    # Get image URL from image document
    image_doc = db.collection("users").document(uid).collection("images").document(image_id).get()
    if not image_doc.exists:
        logging.error(f"Image {image_id} not found for user {uid}")
        return {"strictExplicitness": False, "noFace": False}

    image_url = image_doc.get("url")
    if not image_url:
        logging.error(f"No URL found for image {image_id}")
        return {"strictExplicitness": False, "noFace": False}

    # Use safe_search to check image
    try:
        vision_result = safe_search(image_url)
        return {
            "strictExplicitness": vision_result.get("adult") == "VERY_LIKELY",
            "noFace": not vision_result.get("has_face", False)
        }
    except Exception as e:
        logging.error(f"Error scanning image {image_id} for user {uid}: {e}")
        return {"strictExplicitness": False, "noFace": False}


@scheduler_fn.on_schedule(
    schedule='25 23 * * *',
    timezone=scheduler_fn.Timezone("GMT"),
    region="europe-west1",
    timeout_sec=1800,
    memory=options.MemoryOption.GB_1,
)
def autoPushUserMpe(event: scheduler_fn.ScheduledEvent) -> None:
    """
    Once a day, push remaining users in quota through mandatory profile editing blocker.
    Checks for:
    1. Explicit content in images using Google Cloud Vision
    2. Face presence in images
    3. Inappropriate content in prompts and attributes
    """
    db = firestore.client()

    # defining necessary collections
    user_collection = db.collection("users")
    mpe_quota_collection = db.collection("mpeQuota")

    # Get the current UTC date and time
    yesterday_date = datetime.now()

    # Format the date as "YYYYMMDD"
    formatted_date = yesterday_date.strftime("%Y%m%d")

    # Get remaining mpe block quota for the day
    mpe_quota_doc = mpe_quota_collection.document("quota").get()
    mpe_today_doc = mpe_quota_collection.document(formatted_date).get()

    quota = mpe_quota_doc.get("quota")
    blocks_made = mpe_today_doc.get("blocksCount") if mpe_today_doc.exists else 0

    remaining_quota: int = quota - blocks_made

    # Query users to block, go down severity level as needed
    all_users = []
    severities = [4, 3, 2, 1]

    # all_users = [user_collection.document("GWAJKKgQ9nhSrhrpRu8cMUL2EaB3").get()]

    for s in severities:
        more_users: list[DocumentSnapshot] = fetchUsers(remaining_quota, s)
        all_users.extend(more_users)

        if len(more_users) < remaining_quota:
            remaining_quota -= len(more_users)
        else:
            break

        if len(more_users) >= 40:
            break

    consolidated_users = list(set(all_users))

    logging.info(f"Fetched {len(consolidated_users)} users total")

    # Process each user
    for c in consolidated_users:
        uid = c.id
        # get user dict

        current_user = mpe_user_watcher.FirestoreUserWatcher(uid)

        if not current_user.get_latest_data()["signUpFinished"]:
            logging.info("Sign Up not yet finished")
            return

        # Fetch latest synced data
        data = current_user.get_latest_data()

        mpe_images = data['mpeImprovements']['images']
        mpe_images_confirmed = []

        mpe_attributes = data['mpeImprovements']['attributes']
        mpe_attributes_confirmed = [m for m in mpe_attributes if m == "bio"]

        for m in mpe_images:
            image_scan = mpe_helper.imageScan(uid, m)

            if image_scan.explicit:
                mpe_images_confirmed.append(m)

        has_face = mpe_helper.faceOnProfile(uid, current_user.get_latest_snapshot())

        logging.info(f"Has face: {has_face}")

        db.collection("users").document(uid).update(
            {"mpeImprovements.images": mpe_images_confirmed, "mpeImprovements.attributes": mpe_attributes_confirmed})

        if current_user.get_latest_data()["mpeNoFace"] != (not has_face):
            db.collection("users").document(uid).update({"mpeNoFace": not has_face})

        if current_user.wait_for_update(timeout=1):  # Waits up to 5 seconds
            logging.info("✅ Update received!")
        else:
            logging.info("⚠️ No update received within timeout.")

        # Fetch latest synced data
        data = current_user.get_latest_data()

        blockers, blockers_content = mpe_helper.checkBlockers(current_user.get_latest_snapshot())

        logging.info(blockers_content)
        logging.info(blockers)

        if blockers.total:
            logging.info("reject")
            mpe_helper.process_profile_rejection(uid, blockers, blockers_content)
        else:
            mpe_helper.process_profile_acceptance(uid)

        current_user.stop_listener()

    return

from urllib.parse import urlparse


def extract_gcs_location_from_signed_url(url):
    """
    Extracts the bucket name and blob path from a Firebase Storage signed URL.
    Returns (bucket_name, blob_name) tuple.
    """
    parsed_url = urlparse(url)

    # Extract bucket from path (after /v0/b/)
    path_parts = parsed_url.path.split('/')
    try:
        bucket_index = path_parts.index('b') + 1
        bucket_name = path_parts[bucket_index]
    except (ValueError, IndexError):
        raise ValueError("Could not extract bucket name from URL")

    # Extract blob name (after /o/)
    try:
        object_index = path_parts.index('o') + 1
        blob_quoted = path_parts[object_index]
        blob_name = unquote(blob_quoted)
    except (ValueError, IndexError):
        raise ValueError("Could not extract blob name from URL")

    return bucket_name, blob_name

def convert_to_jpg_bytes(image_bytes, max_width=2000):
    """
    Convert an image of any format to JPG with a maximum width of 2000px,
    preserving aspect ratio, and return it as base64 encoded bytes.

    Args:
        image_bytes (bytes): The input image as bytes
        max_width (int): Maximum width of the output image (default: 2000)

    Returns:
        str: Base64 encoded JPG image
    """
    # Open image from bytes
    img = Image.open(BytesIO(image_bytes))

    # Calculate new dimensions while maintaining aspect ratio
    width, height = img.size
    if width > max_width:
        ratio = max_width / width
        new_width = max_width
        new_height = int(height * ratio)
        img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

    # Convert to RGB if needed (for formats like RGBA or P)
    if img.mode != 'RGB':
        img = img.convert('RGB')

    # Save as PNG to BytesIO object
    output = BytesIO()
    img.save(output, format='JPEG')
    png_bytes = output.getvalue()

    return png_bytes


def read_image_bytes(bucket_name, blob_name):
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(blob_name)

    # Download content as bytes
    image_bytes = blob.download_as_bytes()

    return image_bytes


def moderate_image(image_source, session, client):
    response = client.detect_moderation_labels(Image={'Bytes': image_source})
    print(response)

    for label in response['ModerationLabels']:
        if label['Name'] in ['Explicit', 'Violence', 'Visually Disturbing', 'Drugs & Tobacco', 'Alcohol', 'Gambling',
                     'Hate Symbols']:
            return True

    return False


def detect_faces(image_source, session, client):
    response = client.detect_faces(Image={'Bytes': image_source},
                                   Attributes=[])

    return bool(response['FaceDetails'])


@on_document_created(region="europe-west1", memory=options.MemoryOption.GB_1,
                     document="users/{userId}/images/{imageDoc}")
def uponImageUpload(event: Event[Change[DocumentSnapshot]]):
    db = firestore.client()

    image_dict = event.data.to_dict()

    if not image_dict.get('v2'):
        return

    uid = event.params['userId']

    # get the image url
    image_id = event.params['imageDoc']
    image_url = image_dict.get("url")
    bucket_name, blob_name = extract_gcs_location_from_signed_url(image_url)

    # get image bytes
    image_bytes = convert_to_jpg_bytes(read_image_bytes(bucket_name, blob_name))

    aws_key = access_secret_version("AWS_Rekognito_Key")
    aws_secret = access_secret_version("AWS_Rekognito_Secret_Key")

    session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)
    client = session.client('rekognition', region_name='eu-west-2')

    # get moderation and face results
    explicit = moderate_image(image_bytes, session, client)
    face = detect_faces(image_bytes, session, client)

    meta_dictionary = {"currentImages": {
        image_id: {"explicit": explicit, "face": face, "timeAdded": firestore.firestore.SERVER_TIMESTAMP}}}

    image_dictionary = {
        "explicit": explicit,
        "face": face,
        "timeAdded": firestore.firestore.SERVER_TIMESTAMP
    }

    # get user document
    parent_reference = db.collection("users").document(event.params['userId'])
    user_data: Dict[str, Any] = parent_reference.get().to_dict()

    # determine user status
    verified:  Dict[str, Any] = user_data.get('verified', False)

    # if user is verified, own face presence should be checked to
    if verified:
        if face:
            own_face = compare_face_urls(user_data.get('verificationPhoto', ''), image_url, client)
        else:
            own_face = False

        meta_dictionary["currentImages"][image_id]['ownFace'] = own_face
        image_dictionary['ownFace'] = own_face

    time.sleep(3)

    # update meta document
    meta_image_reference = parent_reference.collection("meta").document("imageCompliance")
    meta_image_reference.set(meta_dictionary, merge=True)

    # update image document
    event.data.reference.update(image_dictionary)

    # show loading screen in between
    compliance_images = get_current_compliance_images(uid)

    # based on user status, proceed with verification consequences or mpe consequences
    if verified and user_data.get('verificationV3'):

        face_images, own_face_images, explicit_images = has_ownface_face_explicitness(compliance_images)
        print(f"Face images: {face_images}, Own face images: {own_face_images}, Explicit images: {explicit_images}")

        send_ampli_event('Profile: User Uploads Image', uid, {'Explicit': explicit, 'Face': face, 'Own Face': own_face, 'Any Explicit': bool(explicit_images), 'Any Face': bool(face_images), 'Any Own Face': bool(own_face_images)})

        if user_data.get('gender') == 'Female' and len(explicit_images) > 0:
                record_explicit_images_women(uid, (not bool(own_face_images)), explicit_images, db)
                explicit_images = [] 

        if len(own_face_images) >= 3 and not explicit_images:
            print('own face images > 3 and no explicit images')
            advanced_verification_eligible(user_data, parent_reference, own_face_images)
        elif len(own_face_images) >= 1 and not explicit_images:
            print('own face images > 1 and no explicit images')
            normal_verification_eligible(user_data, parent_reference, own_face_images, 'uponImageUpload')
        else:
            print('not enough own face images or explicit images present')
            print(f"Own face images: {bool(own_face_images)}, Explicit images: {explicit_images}")
            put_user_mpe((not bool(own_face_images)), explicit_images, uid, db, 'uponImageUpload')

    else:

        face_images, explicit_images = has_face_explicitness(compliance_images)

        send_ampli_event('Profile: User Uploads Image', uid, {'Explicit': explicit, 'Face': face, 'Any Explicit': bool(explicit_images), 'Any Face': bool(face_images)})

        # decision rule: approve user automatically or disapproving them with cause
        if face_images and not explicit_images:
            approve_retry_image_verification(uid)
        else:
            public_profile = db.collection('users').document(uid).collection("publicProfile").limit(1).get()[0].to_dict()
            n_pictures_raw = public_profile.get("nPictures")
            n_pictures = [n.id for n in n_pictures_raw] 
            disapprove_image_verifiation(uid, n_pictures, explicit_images, face_images)
        

    return


import copy


@google.cloud.firestore_v1.transaction.transactional
def move_image_compliance_image(transaction, meta_image_reference, image_id):
    '''Transaction-based removal of deleted image from meta images representation, returns latest state for any considerations'''

    # get meta doc & current images from there
    meta_image_doc = meta_image_reference.get(transaction=transaction)
    current_images = meta_image_doc.to_dict().get("currentImages", {})

    # check if image id in current images
    if image_id not in current_images:
        return

    # get the removed image to store for auditing purposes
    removed_image = copy.deepcopy(current_images[image_id])

    # delete key of removed image in current images
    del current_images[image_id]

    # add deletion time
    removed_image["timeDeleted"] = firestore.firestore.SERVER_TIMESTAMP

    # update meta document
    transaction.update(meta_image_reference,
                       {"currentImages": current_images, f"pastImages.{image_id}": removed_image})

    # return new current images for mpe purposes
    return


@on_document_deleted(region="europe-west1", memory=options.MemoryOption.GB_1,
                     document="users/{userId}/images/{imageDoc}")
def uponImageDeletion(event: Event[Change[DocumentSnapshot]]):
    db = firestore.client()

    image_dict = event.data.to_dict()

    if not image_dict.get('v2'):
        return

    # get the image url
    image_id = event.params['imageDoc']
    uid = event.params['userId']

    # update meta document
    parent_reference = db.collection("users").document(event.params['userId'])
    meta_image_reference = parent_reference.collection("meta").document("imageCompliance")

    user_data = parent_reference.get().to_dict()
    verified = user_data.get('verified', False)

    transaction = db.transaction()

    move_image_compliance_image(transaction, meta_image_reference, image_id)

    public_profile = db.collection('users').document(uid).collection("publicProfile").limit(1).get()[0].to_dict()
    n_pictures_raw = public_profile.get("nPictures")
    n_pictures = [n.id for n in n_pictures_raw]  # gets the ids as needed for comp functions

    # show loading screen in between
    compliance_images = get_current_compliance_images(uid)

    # based on user status, proceed with verification consequences or mpe consequences
    if verified and user_data.get('verificationV3'):
        face_images, own_face_images, explicit_images = has_ownface_face_explicitness(compliance_images)

        send_ampli_event('Profile: User Deletes Image', uid, {'Any Explicit': bool(explicit_images), 'Any Face': bool(face_images), 'Any Own Face': bool(own_face_images)}) 

        if user_data.get('gender') == 'Female' and len(explicit_images) > 0:
                record_explicit_images_women(uid, (not bool(own_face_images)), explicit_images, db)
                explicit_images = [] 

        if len(own_face_images) >= 3 and not explicit_images:
            advanced_verification_eligible(user_data, parent_reference, own_face_images)
        elif len(own_face_images) >= 1 and not explicit_images:
            normal_verification_eligible(user_data, parent_reference, own_face_images, 'uponImageDeletion')
        else:
            put_user_mpe((not bool(own_face_images)), explicit_images, uid, db, 'uponImageDeletion')
    else:
        face_images, explicit_images = has_face_explicitness(compliance_images)
        
        send_ampli_event('Profile: User Deletes Image', uid, {'Any Explicit': bool(explicit_images), 'Any Face': bool(face_images)})

        # decision rule: approve user automatically or disapproving them with cause
        if face_images and not explicit_images:
            approve_retry_image_verification(uid)
        else:
            disapprove_image_verifiation(uid, n_pictures, explicit_images, face_images)

    return


def has_face_explicitness(images_moderation):
    """
    Checks whether a user's profile is ready for verification, i.e., presence of face and no explicit imagery
    """

    explicit_images = []
    face_images = []

    # loop over all images provided and return if any explicit or face ones among them
    for i in images_moderation:

        image_dict = images_moderation[i]

        face = image_dict.get("face")
        explicit = image_dict.get("explicit")

        if face:
            face_images.append(i)

        if explicit:
            explicit_images.append(i)

    return face_images, explicit_images

def has_ownface_face_explicitness(images_moderation):
    """
    Checks whether a user's profile is ready for verification, i.e., presence of face and no explicit imagery
    """

    explicit_images = []
    face_images = []
    own_face_images = []

    def get_images(key): 
        return [i for i, image_dict in images_moderation.items() if image_dict.get(key)]  
    
    return get_images('face'), get_images('ownFace'), get_images('explicit')  




def get_current_compliance_images(uid):
    db = firestore.client()

    meta_image_reference = db.collection("users").document(uid).collection("meta").document("imageCompliance")

    meta_image_doc = meta_image_reference.get().to_dict()

    current_images = meta_image_doc.get("currentImages")

    return current_images


def ordinal(n):
    """Convert an integer into its ordinal representation: 1 -> 1st, 2 -> 2nd, etc."""
    return f"{n}{'tsnrhtdd'[(n//10%10!=1)*(n%10<4)*n%10::4]}"

def generate_image_reject_title_body(all_image_ids, violating_image_ids, face):
    explicit_items = {}

    if violating_image_ids:
        indices = [all_image_ids.index(img_id) + 1 for img_id in violating_image_ids if img_id in all_image_ids]
        ordinals = [ordinal(i) for i in indices]
        explicit_items["image"] = ordinals

    explicit_entries = list(explicit_items.keys())

    if not explicit_entries and not face:
        title = "Add a clear face picture to get verified"
        description = "Every Chyrpe profile must include a clear photo of your face"
        return title, description

    elif not explicit_entries:
        title = "Edit your profile to get verified"
        description = """It features content not allowed outside the kink area, like sexualising or kinky themes.
            Kink is welcome in the new profile kink area or when a woman allows it in your chat."""
        return title, description

    else:
        image_ordinals = explicit_items["image"]
        if len(image_ordinals) > 1:
            ordinal_str = ", ".join(image_ordinals[:-1]) + " and " + image_ordinals[-1]
        else:
            ordinal_str = image_ordinals[0]
        title = f"Edit your {ordinal_str} image to get verified"

        description = "These images feature sexualising or kinky themes. This is not allowed on Chyrpe."
        if not face:
            description += "\n\nPlease also add at least one image of your face. "

        return title, description

def disapprove_image_verifiation(uid, n_pictures, explicit_images, face_images, title_override=None, description_override=None, force_update_decriptions=False):
    """Handles updating user state to disapprove verification images, whether moderator (can override title & description) or not"""

    db = firestore.client()

    users_reference = db.collection("users").document(uid)

    users_doc = users_reference.get().to_dict()

    users_update_dict = {
        "imageVeriHasFace": True if face_images else False,
        "imageVeriMadeChanges": False,
        "imageVeriHasExplicit": True if explicit_images else False,
        "imageVerified": False,
        "imageVeriFailed": True,
        "imageVeriImprovementImages": explicit_images
    }

    title, description = generate_image_reject_title_body(n_pictures, explicit_images, bool(face_images))

    # this should not always override current custom values
    if ((users_doc.get("imageVeriFailed", False) or users_doc.get("imageVeriHumanRejected",
                                                                  False)) and not users_doc.get(
            "imageVeriHumanReviewInProgress", False)) or force_update_decriptions:
        users_update_dict["verificationCustomTitle"] = title_override if title_override else title
        users_update_dict["verificationCustomSubtitle"] = description_override if description_override else description

    users_reference.update(users_update_dict)

def approve_image_verification(uid, force_update_decriptions=False):
    """Handles updating user state to approve verification images, whether moderator or not"""

    db = firestore.client()

    users_reference = db.collection("users").document(uid)

    users_doc = users_reference.get().to_dict()

    users_update_dict = {
        "imageVeriHasFace": True,
        "imageVeriHasExplicit": False,
        "imageVerified": True,
        "imageVeriFailed": False,
        "imageVeriMadeChanges": True,
        "imageVeriImprovementImages": []}

    # this should not always override current custom values
    if ((users_doc.get("imageVeriFailed", False) or users_doc.get("imageVeriHumanRejected", False)) and not users_doc.get("imageVeriHumanReviewInProgress", False)) or force_update_decriptions:
        users_update_dict["verificationCustomTitle"] = "You can now re-attempt verification"
        users_update_dict["verificationCustomSubtitle"] = "Tap below to retry"

    users_reference.update(users_update_dict)

def approve_retry_image_verification(uid, force_update_decriptions=False):
    """Handles updating user state to approve verification images, whether moderator or not"""

    db = firestore.client()

    users_reference = db.collection("users").document(uid)

    users_doc = users_reference.get().to_dict()

    users_update_dict = {
        "imageVeriHasFace": True,
        "imageVeriHasExplicit": False,
        "imageVeriImprovementImages": [],
        "imageVeriMadeChanges": True
    }

    # this should not always override current custom values
    if ((users_doc.get("imageVeriFailed", False) or users_doc.get("imageVeriHumanRejected", False)) and not users_doc.get("imageVeriHumanReviewInProgress", False)) or force_update_decriptions:
        users_update_dict["verificationCustomTitle"] = "You can now re-attempt verification"
        users_update_dict["verificationCustomSubtitle"] = "Tap below to retry"

    users_reference.update(users_update_dict)


@https_fn.on_call(region="europe-west1", memory=options.MemoryOption.MB_512, enforce_app_check=True)
def imageVerificationRequestReview(req: https_fn.CallableRequest) -> Any:
    """User called function to check uploaded images for explicitness & face presence in verification"""

    if req.auth == None:
        return

    db = firestore.client()

    uid = req.auth.uid

    user_doc_ref = db.collection('users').document(uid)

    user_doc_ref.update(
        {"verificationGoingOn": True, "manualVerification": True, "autoVerificationStarted": False,
         "autoVeriReclass": True, "imageVeriHumanReviewInProgress": True, "manualVerificationReason": "Images",
         "verificationCustomTitle": "Manual Review in Progress",
         "verificationCustomSubtitle": "This can take up to 48 hours, but is mostly quicker.",
         })

    track_manual_review_verification_requested(uid, "Images")

    return

import string
def generate_random_string(length=10):
    characters = string.ascii_letters + string.digits  # a-zA-Z0-9
    return ''.join(random.choice(characters) for _ in range(length))

import io
def upload_image_to_firebase(bucket_name, image_bytes, file_name):
    '''Uploads a given image (verification image from AWS) to Firebase Storage'''
    # Create a bucket instance
    from firebase_admin import storage
    bucket = storage.bucket(bucket_name)

    # Create an in-memory file-like object from the bytes
    image_stream = io.BytesIO(image_bytes)

    # Create a new blob in the bucket with the desired filename
    blob = bucket.blob(file_name)

    # Upload the image to Firebase Storage from the in-memory file
    blob.upload_from_file(image_stream,
                          content_type='image/jpeg')  # Set the correct content type for your image

    metadata = blob.metadata or {}
    token = metadata.get("firebaseStorageDownloadTokens", None)

    if not token:
        import uuid
        token = str(uuid.uuid4())  # Generate a new token if none exists
        metadata["firebaseStorageDownloadTokens"] = token
        blob.metadata = metadata
        blob.patch()  # Save the new metadata

    # Construct the download URL with the token
    download_url = f"https://firebasestorage.googleapis.com/v0/b/{bucket.name}/o/{blob.name.replace('/', '%2F')}?alt=media&token={token}"

    return download_url

def aws_rekognition_query_results(client, session_id, uid):
    """Get AWS Face Rekognition Results - store them safely for further processing"""
    db = firestore.client()

    # get face liveness session, get bytes & confidence
    response = client.get_face_liveness_session_results(SessionId=session_id)
    image_bytes: bytes = response['ReferenceImage']['Bytes']
    image_certainty: float = response['Confidence']

    # get bucket from env, construct filename
    bucket = 'greenrocks-s3' if is_dev_environment() else 'greenrocks-s4p'
    current_timestamp = str(datetime.now().timestamp())
    random_string = generate_random_string()
    filename = f"users/{uid}/uploads/{current_timestamp}_{random_string}.jpeg"

    # Write the image to GCP for moderators
    image_url = upload_image_to_firebase(bucket, image_bytes, filename)

    db.collection("users").document(uid).update({"verificationPhoto": image_url, "verificationV3": True})
    db.collection("users").document(uid).collection("meta").document("verificationRequests").set({
        "auto": firestore.firestore.ArrayUnion([{
            "sessionId": session_id,
            "timestamp": datetime.now().strftime('%Y%m%d %H:%M:%S'),
            "imageUrl": image_url,
            "imageCertainty": image_certainty,
            "imageName": filename
        }])
    }, merge=True)

    return image_bytes, image_url, image_certainty

from google.auth import default

def check_gender_age(client, source_bytes, given_gender):
    """Checks if AWS gender matches given gender; if not, puts them in manual verification queue"""

    # get response & face_details
    response = client.detect_faces(Image={'Bytes': source_bytes},
                                   Attributes=['GENDER', 'AGE_RANGE'])

    face_details = response['FaceDetails']

    # set default values
    gender = "Unsure"
    medium_age = 18

    # loop over face details - safe even if no face details
    for f in face_details:
        medium_age = int((f['AgeRange']['High'] + f['AgeRange']['Low']) / 2)
        gender = f['Gender']['Value'] if f['Gender']['Confidence'] > 60 else "Unsure"

    # return gender equality, age compliance
    return gender == given_gender, medium_age > 18

def is_high_risk_region_name(region, name) -> bool:
    risky_regions = ['Nigeria']
    risky_names = ['goddess', 'mistress', 'kendra', 'angel']

    for r in risky_names:
        if r in name.lower():
            return True

    if region in risky_regions:
        return True

    return False


def check_face_comparison(client, source_bytes, uid):
    """Checks if user face matches face on their profile (checks against profile pics); if not matched, puts them in manual verification queue"""

    db = firestore.client()

    meta_image_reference = db.collection("users").document(uid).collection("meta").document("imageCompliance")
    meta_image_doc_data = meta_image_reference.get().to_dict()

    current_images = meta_image_doc_data.get("currentImages", {})
    face_image_refs = []

    for c in current_images.keys():
        if current_images.get(c).get("face"):
            face_image_refs.append(c)

    face_images = []
    own_face_images = []

    for f in face_image_refs:
        image_doc = db.collection("users").document(uid).collection("images").document(f).get()
        url = image_doc.get("url")

        bucket, blob = extract_gcs_location_from_signed_url(url)

        initial_bytes = read_image_bytes(bucket, blob)

        converted_bytes = convert_to_jpg_bytes(initial_bytes)

        face_images.append({'converted_bytes': converted_bytes, 'image_id': f})

    for target_dict in face_images:

        response = client.compare_faces(SimilarityThreshold=80,
                                        # TODO: Add env. variable with required similarity threshold
                                        SourceImage={'Bytes': source_bytes},
                                        TargetImage={'Bytes': target_dict.get('converted_bytes')})

        for _ in response['FaceMatches']:
            user_reference = db.collection("users").document(uid)
            user_reference.collection("images").document(f).update({'face': True, 'ownFace': True})
            user_reference.collection('meta').document('imageCompliance').update({f'currentImages.{target_dict.get('image_id')}.ownFace': True, f'currentImages.{target_dict.get('image_id')}.face': True})
            own_face_images.append(f)

    return bool(own_face_images)

def catch_log_return_false(f):
    '''
    A function wrapper for catching all exceptions and logging them
    '''
    @functools.wraps (f)
    def inner(*args, **kwargs): 
        try:
            return f(*args, **kwargs) 
        except Exception as e:
            logging.exception(e)
            return False

    return inner

from typing import Optional

@catch_log_return_false
def get_findom_interest_status(kinks_list: list[str], professionals_req: bool) -> Optional[bool]:
    """
    Based on a user's kink list and findom filter status, returns whether to classify them as a findom_interest.

    Args:
        kinks_list (list): A list of kinks associated with the user.
        findom_filter (bool): Indicates if the findom filter is enabled.

    Returns:
        Optional[bool]: True if the user is a findom_interest, False if explicitly opted out of findom interest, or None if undetermined.
    """

    if professionals_req:
        return False
    elif 'Findom' in kinks_list:
        return True
    else:
        return None



def compare_face_urls(verification_photo_url: str, compare_url: str, client) -> bool:
    """
    Compares whether two images (verification photo + profile photo) include the same face
    
    Args:
        verification_photo_url: signed Firebase Storage URL of the verification photo
        compare_url: signed Fireabse Storage URL of the comparison/profile photo

    Returns:
        bool: true if input imges include same face, else false
    """

    face_images = []
    
    for url in [verification_photo_url, compare_url]:

        bucket, blob = extract_gcs_location_from_signed_url(url)

        initial_bytes = read_image_bytes(bucket, blob)

        converted_bytes = convert_to_jpg_bytes(initial_bytes)

        face_images.append(converted_bytes)
    

    response = client.compare_faces(SimilarityThreshold=80,
                                        # TODO: Add env. variable with required similarity threshold
                                        SourceImage={'Bytes': face_images[0]},
                                        TargetImage={'Bytes': face_images[1]})
    
    for _ in response['FaceMatches']:
            return True
    

    return False


@https_fn.on_call(region="europe-west1", timeout_sec=540, memory=options.MemoryOption.MB_512)
def requestAdvancedVerification(req: https_fn.CallableRequest) -> Any:

    if req.auth is None:
        print("Unauthorized request: No authentication provided")
        return
    
    uid = req.auth.uid
    db = firestore.client()

    user_doc_ref = db.collection('users').document(uid)
    images_collection_ref = user_doc_ref.collection('images')

    user_data = user_doc_ref.get().to_dict()
    user_advanced_verification_eligible = user_data.get('advancedVerificationEligible', False)
    user_mpe_block = user_data.get('mpeBlock', False)
    user_verified = user_data.get('verified', False)
    user_sign_up_finished = user_data.get('signUpFinished', False)

    if not user_advanced_verification_eligible and user_verified and user_sign_up_finished and not user_mpe_block:
        print(f"User {uid} is not eligible for advanced verification")
        return

    images_to_advanced_verify = req.data.get('imagesToAdvancedVerify', [])

    verified_images_to_advanced_verify = []

    for i in images_to_advanced_verify:
        image_doc_ref = images_collection_ref.document(i)
        image_doc = image_doc_ref.get()

        if not image_doc.exists:
            continue

        image_data = image_doc.to_dict()

        if image_data.get('ownFace', False) and image_data.get('face', False) and not image_data.get('explicit', False):
            verified_images_to_advanced_verify.append(i)

    if len(verified_images_to_advanced_verify) >= 3:
        # Update user document with advanced verification request
        user_doc_ref.update({
            "advancedVerified": True,
        })

        user_doc_ref.collection('publicProfile').get()[0].reference.update({
            "advancedVerified": True,
        })

        for image_id in verified_images_to_advanced_verify:
            # Update each image document to mark it as advanced verified
            images_collection_ref.document(image_id).update({
                "advancedVerified": True,
            })

        # Log the event
        send_ampli_event('Profile: User Requests Advanced Verification', uid, {
            'images': verified_images_to_advanced_verify
        })

        print(f"User {uid} has been marked as advanced verified with images: {verified_images_to_advanced_verify}")

        return
    else:

        print(f"User {uid} does not have enough images for advanced verification: {len(verified_images_to_advanced_verify)} found, 3 required")
        return

def advanced_verification_eligible(users_data: dict | None = None, users_reference: firestore.DocumentReference = None, own_face_images: list[str] | None = None):

    users_data = users_data or {}
    own_face_images = own_face_images or []

    if not users_data.get("advancedVerified", False) and not users_data.get("advancedVerificationEligible", False):
        users_reference.update({"advancedVerificationEligible": True})

        if users_data.get("mpeBlock", False):
            mpe_helper.process_profile_acceptance(users_reference.id)

def normal_verification_eligible(users_data: dict = {}, users_reference: firestore.DocumentReference = None, own_face_images: list[str] = [], source: str = ''):
    
    print('User is eligible for normal verification')

    if users_data.get("advancedVerified", False):
        users_reference.update({"advancedVerified": False})
        users_reference.collection('publicProfile').get()[0].reference.update({
            "advancedVerified": True,
        })

        advanced_verified_images_to_remove_status = users_reference.collection('images').where(filter=FieldFilter('advancedVerified', '==', True)).get()

        for image in advanced_verified_images_to_remove_status:
            # Update each image document to mark it as advanced verified
            image.reference.update({
                "advancedVerified": False,
            })

        send_ampli_event('Profile: Remove Advanced Verifiction', users_reference.id, {})

        return
    else:
        if users_data.get("mpeBlock", False):
            print(f'User is mpe blocked, will be unblocked, source: {source}')
            mpe_helper.process_profile_acceptance(users_reference.id)
            users_data.get('publicProfile').update({'verified': True})

    return

def put_user_mpe(no_own_face: bool = False, explicit_images: list[str] = [], uid: str = '', db: firestore.Client = None, source: str = 'Image Upload'):

    title, description = mpe_helper.generateMpeTitleAndBody([], [], explicit_images, no_own_face)

    new_value = {
        "uid": uid,  # Replace with the actual user id
        "markedImageIds": explicit_images,
        "markedPromptIds": [],
        "markedAttributeIds": [],
        "noFace": no_own_face,
        "displayTitle": title,
        "displayBody": description,
        "displayBtn": 'Edit Profile',
        "timestamp": firestore.SERVER_TIMESTAMP,
        "source": source
    }

    db.collection("moderatorRejectProfiles").add(new_value)

    db.collection('users').document(uid).get().to_dict().get('publicProfile', {}).update({'verified': False,  "advancedVerified": False})

    advanced_verified_images_to_remove_status = db.collection('users').document(uid).collection('images').where(filter=FieldFilter('advancedVerified', '==', True)).get()

    for image in advanced_verified_images_to_remove_status:
        # Update each image document to mark it as advanced verified
        image.reference.update({
            "advancedVerified": False,
        })

    return

def record_explicit_images_women(uid, no_own_face, explicit_images, db):
    """
    Records explicit images in case of women
    """

    # MPE Potential W Collection

    mpe_potential_collection = db.collection("mpeImagePotentialW")

    new_value = {
        "uid": uid,
        "markedImageIds": explicit_images,
        "noFace": no_own_face,
        "timestamp": firestore.SERVER_TIMESTAMP,
    }

    mpe_potential_collection.add(new_value)
