import datetime

from google.cloud import secretmanager, firestore
from firebase_admin import firestore
from google.cloud.firestore_v1 import <PERSON><PERSON>ilt<PERSON>, Or
import google.cloud.logging

# initializing log client
log_client = google.cloud.logging.Client()
log_client.setup_logging()

import hmac
import hashlib

def access_secret_version(secret_name):
    '''Access a secret from the project's secret manager'''
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/greenrocks-9abc3/secrets/{secret_name}/versions/latest"
    response = client.access_secret_version(name=name)
    return response.payload.data.decode('UTF-8')

def hash_identifier(identifier: str, secret: str) -> str:
    """
    Hash an identifier (e.g., email or user ID) using HMAC-SHA256 with a secret (salt/pepper).

    Args:
        identifier (str): The identifier to be hashed.
        secret (str): The secret value used as the key.

    Returns:
        str: The resulting hexadecimal hash string.
    """
    # Create an HMAC object using the secret as the key, the identifier as the message,
    # and SHA256 as the digest method.
    hmac_obj = hmac.new(key=secret.encode('utf-8'),
                        msg=identifier.encode('utf-8'),
                        digestmod=hashlib.sha256)
    # Return the hexadecimal digest of the HMAC object.
    return hmac_obj.hexdigest()


def attributeReferredUser(uid) -> None:
    """Attribute user with the given uid to a given creator campaign"""

    db = firestore.client()

    # get the referred user's user document

    users_collection = db.collection("users")

    user_ref = users_collection.document(uid)
    user_doc = user_ref.get()
    user_data = user_doc.to_dict()

    # determine eligibility for referral

    referral_collection = db.collection("referralLog")

    # get their defining login characteristics: email and phone number

    user_phone = user_data.get("phone_number")
    user_email = user_data.get("email")
    user_ref_code = user_data.get("refCode")
    user_created_time = user_data.get("created_time")

    # hash email and phone number

    referral_secret = access_secret_version("Referral_Secret")

    hashed_phone = None
    hashed_email = None

    if user_phone is not None:
        hashed_phone = hash_identifier(user_phone, referral_secret)

    hashed_email = hash_identifier(user_email, referral_secret)

    # search permanent collection for other user with that email or phone number hash

    phone_query = []
    email_query = []
    eligible = True

    if user_phone is not None:
        phone_query = referral_collection.where(filter=FieldFilter("eligible_referral_phone", "==", hashed_phone)).get()

    email_query = referral_collection.where(filter=FieldFilter("eligible_referral_email", "==", hashed_email)).get()

    total_query = email_query + phone_query

    if len(total_query) == 0:
        eligible = True
    else:
        eligible = False

    # if eligible:
    if eligible:

        # create new record with user's uid, hashed values and include:
        referral_collection.document(uid).set({
            "eligible_referral_phone": hashed_phone,
            "eligible_referral_email": hashed_email,
            "eligible_referral_creator": user_ref_code,  # eligible_referral_creator: get ref code from users document
            "eligible_referral_date": firestore.SERVER_TIMESTAMP,  # eligible_referral_date: current timestamp
            "eligible_referral_uid": uid, # eligible_referral_uid: uid
            "eligible_referral_account_creation_date": user_created_time # eligible_referral_account_creation_date: created_date of profile
        })

    # if not eligible:
    else:

        timestamp = datetime.datetime.now()

        # update previous record with:
        total_query[0].reference.update({
            "secondary_referrals": firestore.firestore.ArrayUnion([{
                "referral_creator": user_ref_code, # referral_creator: get ref code from users document
                "referral_date": timestamp, # referral_date: current timestamp
                "referral_uid": uid,  # referral_uid: uid
                "referral_account_creation_date": user_created_time # referral_account_creation_date: created_date of profile
            }])
        })