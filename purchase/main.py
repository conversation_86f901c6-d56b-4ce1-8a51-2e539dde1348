# Imports in alphabetical order

from amplitude import Amplitude, BaseEvent, Identify, EventOptions
from datetime import datetime, timedelta
from firebase_admin import initialize_app
from firebase_functions import https_fn, identity_fn, options, scheduler_fn
from firebase_functions.firestore_fn import (
  on_document_created,
  on_document_deleted,
  on_document_updated,
  on_document_written,
  Event,
  Change,
  DocumentSnapshot,
)
from google.cloud import secretmanager, firestore
from firebase_admin import firestore
from google.cloud.firestore_v1 import FieldFilter, Or
import google.cloud.logging
from google.cloud.sql.connector import Connector
import json
import logging
import pg8000
import requests
import re
import requests
from requests.adapters import HTTPAdapter, Retry
import sqlalchemy
from typing import Any
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type


# initializing log client
log_client = google.cloud.logging.Client()
log_client.setup_logging()

# defining common methods in global scope
def access_secret_version(secret_name):
    '''Access a secret from the project's secret manager'''
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/greenrocks-9abc3/secrets/{secret_name}/versions/latest"
    response = client.access_secret_version(name=name)
    return response.payload.data.decode('UTF-8')

def connect_with_connector(connector:Connector) -> sqlalchemy.engine.base.Engine:

    """
    Initializes a connection pool for a Cloud SQL instance of Postgres.

    Uses the Cloud SQL Python Connector package.
    """
    def getconn() -> pg8000.dbapi.Connection:
        conn: pg8000.dbapi.Connection = connector.connect(
            "greenrocks-9abc3:europe-west2:chyrpe-prod1",
            "pg8000",
            user="<EMAIL>",
            db="chyrpe_main_db",
            enable_iam_auth=True,
        )
        return conn

    # The Cloud SQL Python Connector can be used with SQLAlchemy
    # using the 'creator' argument to 'create_engine'
    pool = sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        pool_size=2,
        max_overflow=2,
        # ...
    )
    return pool

def create_retry_session() -> requests.Session:
    session = requests.Session()
    retries = Retry(total=5, backoff_factor=0.1, status_forcelist=[429, 500, 502, 503, 504])
    session.mount('https://', HTTPAdapter(max_retries=retries))
    return session

def grant_promotional_access_rc(entitlement_name: str, time_to_end: datetime, user_id: str, secret: str, s: requests.Session) -> bool:
    timestamp_end = int(time_to_end.timestamp() * 1000)

    url = f"https://api.revenuecat.com/v1/subscribers/{user_id}/entitlements/{entitlement_name}/promotional"

    payload = json.dumps({
        "end_time_ms": timestamp_end
    })
    headers = {
        'Authorization': f'Bearer {secret}',
        'Content-Type': 'application/json'
    }

    response = s.post(url, headers=headers, data=payload)

    if response.status_code in [200, 201]:
        return True
    else:
        return False

def get_user_info_rc(user_id: str, secret: str, s: requests.Session) -> dict:
    url = f'https://api.revenuecat.com/v1/subscribers/{user_id}'

    # Prepare the headers with the bearer token
    headers = {
        'Authorization': f'Bearer {secret}'
    }

    # Make a GET request (you can change this to POST, PUT, DELETE etc. as needed)
    response = s.get(url, headers=headers)

    if response.status_code == 200:
        data = response.json()
        return data
    else:
        return {}

def sendPushNotification(uid, title, text, push_notifications_collection):
    push_notifications_collection.add({"notification_title": title, "notification_text": text,
                                       "user_refs": f'/users/{uid}',
                                       "timestamp": firestore.SERVER_TIMESTAMP})
# defining common variables
amplitude_client = Amplitude("65dbac83dec4e06df231ca34a83e4757")

def log_retry_attempt(retry_state):
    """Logs retry attempts before each retry."""
    logging.warning(f"Retrying... Attempt {retry_state.attempt_number}")

def with_retry(max_attempts=6, min_wait=2, max_wait=20, exception_types=(Exception)):
    """Decorator to add retry logic with exponential backoff."""
    return retry(
        retry=retry_if_exception_type(exception_types),
        wait=wait_exponential(multiplier=1, min=min_wait, max=max_wait),  # Initial wait 2s, doubles each time, max 10s
        stop=stop_after_attempt(max_attempts),  # Max 5 retry attempts
        before=log_retry_attempt,
        reraise=True
    )


### Actual cloud functions

initialize_app()

# GET CHYRPE FREE ENTITLEMENT: Apply Chyrpe Light entitlements for user
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.GB_1, enforce_app_check=True)
def getChyrpeFreeEntitlement(req: https_fn.CallableRequest) -> Any:
    """
    Apply Chyrpe Light entitlements for user
    :param req: Callable request incl. user auth information - no extra params
    :return: None
    """
    if req.auth.uid == None:
        return "User not authenticated"

    uid = req.auth.uid
    db = firestore.client()

    rc_secret = access_secret_version("Revenuecat_Key")
    session = create_retry_session()

    time_to_end = datetime(2099, 12, 31)

    grant_promotional_access_rc("standard_access", time_to_end, uid, rc_secret, session)
    grant_promotional_access_rc("free_standard_lifetime", time_to_end, uid, rc_secret, session)

    db.collection('users').document(uid).update({'freetrialClaimed1d': True})

# GET GOLD ENTITLEMENT: Apply Chyrpe Gold free for one week - deprecated & phased out
@https_fn.on_call(region="europe-west2", enforce_app_check=True)
def getGoldEntitlement(req: https_fn.CallableRequest) -> Any:
    if req.auth.uid == None:
        return "User not authenticated"
    uid = req.auth.uid

    rc_secret = access_secret_version("Revenuecat_Key")
    session = create_retry_session()

    time_to_end = datetime.now() + timedelta(days=7)

    grant_promotional_access_rc("gold_access", time_to_end, uid, rc_secret, session)
    grant_promotional_access_rc("free_standard_lifetime", time_to_end, uid, rc_secret, session)

    return

# GET FREE TRIAL CANCELLATION: Schedule a push notification to be sent to users 2 days before their free trial (App Store-administered) of Standard ends
@https_fn.on_call(region="europe-west2", enforce_app_check=True)
def getFreeTrialCancellationPN(req: https_fn.CallableRequest) -> Any:
    """
       Schedule a push notification to be sent to users 2 days before their free trial (App Store-administered) of Standard ends
       :param req: Callable request incl. user auth information - no extra params
       :return: None
    """
    if req.auth.uid == None:
        return

    db = firestore.client()

    uid = req.auth.uid
    push_notifications_collection = db.collection("ff_push_notifications")
    time = datetime.now() + timedelta(5)
    title = 'Your free trial for Chyrpe Standard is about to end'
    text = 'You have two more days left'
    def sendPushNotificatioDeferred(uid, title, text, time):
        push_notifications_collection.add({"notification_title": title, "notification_text": text,
              "user_refs": f'/users/{uid}', "initial_page_name": "Discovery",
              "timestamp": firestore.SERVER_TIMESTAMP, "scheduled_time": time})

    sendPushNotificatioDeferred(uid, title, text, time)

# REDEEM BOOST: Consumes a boost from a user and makes necessary changes to start a boost in user document and Postgres
@https_fn.on_call(region="europe-west2", timeout_sec=90, memory=options.MemoryOption.MB_512, enforce_app_check=True)
def redeemBoost(req: https_fn.CallableRequest) -> Any:
    """
       Consumes a boost from a user and makes necessary changes to start a boost in user document and Postgres
       :param req: Callable request incl. user auth information - no extra params
       :return: None
    """
    if req.auth.uid == None:
        return

    uid = req.auth.uid
    current_subscriptions = req.data.get("currentSubscriptions", [])

    db = firestore.client()

    # reads how many boosts user has left
    user_ref = db.collection("users").document(uid)
    user_doc = user_ref.get()
    user_data = user_doc.to_dict()
    user_gender = user_data.get("gender")
    boost_likes_left = user_data.get("boostsLeft", 0)

    woman_eligible = (user_gender == "Female" and "divine_access" in current_subscriptions)

    if (boost_likes_left < 1) and (not (woman_eligible)):
        return

    # call SQL function to insert new boost record there
    try:
        @with_retry()
        def insert_boost_with_retry():
            with Connector(refresh_strategy="lazy") as connector:
                pool = connect_with_connector(connector)

                try:
                    with pool.connect() as db_conn:
                        logging.info(f"Attempting start boost: calling user: {uid}")
                        stmt = sqlalchemy.text(
                            """SELECT insert_new_boost(:user_id)""")

                        # Execute with the parameters
                        result = db_conn.execute(stmt, {
                            'user_id': uid}).fetchall()

                        db_conn.commit()
                        logging.info(f"Committed start boost: calling user: {uid}")

                finally:
                    pool.dispose()

        insert_boost_with_retry()

    except Exception as e:
        logging.exception(e)
        db.collection('SQLExceptions').add(
            {'function': 'redeemBoost', 'user': uid, 'error': str(e), 'time': firestore.SERVER_TIMESTAMP})

    if woman_eligible:
        user_ref.update({"boostedUntil": (datetime.now() + timedelta(hours=2)),
                         "boostedConfirmationShown": False})
    else:
        # set lapsing time in user document for boost, decrease remaining boost in user doc
        user_ref.update({"boostedUntil": (datetime.now()+timedelta(hours=2)), "boostsLeft": firestore.Increment(-1), "boostedConfirmationShown": False})

# REDEEM DIAMOND LIKE: Consumes a diamond like from a user and makes necessary changes to upgrade a like to diamond status
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.MB_512, enforce_app_check=True)
def redeemDiamondLike(req: https_fn.CallableRequest) -> Any:
    """
          Consumes a diamond like from a user and makes necessary changes to upgrade a like to diamond status
          :param req: Callable request incl. user auth information - incl. param "matchId" (string)
          :return: None
    """
    if req.auth.uid == None:
        return

    db = firestore.client()

    uid = req.auth.uid
    user_ref = db.collection("users").document(uid)

    params = req.data
    match_id = params.get("matchId")

    db.collection("likes").document(match_id).update({"diamondLike": True, "evolvedLike": True})
    user_ref.update({"diamondLikesLeft": firestore.Increment(-1)})

# REDEEM FEEDBACK 1 WEEK: Directly grants special entitlement to see feedback for 1 week
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.MB_512, enforce_app_check=True)
def redeemFeedback1week(req: https_fn.CallableRequest) -> Any:
    """
          Directly grants special entitlement to see feedback for 1 week
          :param req: Callable request incl. user auth information - no extra params
          :return: None
    """
    if req.auth.uid == None:
        return

    uid = req.auth.uid
    bearer_token = access_secret_version("Revenuecat_Key")

    time_to_end = datetime.now()+timedelta(7)

    session = create_retry_session()
    grant_promotional_access_rc("feedback_only_1w", time_to_end, uid, bearer_token, session)

# REDEEM READ RECEIPTS MATCH: Adds read receipts for one user to a given match
@https_fn.on_call(region="europe-west2", memory=options.MemoryOption.MB_512, enforce_app_check=True)
def redeemReadReceiptsMatch(req: https_fn.CallableRequest) -> Any:
    """
          Adds read receipts for one user to a given match
          :param req: Callable request incl. user auth information - incl. param "matchRef" (string)
          :return: None
    """
    if req.auth == None:
        return

    uid = req.auth.uid
    match_ref = req.data["matchRef"]
    current_subscriptions = req.data.get("currentSubscriptions", [])

    db = firestore.client()

    user_ref = db.collection("users").document(uid)
    user_doc = user_ref.get()
    user_data = user_doc.to_dict()

    user_gender = user_data.get("gender")

    # determine if user has any read receipts left
    read_receipts_left = user_data.get("readReceiptsLeft", 0)

    if user_gender == "Female" and "divine_access" in current_subscriptions:
        user_ref.update(
            {"matchesWithReadReceipts": firestore.ArrayUnion([match_ref])})
        return

    # if so, add this match to their user doc's eligible matches & decrease remaining quantity
    if read_receipts_left > 0:
        user_ref.update({"readReceiptsLeft": firestore.Increment(-1), "matchesWithReadReceipts": firestore.ArrayUnion([match_ref])})

    # else: return
    return

import time
from datetime import timezone
# SYNC AFTER ONE TIME PURCHASE: Ingests any fresh premium one-time purchases & restocks user limits accordingly
@https_fn.on_call(region="europe-west2", timeout_sec=540, memory=options.MemoryOption.GB_2, cpu=2, enforce_app_check=True)
def syncAfterOneTimePurchase(req: https_fn.CallableRequest) -> Any:
    """
          Ingests any fresh premium one-time purchases & restocks user limits accordingly
          :param req: Callable request incl. user auth information - no extra params
          :return: None
    """

    if req.auth.uid == None:
        return

    def sendSlackMessage(name, gender, subscriptionType, userUID):
        webhook_url = '*********************************************************************************'
        message = {
            'text': req.data.get('text', f"""New one-time purchase made:
                                        Purchase Type: {subscriptionType}
                                        {name}
                                        {gender}
                                        {userUID}""")
        }

        # Send message to Slack
        try:
            response = requests.post(webhook_url, data=json.dumps(message),
                                     headers={'Content-Type': 'application/json'})
            response.raise_for_status()  # Check for errors
        except requests.exceptions.RequestException as error:
            print("Error sending message to Slack", error)

    uid = req.auth.uid

    db = firestore.client()

    logging.info(f"sync after one time purchase called for id: {uid}")

    user_ref = db.collection("users").document(uid)
    user_doc = user_ref.get()
    user_data = user_doc.to_dict()

    name = user_data.get("name")
    gender = user_data.get("gender")

    evolved_likes_ingested = user_data.get("evolvedLikesIngested", [])
    read_receipts_ingested = user_data.get("readReceiptsIngested", [])
    diamond_likes_ingested = user_data.get("diamondLikesIngested", [])
    boost_likes_ingested = user_data.get("boostsIngested", [])
    undos_ingested = user_data.get("undosIngested", [])

    evolved_likes_pname_3 = 'chy_roses_3_241021'
    evolved_likes_pname_12 = 'chy_roses_12_241021'
    evolved_likes_pname_25 = 'chy_roses_25_241021'
    read_receipts_pname_1 = 'chy_readrec_1_241021'
    read_receipts_pname_3 = 'chy_readrec_3_241021'
    read_receipts_pname_5 = 'chy_readrec_5_241021'
    diamond_likes_pname_1 = 'chy_dlikes_1_241021'
    diamond_likes_pname_3 = 'chy_dlikes_3_241021'
    diamond_likes_pname_5 = 'chy_dlikes_5_241021'
    boost_pname_1 = 'chy_boosts_1_241021'
    boost_pname_5 = 'chy_boosts_5_241021'
    boost_pname_10 = 'chy_boosts_10_241021'

    time.sleep(5)

    session = create_retry_session()

    # Your bearer token you've obtained from the authentication process
    bearer_token = access_secret_version("Revenuecat_Key")

    data = get_user_info_rc(uid, bearer_token, session)

    all_non_subscriptions = data["subscriber"].get("non_subscriptions", {})

    all_evolved_likes_3 = all_non_subscriptions.get(evolved_likes_pname_3, [])
    all_evolved_likes_12 = all_non_subscriptions.get(evolved_likes_pname_12, [])
    all_evolved_likes_25 = all_non_subscriptions.get(evolved_likes_pname_25, [])
    all_read_receipts_1 = all_non_subscriptions.get(read_receipts_pname_1, [])
    all_read_receipts_3 = all_non_subscriptions.get(read_receipts_pname_3, [])
    all_read_receipts_5 = all_non_subscriptions.get(read_receipts_pname_5, [])
    all_diamond_likes_1 = all_non_subscriptions.get(diamond_likes_pname_1, [])
    all_diamond_likes_3 = all_non_subscriptions.get(diamond_likes_pname_3, [])
    all_diamond_likes_5 = all_non_subscriptions.get(diamond_likes_pname_5, [])
    all_boosts_1 = all_non_subscriptions.get(boost_pname_1, [])
    all_boosts_5 = all_non_subscriptions.get(boost_pname_5, [])
    all_boosts_10 = all_non_subscriptions.get(boost_pname_10, [])
    all_undos = all_non_subscriptions.get("chy_undos_1_250519", [])


    for u in all_undos:
        transaction_id = u.get("store_transaction_id")
        if transaction_id not in undos_ingested:
            user_ref.update({"undosLeft": firestore.Increment(1),
                             "undosIngested": firestore.ArrayUnion([transaction_id])})

    for a in all_evolved_likes_3:
        transaction_id = a.get("store_transaction_id")
        if transaction_id not in evolved_likes_ingested:
            user_ref.update({"aEvolvedLikesLeft": firestore.Increment(3), "evolvedLikesIngested": firestore.ArrayUnion([transaction_id])})
            try:
                sendSlackMessage(name, gender, "3 Roses", uid)
            except Exception as e:
                logging.exception(e)
    for a in all_evolved_likes_12:
        transaction_id = a.get("store_transaction_id")
        if transaction_id not in evolved_likes_ingested:
            user_ref.update({"aEvolvedLikesLeft": firestore.Increment(12),
                             "evolvedLikesIngested": firestore.ArrayUnion([transaction_id])})
            try:
                sendSlackMessage(name, gender, "12 Roses", uid)
            except Exception as e:
                logging.exception(e)
    for a in all_evolved_likes_25:
        transaction_id = a.get("store_transaction_id")
        if transaction_id not in evolved_likes_ingested:
            user_ref.update({"aEvolvedLikesLeft": firestore.Increment(25),
                             "evolvedLikesIngested": firestore.ArrayUnion([transaction_id])})
            try:
                sendSlackMessage(name, gender, "25 Roses", uid)
            except Exception as e:
                logging.exception(e)

    for a in all_read_receipts_1:
        transaction_id = a.get("store_transaction_id")
        if transaction_id not in read_receipts_ingested:
            user_ref.update({"readReceiptsLeft": firestore.Increment(1), "readReceiptsIngested": firestore.ArrayUnion([transaction_id])})
            try:
                sendSlackMessage(name, gender, "1 Read Receipt", uid)
            except Exception as e:
                logging.exception(e)
    for a in all_read_receipts_3:
        transaction_id = a.get("store_transaction_id")
        if transaction_id not in read_receipts_ingested:
            user_ref.update({"readReceiptsLeft": firestore.Increment(3),
                             "readReceiptsIngested": firestore.ArrayUnion([transaction_id])})
            try:
                sendSlackMessage(name, gender, "3 Read Receipt", uid)
            except Exception as e:
                logging.exception(e)
    for a in all_read_receipts_5:
        transaction_id = a.get("store_transaction_id")
        if transaction_id not in read_receipts_ingested:
            user_ref.update({"readReceiptsLeft": firestore.Increment(5),
                             "readReceiptsIngested": firestore.ArrayUnion([transaction_id])})
            try:
                sendSlackMessage(name, gender, "5 Read Receipt", uid)
            except Exception as e:
                logging.exception(e)

    for a in all_diamond_likes_1:
        transaction_id = a.get("store_transaction_id")
        if transaction_id not in diamond_likes_ingested:
            user_ref.update({"diamondLikesLeft": firestore.Increment(1),
                             "diamondLikesIngested": firestore.ArrayUnion([transaction_id])})
            try:
                sendSlackMessage(name, gender, "1 Diamond Like", uid)
            except Exception as e:
                logging.exception(e)
    for a in all_diamond_likes_3:
        transaction_id = a.get("store_transaction_id")
        if transaction_id not in diamond_likes_ingested:
            user_ref.update({"diamondLikesLeft": firestore.Increment(3),
                             "diamondLikesIngested": firestore.ArrayUnion([transaction_id])})
            try:
                sendSlackMessage(name, gender, "3 Diamond Like", uid)
            except Exception as e:
                logging.exception(e)
    for a in all_diamond_likes_5:
        transaction_id = a.get("store_transaction_id")
        if transaction_id not in diamond_likes_ingested:
            user_ref.update({"diamondLikesLeft": firestore.Increment(5),
                             "diamondLikesIngested": firestore.ArrayUnion([transaction_id])})
            try:
                sendSlackMessage(name, gender, "5 Diamond Like", uid)
            except Exception as e:
                logging.exception(e)


    for a in all_boosts_1:
        transaction_id = a.get("store_transaction_id")
        if transaction_id not in boost_likes_ingested:
            user_ref.update({"boostsLeft": firestore.Increment(1),
                             "boostsIngested": firestore.ArrayUnion([transaction_id])})
            try:
                sendSlackMessage(name, gender, "1 Boost", uid)
            except Exception as e:
                logging.exception(e)
    for a in all_boosts_5:
        transaction_id = a.get("store_transaction_id")
        if transaction_id not in boost_likes_ingested:
            user_ref.update({"boostsLeft": firestore.Increment(5),
                             "boostsIngested": firestore.ArrayUnion([transaction_id])})
            try:
                sendSlackMessage(name, gender, "5 Boosts", uid)
            except Exception as e:
                logging.exception(e)
    for a in all_boosts_10:
        transaction_id = a.get("store_transaction_id")
        if transaction_id not in boost_likes_ingested:
            user_ref.update({"boostsLeft": firestore.Increment(10),
                             "boostsIngested": firestore.ArrayUnion([transaction_id])})
            try:
                sendSlackMessage(name, gender, "10 Boosts", uid)
            except Exception as e:
                logging.exception(e)


def has_divine_access_expired(payload):
    """
    Determines if a subscription expiration involved the 'divine_access' entitlement.

    Args:
        payload (dict): The JSON payload representing the subscription event.

    Returns:
        bool: True if the event is an expiration involving 'divine_access', False otherwise.
    """
    try:
        print(payload)
        event = payload.get("event", {})
        print(f"event {event}")
        event_type = event.get("type")
        print(f"event_type {event_type}")
        product = event.get("product_id", "")
        print(f"product {product}")


        if event_type == "EXPIRATION" and "divine" in product.lower():
            return True
    except Exception as e:
        print(f"Error processing payload: {e}")

    return False


# RECEIVE SUBSCRIPTION INFORMATION: Webhook for Revenue Cat to be called on every purchase event
@https_fn.on_request(cors=options.CorsOptions(cors_origins="*", cors_methods=["post"]), memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def receiveSubscriptionInformation(req: https_fn.Request) -> https_fn.Response:
    """
          Webhook for Revenuecat to be called on every purchase event

          More information from Revenuecat here: https://www.revenuecat.com/docs/integrations/webhooks
          :param req: Callable request data incl. Revenuecat-formatted event data
          :return: None
    """
    if req.headers.get("User-Agent") != "RevenueCat":
        return https_fn.Response("Unauthenticated")

    request_json = req.get_json(silent=True)
    logging.info(request_json)
    user_id = request_json["event"]["app_user_id"]
    divine_access_expired = has_divine_access_expired(request_json)
    logging.info(f"divine access expired: {divine_access_expired}")

    bearer_token = access_secret_version("Revenuecat_Key")

    session = create_retry_session()
    data = get_user_info_rc(user_id, bearer_token, session)

    logging.info(data)

    current_datetime = datetime.now(timezone.utc)

    all_entitlements = data["subscriber"].get("entitlements")

    subscribed_to = []

    if all_entitlements != None:
        for x, sub_dict in all_entitlements.items():
            try:
                validity = sub_dict.get("expires_date", "2029-07-24T18:28:48Z")

                if datetime.fromisoformat(validity)>current_datetime:
                    subscribed_to.append(x)
            except:
                subscribed_to.append(x)


    db = firestore.client()

    @firestore.transactional
    def update_in_transaction(transaction, user_ref):

        user_doc_obj = user_ref.get(transaction=transaction)

        if not user_doc_obj.exists:
            logging.info("User does not exist")
            return


        user_doc = user_doc_obj.to_dict()

        user_on_waitinglist = user_doc.get("onWaitingList", False)
        user_gender = user_doc.get("gender")
        user_on_global_waitinglist = user_doc.get("onGlobalWaitingList", False)

        user_public_profile = user_doc.get("publicProfile")
        user_show_supporter_badge = user_doc.get("supporter_badge_shown", False)

        waiting_list_users = []
        waiting_list_users_g = []

        # get current search premium settings
        user_incognito = user_doc.get("incognito", False)
        user_lowerHeightReq = user_doc.get("lowerHeightReq", 0)
        user_upperHeightReq = user_doc.get("upperHeightReq", 250)
        user_kinksToSee = user_doc.get("kinksToSeeReq", [])
        user_hobbiesToSee = user_doc.get("hobbiesToSeeReq", [])

        if user_on_waitinglist:
            user_wl_region = user_doc.get("wlRegion")
            waiting_list_ref = db.collection("waitingList").document(user_wl_region)
            waiting_list_doc = waiting_list_ref.get(transaction=transaction)
            waiting_list_users = waiting_list_doc.to_dict().get("users", [])

        if user_on_global_waitinglist:
            waiting_list_ref_g = db.collection("waitingList").document("GLOBAL_WL")
            waiting_list_doc_g = waiting_list_ref_g.get(transaction=transaction)
            waiting_list_users_g = waiting_list_doc_g.to_dict().get("users", [])

        # Determine new subscription levels
        new_sublevel = "100"
        if "150_waitinglist_speed" in subscribed_to:
            new_sublevel = "150"
        elif "200_waitinglist_speed" in subscribed_to:
            new_sublevel = "200"
        elif "300_waitinglist_speed" in subscribed_to:
            new_sublevel = "300"

        # Update waiting list users
        if user_on_waitinglist:
            for index, user in enumerate(waiting_list_users):
                if user["reference"] == user_ref:
                    waiting_list_users[index] = {"reference": user_ref, "sublevel": new_sublevel}
                    transaction.update(waiting_list_ref, {"users": waiting_list_users})
                    break

        if user_on_global_waitinglist:
            for index, user in enumerate(waiting_list_users_g):
                if user["reference"] == user_ref:
                    waiting_list_users_g[index] = {"reference": user_ref, "sublevel": new_sublevel}
                    transaction.update(waiting_list_ref_g, {"users": waiting_list_users_g})
                    break



        standard_subscriber = True if (user_gender == "Female" or "standard_access" in subscribed_to) else False
        paid_standard_subscriber = True if ("paid_standard_lifetime" or "paid_standard_1w" in subscribed_to) else False
        evolved_subscriber = True if (("evolved_access" in subscribed_to) or ("divine_access" in subscribed_to)) else False
        plus_subscriber = True if "plus_access" in subscribed_to else False
        gold_subscriber = True if "gold_access" in subscribed_to else False
        waiting_list_subscriber = True if "waitinglist_150" or "waitinglist_200" or "waiting_list_300" in subscribed_to else False
        evolved_likes_weekly_limit = 7 if "evolved_access" in subscribed_to else 2 if "gold_access" in subscribed_to else 0
        supporter_badge = True if (paid_standard_subscriber and user_show_supporter_badge) else False


        if user_gender == 'Female':
            normal_likes_daily_limit = 12 if "evolved_access" in subscribed_to else 6
        else:
            normal_likes_daily_limit = 6 if "evolved_access" in subscribed_to else 3

        update_for_user = {'evolvedSubscriber': evolved_subscriber, 'plusSubscriber': plus_subscriber,
                           'goldSubscriber': gold_subscriber, 'waitingListSubscriber': waiting_list_subscriber,
                           'evolvedLikesWeeklyLimit': evolved_likes_weekly_limit, 'normalLikesDailyLimit': normal_likes_daily_limit,
                           'chyrpeStandardSubscriber': standard_subscriber,
                           "incognito": user_incognito if plus_subscriber or evolved_subscriber or paid_standard_subscriber else False,
                           "lowerHeightReq": user_lowerHeightReq if user_lowerHeightReq is not None and (user_gender == "Female" or evolved_subscriber) else firestore.DELETE_FIELD,
                           "upperHeightReq": user_upperHeightReq if user_upperHeightReq is not None and (user_gender == "Female" or evolved_subscriber) else firestore.DELETE_FIELD,
                           "kinksToSeeReq": user_kinksToSee if user_kinksToSee is not None and (user_gender == "Female" or evolved_subscriber) else firestore.DELETE_FIELD,
                           "hobbiesToSeeReq": user_hobbiesToSee if user_hobbiesToSee is not None and (user_gender == "Female" or evolved_subscriber) else firestore.DELETE_FIELD,
                           "supporter_badge_shown": supporter_badge}

        logging.info(f"{user_ref.id} has entitlements: {all_entitlements}")

        if divine_access_expired:
            update_for_user['currentLikeLimitsId'] = "default"

        transaction.update(user_ref, update_for_user)

        try:
            user_public_profile.update({"supporter_badge_shown": supporter_badge})
        except Exception as e:
            logging.exception(e)

    user_ref = db.collection("users").document(user_id)

    transaction = db.transaction()

    update_in_transaction(transaction, user_ref)

    # for one-time purchases (not standard, not promotional), additionally execute syncAfterOneTimePurchase logic
    if request_json["event"]["type"] == "NON_RENEWING_PURCHASE" and request_json["event"]["entitlement_id"] == None:
        try:
            time.sleep(10)

            user_data = user_ref.get().to_dict()
            uid = user_data.get("uid")
            name = user_data.get("name")
            gender = user_data.get("gender")

            evolved_likes_ingested = user_data.get("evolvedLikesIngested", [])
            read_receipts_ingested = user_data.get("readReceiptsIngested", [])
            diamond_likes_ingested = user_data.get("diamondLikesIngested", [])
            boost_likes_ingested = user_data.get("boostsIngested", [])

            evolved_likes_pname_3 = 'chy_roses_3_241021'
            evolved_likes_pname_12 = 'chy_roses_12_241021'
            evolved_likes_pname_25 = 'chy_roses_25_241021'
            read_receipts_pname_1 = 'chy_readrec_1_241021'
            read_receipts_pname_3 = 'chy_readrec_3_241021'
            read_receipts_pname_5 = 'chy_readrec_5_241021'
            diamond_likes_pname_1 = 'chy_dlikes_1_241021'
            diamond_likes_pname_3 = 'chy_dlikes_3_241021'
            diamond_likes_pname_5 = 'chy_dlikes_5_241021'
            boost_pname_1 = 'chy_boosts_1_241021'
            boost_pname_5 = 'chy_boosts_5_241021'
            boost_pname_10 = 'chy_boosts_10_241021'

            time.sleep(5)

            all_non_subscriptions = data["subscriber"].get("non_subscriptions", {})

            all_evolved_likes_3 = all_non_subscriptions.get(evolved_likes_pname_3, [])
            all_evolved_likes_12 = all_non_subscriptions.get(evolved_likes_pname_12, [])
            all_evolved_likes_25 = all_non_subscriptions.get(evolved_likes_pname_25, [])
            all_read_receipts_1 = all_non_subscriptions.get(read_receipts_pname_1, [])
            all_read_receipts_3 = all_non_subscriptions.get(read_receipts_pname_3, [])
            all_read_receipts_5 = all_non_subscriptions.get(read_receipts_pname_5, [])
            all_diamond_likes_1 = all_non_subscriptions.get(diamond_likes_pname_1, [])
            all_diamond_likes_3 = all_non_subscriptions.get(diamond_likes_pname_3, [])
            all_diamond_likes_5 = all_non_subscriptions.get(diamond_likes_pname_5, [])
            all_boosts_1 = all_non_subscriptions.get(boost_pname_1, [])
            all_boosts_5 = all_non_subscriptions.get(boost_pname_5, [])
            all_boosts_10 = all_non_subscriptions.get(boost_pname_10, [])

            for a in all_evolved_likes_3:
                transaction_id = a.get("store_transaction_id")
                if transaction_id not in evolved_likes_ingested:
                    user_ref.update({"aEvolvedLikesLeft": firestore.Increment(3),
                                     "evolvedLikesIngested": firestore.ArrayUnion([transaction_id])})
            for a in all_evolved_likes_12:
                transaction_id = a.get("store_transaction_id")
                if transaction_id not in evolved_likes_ingested:
                    user_ref.update({"aEvolvedLikesLeft": firestore.Increment(12),
                                     "evolvedLikesIngested": firestore.ArrayUnion([transaction_id])})
            for a in all_evolved_likes_25:
                transaction_id = a.get("store_transaction_id")
                if transaction_id not in evolved_likes_ingested:
                    user_ref.update({"aEvolvedLikesLeft": firestore.Increment(25),
                                     "evolvedLikesIngested": firestore.ArrayUnion([transaction_id])})

            for a in all_read_receipts_1:
                transaction_id = a.get("store_transaction_id")
                if transaction_id not in read_receipts_ingested:
                    user_ref.update({"readReceiptsLeft": firestore.Increment(1),
                                     "readReceiptsIngested": firestore.ArrayUnion([transaction_id])})
            for a in all_read_receipts_3:
                transaction_id = a.get("store_transaction_id")
                if transaction_id not in read_receipts_ingested:
                    user_ref.update({"readReceiptsLeft": firestore.Increment(3),
                                     "readReceiptsIngested": firestore.ArrayUnion([transaction_id])})

            for a in all_read_receipts_5:
                transaction_id = a.get("store_transaction_id")
                if transaction_id not in read_receipts_ingested:
                    user_ref.update({"readReceiptsLeft": firestore.Increment(5),
                                     "readReceiptsIngested": firestore.ArrayUnion([transaction_id])})

            for a in all_diamond_likes_1:
                transaction_id = a.get("store_transaction_id")
                if transaction_id not in diamond_likes_ingested:
                    user_ref.update({"diamondLikesLeft": firestore.Increment(1),
                                     "diamondLikesIngested": firestore.ArrayUnion([transaction_id])})

            for a in all_diamond_likes_3:
                transaction_id = a.get("store_transaction_id")
                if transaction_id not in diamond_likes_ingested:
                    user_ref.update({"diamondLikesLeft": firestore.Increment(3),
                                     "diamondLikesIngested": firestore.ArrayUnion([transaction_id])})

            for a in all_diamond_likes_5:
                transaction_id = a.get("store_transaction_id")
                if transaction_id not in diamond_likes_ingested:
                    user_ref.update({"diamondLikesLeft": firestore.Increment(5),
                                     "diamondLikesIngested": firestore.ArrayUnion([transaction_id])})

            for a in all_boosts_1:
                transaction_id = a.get("store_transaction_id")
                if transaction_id not in boost_likes_ingested:
                    user_ref.update({"boostsLeft": firestore.Increment(1),
                                     "boostsIngested": firestore.ArrayUnion([transaction_id])})

            for a in all_boosts_5:
                transaction_id = a.get("store_transaction_id")
                if transaction_id not in boost_likes_ingested:
                    user_ref.update({"boostsLeft": firestore.Increment(5),
                                     "boostsIngested": firestore.ArrayUnion([transaction_id])})

            for a in all_boosts_10:
                transaction_id = a.get("store_transaction_id")
                if transaction_id not in boost_likes_ingested:
                    user_ref.update({"boostsLeft": firestore.Increment(10),
                                     "boostsIngested": firestore.ArrayUnion([transaction_id])})


        except Exception as e:
            logging.exception(e)

    return https_fn.Response("Success")

@https_fn.on_request(region="europe-west1", cors=options.CorsOptions(cors_origins="*", cors_methods=["post"]), memory=options.MemoryOption.MB_512, timeout_sec=900, cpu=1)
def receiveInformationAmplitude(req: https_fn.Request) -> https_fn.Response:
    auth_header = req.headers.get("Authorization")

    # print(auth_header)
    # print("Started")
    #
    # # Step 2: Check if the header exists and is properly formatted
    # if not auth_header or not auth_header.startswith("Bearer "):
    #     return https_fn.Response(
    #         "Missing or invalid Authorization header", status=401
    #     )
    #
    # # Step 3: Extract and validate the token
    # token = auth_header.split(" ")[1]  # Extract token after "Bearer "
    # if token != access_secret_version("Amplitude_Webhook_Token"):
    #     return https_fn.Response(
    #         "Invalid token", status=403
    #     )
    #
    # print("Authorized")
    #
    # received_data = req.get_json(silent=True)
    # print(received_data)
    #
    # db = firestore.client()
    #
    # cohort_id = received_data.get('cohort_id')
    # users_list = received_data.get('users')
    # today = datetime.now().strftime('%Y%m%d')
    #
    # for u in users_list:
    #     try:
    #         user_id = u.get('user_id')
    #         print(user_id)
    #         db.collection("users").document(user_id).update({"discountEligibleNow": cohort_id, "discountEligibilityDay": today})
    #     except Exception as e:
    #         logging.exception(e)

    return https_fn.Response("Success")


#UNMATCH: Undoes a user's last left swipe on the backend
@https_fn.on_call(region="europe-west1", memory=options.MemoryOption.MB_512)
def undoLeftSwipe(req: https_fn.CallableRequest) -> Any:
    """
         Undoes a user's last left swipe on the backend

         :param req: Data includes "otherUserUID" (string)
         :return: None
      """

    if req.auth.uid == None:
        return

    uid = req.auth.uid
    other_user_id = req.data.get('otherUserUID')
    undo_access_type_is_subscription = req.data.get('undoAccessTypeIsSubscription')

    print(f"UID to undo: {other_user_id}")

    db = firestore.client()

    users_collection = db.collection('users')
    user_reference = users_collection.document(uid)
    user_data = user_reference.get().to_dict()

    all_disliked = user_data.get('passedProfiles')
    matching_suggestions = user_data.get('matchingSuggestions', [])
    matching_suggestions_uids = [m.get('uid') for m in matching_suggestions]

    print(matching_suggestions_uids)

    relevant_disliked = [u for u in all_disliked if u.get('uid') == other_user_id][0]

    if 'date' in relevant_disliked:
        del relevant_disliked['date']

    if other_user_id not in matching_suggestions_uids:
        matching_suggestions.insert(0, relevant_disliked)

    user_reference.update({
        # "undosIngested": firestore.ArrayUnion([transaction_id]),
        "matchingSuggestions": matching_suggestions,
        "undosLeft": firestore.firestore.Increment(-1 if not undo_access_type_is_subscription else 0)})

    return True
